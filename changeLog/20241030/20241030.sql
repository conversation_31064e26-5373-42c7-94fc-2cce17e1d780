alter table investment_file
    drop column bid_summary_contract_code,
    drop column bid_summary_project_code,
    add column relation_file_belong_id varchar(32) null comment '关联文件belongId',
    add column relation_file_type      tinyint     null comment '关联文件类型',
    add column business_type                             varchar(128)   null comment '业务类型',
    add column business_type_code                        varchar(128)   null comment '业务类型code',
    add column plan_total_investment                     DECIMAL(20, 2) null comment '计划总投(亿元)',
    add column self_construction_installation_investment DECIMAL(20, 2) null comment '自建安装投资（亿元）',
    add COLUMN plan_contribution_capital                 DECIMAL(20, 2) NULL COMMENT '计划出资资本（亿元）',
    add column ownership_ratio                           DECIMAL(10, 2) NULL COMMENT '出资比例（%）',
    add column project_type                              varchar(32)    null comment '工程类型(总公司综合口径)',
    add column project_type2                             varchar(32)    null comment '工程类型(总公司综合口径)2',
    add column project_type3                             varchar(32)    null comment '工程类型(总公司综合口径)3',
    add column project_type4                             varchar(32)    null comment '工程类型(总公司综合口径)4',
    add column project_type_code                         varchar(32)    null comment '工程类型code(总公司综合口径)',
    add column project_type2_code                        varchar(32)    null comment '工程类型code(总公司综合口径)2',
    add column project_type3_code                        varchar(32)    null comment '工程类型code(总公司综合口径)3',
    add column project_type4_code                        varchar(32)    null comment '工程类型code(总公司综合口径)4',
    add column standard_type1                            varchar(64)    null comment '局标准分类1',
    add column standard_type2                            varchar(64)    null comment '局标准分类2',
    add column standard_type3                            varchar(64)    null comment '局标准分类3',
    add column standard_type4                            varchar(64)    null comment '局标准分类4',
    add column standard_type1_code                       varchar(32)    null comment '局标准分类Code1',
    add column standard_type2_code                       varchar(32)    null comment '局标准分类Code1',
    add column standard_type3_code                       varchar(32)    null comment '局标准分类Code1',
    add column standard_type4_code                       varchar(32)    null comment '局标准分类Code1';

ALTER table contract
    ADD customer_code         VARCHAR(32) COMMENT '客户编码' AFTER customer_id,
    ADD superior_company_id   VARCHAR(32) COMMENT '上级相关方id' AFTER customer_code,
    ADD business_license_code VARCHAR(127) COMMENT '统一社会信用代码' AFTER superior_company_id;


ALTER table supplementary_agreement
    ADD customer_code         VARCHAR(32) COMMENT '客户编码' AFTER customer_id,
    ADD superior_company_id   VARCHAR(32) COMMENT '上级相关方id' AFTER customer_code,
    ADD business_license_code VARCHAR(127) COMMENT '统一社会信用代码' AFTER superior_company_id;


ALTER table bureau_contract
    ADD customer_code         VARCHAR(32) COMMENT '客户编码' AFTER customer_id,
    ADD superior_company_id   VARCHAR(32) COMMENT '上级相关方id' AFTER customer_code,
    ADD business_license_code VARCHAR(127) COMMENT '统一社会信用代码' AFTER superior_company_id;


ALTER table bureau_supplementary_agreement
    ADD customer_code         VARCHAR(32) COMMENT '客户编码' AFTER customer_id,
    ADD superior_company_id   VARCHAR(32) COMMENT '上级相关方id' AFTER customer_code,
    ADD business_license_code VARCHAR(127) COMMENT '统一社会信用代码' AFTER superior_company_id;


alter table bid_approval
    add dept_create_type TINYINT      null comment '项目部创建类型： 0:挂接项目部； 1: 创建项目部',
    add dept_name        VARCHAR(128) null comment '项目部名称',
    add parent_dept_id   varchar(64)  null comment '项目部上级部门Id',
    add dept_id        varchar(64) null comment '项目部id',
    ADD dept_create_by varchar(64) NULL COMMENT '项目组织创建人';



alter table bid_summary
    add column business_segment_code_path varchar(50) null comment '业务版块codePath';

alter table supplementary_agreement
    add column business_segment_code_path varchar(50) null comment '业务板块codePath';

alter table bureau_contract
    add column business_segment_code_path varchar(50) null comment '业务板块codePath';

alter table bureau_supplementary_agreement
    add column business_segment_code_path varchar(50) null comment '业务板块codePath';

# delete
# from approval_type_step_mapping
# where type_id = 9999
#   and (step_seq = 30 or step_seq = 255);
#
# update approval_type_step_mapping
# set step_no = 255
# where type_id = 9999
#   and step_no = 30;

CREATE INDEX idx_push_system_id_idx USING BTREE ON project_management.project_event_push_record (push_system_id);
CREATE INDEX project_event_push_record_project_id_IDX USING BTREE ON project_management.project_event_push_record (project_id, push_system_id);



update project_event set business_column = '[{"table": "project", "columns": [{"val": null, "desc": "项目标识", "type": "String", "column": "cpm_project_key", "cameCase": null}, {"val": null, "desc": "项目主键", "type": "Long", "column": "id", "camelCase": null}, {"val": null, "desc": "工程名称", "type": "String", "column": "project_name", "camelCase": null}, {"val": null, "desc": "项目地址", "type": "String", "column": "project_address", "camelCase": null}, {"val": null, "desc": "承包模式", "type": "String", "column": "contract_mode", "camelCase": null}, {"val": null, "desc": "项目分类名称", "type": "String", "column": "project_class_name", "camelCase": null}, {"val": null, "desc": "是否生态敏感区项目", "type": "Integer", "column": "is_ecology_sensitive", "camelCase": null}, {"val": null, "desc": "是否边小远散项目", "type": "Integer", "column": "is_edge_small", "camelCase": null}, {"val": null, "desc": "投资主体", "type": "String", "column": "investors", "camelCase": null}, {"val": null, "desc": "合同总金额", "type": "BigDecimal", "column": "contract_amount", "camelCase": null}, {"val": null, "desc": "合同开工日期", "type": "Long", "column": "worker_begin_time", "camelCase": null}, {"val": null, "desc": "合同竣工日期", "type": "Long", "column": "worker_end_time", "camelCase": null}, {"val": null, "desc": "工期奖罚类型", "type": "String", "column": "worker_date_reward_punish", "camelCase": null}, {"val": null, "desc": "工期奖罚条款", "type": "String", "column": "worker_reward_punish_appoint", "camelCase": null}, {"val": null, "desc": "质量奖罚类型", "type": "String", "column": "reward_punish_type", "camelCase": null}, {"val": null, "desc": "质量奖罚条款", "type": "String", "column": "reward_punish_terms", "camelCase": null}, {"val": null, "desc": "客户名称", "type": "String", "column": "customer_name", "camelCase": null}, {"val": null, "desc": "客户编码", "type": "String", "column": "customer_code", "camelCase": null}, {"val": null, "desc": "客户母公司id", "type": "String", "column": "superior_company_id", "camelCase": null}, {"val": null, "desc": "统一社会信用代码", "type": "String", "column": "business_license_code", "camelCase": null}, {"val": null, "desc": "客户母公司", "type": "String", "column": "superior_company_name", "camelCase": null}, {"val": null, "desc": "现场业主代表姓名", "type": "String", "column": "scene_owner_represent_name", "camelCase": null}, {"val": null, "desc": "现场业主代表职务", "type": "String", "column": "scene_owner_represent_duty", "camelCase": null}, {"val": null, "desc": "现场业主代表联系电话", "type": "String", "column": "scene_owner_represent_phone", "camelCase": null}, {"val": null, "desc": "监理单位", "type": "String", "column": "supervisor", "camelCase": null}, {"val": null, "desc": "设计单位", "type": "String", "column": "designer", "camelCase": null}, {"val": null, "desc": "质量要求", "type": "String", "column": "quality_guarantee", "camelCase": null}, {"val": null, "desc": "安全文明施工要求", "type": "String", "column": "safety_requirement", "camelCase": null}, {"val": null, "desc": "项目经理", "type": "String", "column": "project_manager", "camelCase": null}, {"val": null, "desc": "客户企业性质", "type": "String", "column": "enterprise_type", "camelCase": null}, {"val": null, "desc": "客户级别", "type": "String", "column": "customer_level", "camelCase": null}, {"val": null, "desc": "云枢执行单位", "type": "String", "column": "yunshu_execute_unit", "camelCase": null}, {"val": null, "desc": "云枢执行单位code", "type": "String", "column": "yunshu_execute_unit_code", "camelCase": null}, {"val": null, "desc": "云枢执行单位Id", "type": "String", "column": "yunshu_execute_unit_id", "camelCase": null}, {"val": null, "desc": "云枢执行单位id_path", "type": "String", "column": "yunshu_execute_unit_id_path", "camelCase": null}, {"val": null, "desc": "工程类型（国家标准）", "type": "String", "column": "country_project_type", "camelCase": null}, {"val": null, "desc": "工程类型（总公司市场口径）", "type": "String", "column": "market_project_type", "camelCase": null}, {"val": null, "desc": "工程类型(总公司综合口径)", "type": "String", "column": "project_type", "camelCase": null}, {"val": null, "desc": "签约主体", "type": "string", "column": "signed_subject_value", "camelCase": null}, {"val": null, "desc": "签约主体Code", "type": "string", "column": "signed_subject_code", "camelCase": null}]}, {"table": "bid_summary", "columns": [{"val": null, "desc": "中标项目经理", "type": "String", "column": "winning_project_manager", "camelCase": null}, {"val": null, "desc": "执行项目经理", "type": "String", "column": "executive_project_manager", "camelCase": null}]}, {"table": "contract", "columns": [{"val": null, "desc": "政府备案项目经理ddd", "type": "String", "column": "government_manager", "camelCase": null}]}]'
where id = 100001;

update project_event set business_column = '[{"table": "project", "columns": [{"val": null, "desc": "项目标识", "type": "String", "column": "cpm_project_key", "cameCase": null}, {"val": null, "desc": "项目主键", "type": "Long", "column": "id", "camelCase": null}, {"val": null, "desc": "工程名称", "type": "String", "column": "project_name", "camelCase": null}, {"val": null, "desc": "项目地址", "type": "String", "column": "project_address", "camelCase": null}, {"val": null, "desc": "承包模式", "type": "String", "column": "contract_mode", "camelCase": null}, {"val": null, "desc": "项目分类名称", "type": "String", "column": "project_class_name", "camelCase": null}, {"val": null, "desc": "是否生态敏感区项目", "type": "Integer", "column": "is_ecology_sensitive", "camelCase": null}, {"val": null, "desc": "是否边小远散项目", "type": "Integer", "column": "is_edge_small", "camelCase": null}, {"val": null, "desc": "投资主体", "type": "String", "column": "investors", "camelCase": null}, {"val": null, "desc": "合同总金额", "type": "BigDecimal", "column": "contract_amount", "camelCase": null}, {"val": null, "desc": "合同开工日期", "type": "Long", "column": "worker_begin_time", "camelCase": null}, {"val": null, "desc": "合同竣工日期", "type": "Long", "column": "worker_end_time", "camelCase": null}, {"val": null, "desc": "工期奖罚类型", "type": "String", "column": "worker_date_reward_punish", "camelCase": null}, {"val": null, "desc": "工期奖罚条款", "type": "String", "column": "worker_reward_punish_appoint", "camelCase": null}, {"val": null, "desc": "质量奖罚类型", "type": "String", "column": "reward_punish_type", "camelCase": null}, {"val": null, "desc": "质量奖罚条款", "type": "String", "column": "reward_punish_terms", "camelCase": null}, {"val": null, "desc": "客户名称", "type": "String", "column": "customer_name", "camelCase": null}, {"val": null, "desc": "客户编码", "type": "String", "column": "customer_code", "camelCase": null}, {"val": null, "desc": "客户母公司id", "type": "String", "column": "superior_company_id", "camelCase": null}, {"val": null, "desc": "统一社会信用代码", "type": "String", "column": "business_license_code", "camelCase": null}, {"val": null, "desc": "客户母公司", "type": "String", "column": "superior_company_name", "camelCase": null}, {"val": null, "desc": "现场业主代表姓名", "type": "String", "column": "scene_owner_represent_name", "camelCase": null}, {"val": null, "desc": "现场业主代表职务", "type": "String", "column": "scene_owner_represent_duty", "camelCase": null}, {"val": null, "desc": "现场业主代表联系电话", "type": "String", "column": "scene_owner_represent_phone", "camelCase": null}, {"val": null, "desc": "监理单位", "type": "String", "column": "supervisor", "camelCase": null}, {"val": null, "desc": "设计单位", "type": "String", "column": "designer", "camelCase": null}, {"val": null, "desc": "质量要求", "type": "String", "column": "quality_guarantee", "camelCase": null}, {"val": null, "desc": "安全文明施工要求", "type": "String", "column": "safety_requirement", "camelCase": null}, {"val": null, "desc": "项目经理", "type": "String", "column": "project_manager", "camelCase": null}, {"val": null, "desc": "客户企业性质", "type": "String", "column": "enterprise_type", "camelCase": null}, {"val": null, "desc": "客户级别", "type": "String", "column": "customer_level", "camelCase": null}, {"val": null, "desc": "云枢执行单位", "type": "String", "column": "yunshu_execute_unit", "camelCase": null}, {"val": null, "desc": "云枢执行单位code", "type": "String", "column": "yunshu_execute_unit_code", "camelCase": null}, {"val": null, "desc": "云枢执行单位Id", "type": "String", "column": "yunshu_execute_unit_id", "camelCase": null}, {"val": null, "desc": "云枢执行单位id_path", "type": "String", "column": "yunshu_execute_unit_id_path", "camelCase": null}, {"val": null, "desc": "工程类型（国家标准）", "type": "String", "column": "country_project_type", "camelCase": null}, {"val": null, "desc": "工程类型（总公司市场口径）", "type": "String", "column": "market_project_type", "camelCase": null}, {"val": null, "desc": "工程类型(总公司综合口径)", "type": "String", "column": "project_type", "camelCase": null}, {"val": null, "desc": "签约主体", "type": "string", "column": "signed_subject_value", "camelCase": null}, {"val": null, "desc": "签约主体Code", "type": "string", "column": "signed_subject_code", "camelCase": null}]}, {"table": "bid_summary", "columns": [{"val": null, "desc": "中标项目经理", "type": "String", "column": "winning_project_manager", "camelCase": null}, {"val": null, "desc": "执行项目经理", "type": "String", "column": "executive_project_manager", "camelCase": null}]}, {"table": "contract", "columns": [{"val": null, "desc": "政府备案项目经理", "type": "String", "column": "government_manager", "camelCase": null}]}]'
where id = 100002;

ALTER TABLE project_management.bid_approval
    ADD dept_create_by varchar(64) NULL COMMENT '项目组织创建人';
