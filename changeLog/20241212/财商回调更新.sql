ALTER TABLE project_management.project
    MODIFY COLUMN engineer_parameter text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '工程参数json';
ALTER TABLE project_management.project
    MODIFY COLUMN project_remark TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '项目备注';



ALTER TABLE project_management.project
    ADD finance_business_segment_code_path varchar(64) NULL COMMENT '财商业务板块codePath';
ALTER TABLE project_management.project
    ADD cpm_business_segment_code_path varchar(64) NULL COMMENT '项目中心业务板块(财商立项前取市场，立项后取财商)';
ALTER TABLE project_management.project
    ADD finance_project_source TINYINT UNSIGNED NULL COMMENT '财商承包模式：11 - 外部总承包/独立承包 12 - 外部总承包/联合体 2 - 外部分包 3 - 内部总承包 4 - 内部分包';
ALTER TABLE project_management.project
    ADD finance_business_segment varchar(80) NULL COMMENT '财商业务板块';
ALTER TABLE project_management.project
    ADD cpm_business_segment varchar(100) NULL COMMENT '项目中心业务板块(财商立项前取市场数据，立项后取财商数据)';

ALTER TABLE project_management.project
    CHANGE business_segment_code_path marketing_business_segment_code_path varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '市场业务板块';

ALTER TABLE project_management.project
    CHANGE financial_business_segment marketing_business_segment varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '市场业务板块';

update project
set cpm_business_segment               = marketing_business_segment,
    cpm_business_segment_code_path     = marketing_business_segment_code_path,
    finance_business_segment           = marketing_business_segment,
    finance_business_segment_code_path = marketing_business_segment_code_path
