-- project_management.pending_task_msg_config definition

CREATE TABLE `pending_task_msg_config`
(
    `id`                 bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `type_code`          varchar(64)         NOT NULL COMMENT '业务场景类型',
    `org_id`             varchar(64)         DEFAULT NULL COMMENT '组织id',
    `uc_msg_config_code` varchar(64)         NOT NULL COMMENT 'uc平台的消息配置编码',
    `payload`            json                NOT NULL COMMENT '消息体变量json',
    `user_chose_type`    tinyint(4)          DEFAULT '1' COMMENT '选人类型：1: 组织角色； 2:组织',
    `target_users`       json                NOT NULL COMMENT '目标用户',
    `app_link`           varchar(512)        DEFAULT NULL COMMENT 'appLink',
    `web_link`           varchar(512)        DEFAULT NULL COMMENT 'weblink',
    `billType`           varchar(100)        DEFAULT NULL COMMENT '业务类型编码',
    `bill_type_name`     varchar(100)        DEFAULT NULL COMMENT '业务类型名称',
    `retry_cycle`        varchar(64)         DEFAULT 'R/P1D' COMMENT '通知周期，默认1天',
    `deleted`            bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:否; 1:是;',
    `create_at`          bigint(20) unsigned DEFAULT NULL COMMENT '创建时间',
    `create_by`          varchar(64)         DEFAULT NULL COMMENT '创建人',
    `update_at`          bigint(20) unsigned DEFAULT NULL COMMENT '更新人',
    `update_by`          varchar(64)         DEFAULT NULL COMMENT '更新人',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1876820296730468355
  DEFAULT CHARSET = utf8mb4 COMMENT ='待办任务与消息通知配置';


-- project_management.u_remind_job definition

CREATE TABLE project_management.u_remind_job
(
    `id`            bigint(20) unsigned NOT NULL COMMENT 'id',
    `task_code`     varchar(64)         NOT NULL COMMENT '待办任务code',
    `next_run_time` datetime            NOT NULL COMMENT '下次运行时间',
    `scheduled_id`  varchar(128)                 DEFAULT NULL COMMENT '定时任务id',
    `state`         tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否已执行 1:j是; 0: 否',
    `create_at`     bigint(20) unsigned NOT NULL COMMENT '创建时间',
    `update_at`     bigint(20) unsigned          DEFAULT NULL COMMENT '更新时间',
    `deleted`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '删除状态: 0正常; 时间戳:删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `u_remind_job_unique` (`task_code`, `next_run_time`),
    KEY `u_remind_job_task_code_IDX` (`task_code`, `next_run_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='催办记录表';

alter table project_management.u_todo_task
    add bill_type_name VARCHAR(128) COMMENT '单据名称' AFTER bill_type


-- project_management.smart_project_hook_info definition

CREATE TABLE `smart_project_hook_info`
(
    `id`                  bigint(20) unsigned NOT NULL COMMENT 'id',
    `current_project_id`  bigint(20) unsigned DEFAULT NULL COMMENT '当前项目id',
    `current_project_key` varchar(16)         DEFAULT NULL COMMENT '当前项目标识',
    `hook_yunshu_org_id`  varchar(64)         DEFAULT NULL COMMENT '主项目云枢id',
    `hook_time`           bigint(20) unsigned DEFAULT NULL COMMENT '挂接时间',
    `hook_user_code`      varchar(16)         DEFAULT NULL COMMENT '挂接操作人',
    `hook_project_type`   varchar(32)         DEFAULT NULL COMMENT '挂接项目类型(创建项目部用)',
    `deleted`             bigint(20) unsigned DEFAULT '0' COMMENT '是否删除 0:否; 1:是;',
    `create_at`           bigint(20) unsigned DEFAULT NULL COMMENT '创建时间',
    `create_by`           varchar(64)         DEFAULT NULL COMMENT '创建人',
    `update_at`           bigint(20) unsigned DEFAULT NULL COMMENT '更新人',
    `update_by`           varchar(64)         DEFAULT NULL COMMENT '更新人',
    `remark`              varchar(512)        DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='智慧工地项目挂接信息';

