alter table project
    add column country_project_type_code      varchar(32)  null comment '工程类型Code（国家标准）',
    add column market_project_type_code       varchar(64)  null comment '工程类型code（总公司市场口径）',
    add column project_type_code              varchar(128) null comment '工程类型code(总公司综合口径)',
    add column contract_mode_code             varchar(64)  null comment '工程承包模式Code',
    add column investors_code                 varchar(32)  null comment '投资主体Code',
    add column business_type_code             varchar(32)  null comment '业务类型Code',
    add column project_level_code             varchar(32)  null comment '项目级别Code',
    add column customer_level_code            varchar(32)  null comment '客户级别Code',
    add column enterprise_type_code           varchar(32)  null comment '客户企业性质Code',
    add column do_unit_code                   varchar(32)  null comment '施工单位Code',
    add column worker_date_reward_punish_code varchar(32)  null comment '施工单位奖惩Code',
    add column payment_type_code              varchar(32)  null comment '进度款付款方式Code',
    add column quality_award_type_code        varchar(23)  null comment '质量奖罚类型Code',
    ADD column innovative_business_type_code varchar(128) NULL COMMENT '创新业务分类code',
    ADD column customer_id varchar(32) null comment '客户ID',
    modify column standard_type_code_path varchar(128) null comment '局标准分类codePath';

alter table bid_summary
    add column country_project_type_code      varchar(32) null comment '工程类型Code（国家标准）',
    add column market_project_type_code       varchar(32) null comment '工程类型code（总公司市场口径）',
    add column market_project_type2_code      varchar(32) null comment '工程类型code（总公司市场口径）2',
    add column project_type_code              varchar(32) null comment '工程类型code(总公司综合口径)',
    add column project_type2_code             varchar(32) null comment '工程类型code(总公司综合口径)2',
    add column project_type3_code             varchar(32) null comment '工程类型code(总公司综合口径)3',
    add column project_type4_code             varchar(32) null comment '工程类型code(总公司综合口径)4',
    add column contract_mode1_code            varchar(32) null comment '工程承包模式Code',
    add column contract_mode2_code            varchar(32) null comment '工程承包模式Code',
    add column investors_code                 varchar(32) null comment '投资主体Code',
    add column business_type_code             varchar(32) null comment '业务类型Code',
    add column customer_level_code            varchar(32) null comment '客户级别Code',


    add column enterprise_type_code           varchar(32) null comment '客户企业性质Code',
    add column payment_type_code              varchar(32) null comment '进度款付款方式Code',
    add column quality_award_type_code        varchar(23) null comment '质量奖罚类型Code',
    add column standard_type1_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type2_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type3_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type4_code            varchar(32) null comment '局标准分类Code1',
    add column innovative_business_type_code  varchar(32) null comment '创新业务分类code',
    add column innovative_business_type2_code varchar(32) null comment '创新业务分类2code',
    add column innovative_business_type3_code varchar(32) null comment '创新业务分类3code',
    ADD column customer_id                    varchar(32) null comment '客户ID';

alter table contract
    add column country_project_type_code varchar(32) null comment '工程类型Code（国家标准）',
    add column market_project_type_code  varchar(32) null comment '工程类型code（总公司市场口径）',
    add column market_project_type2_code varchar(32) null comment '工程类型code（总公司市场口径）2',
    add column project_type_code         varchar(32) null comment '工程类型code(总公司综合口径)',
    add column project_type2_code        varchar(32) null comment '工程类型code(总公司综合口径)2',
    add column project_type3_code        varchar(32) null comment '工程类型code(总公司综合口径)3',
    add column project_type4_code        varchar(32) null comment '工程类型code(总公司综合口径)4',
    add column contract_mode1_code       varchar(32) null comment '工程承包模式Code',
    add column contract_mode2_code       varchar(32) null comment '工程承包模式Code',
    add column investors_code            varchar(32) null comment '投资主体Code',
    add column business_type_code        varchar(32) null comment '业务类型Code',
    add column customer_level_code       varchar(32) null comment '客户级别Code',
    add column enterprise_type_code      varchar(32) null comment '客户企业性质Code',
    add column advances_way_code       varchar(32) null comment '进度款付款方式Code',
    add column reward_punish_type_code varchar(23) null comment '质量奖罚类型Code',
    ADD column customer_id             varchar(32) null comment '客户ID';


alter table supplementary_agreement
    add column country_project_type_code      varchar(32) null comment '工程类型Code（国家标准）',
    add column market_project_type_code       varchar(32) null comment '工程类型code（总公司市场口径）',
    add column market_project_type2_code      varchar(32) null comment '工程类型code（总公司市场口径）2',
    add column project_type_code              varchar(32) null comment '工程类型code(总公司综合口径)',
    add column project_type2_code             varchar(32) null comment '工程类型code(总公司综合口径)2',
    add column project_type3_code             varchar(32) null comment '工程类型code(总公司综合口径)3',
    add column project_type4_code             varchar(32) null comment '工程类型code(总公司综合口径)4',
    add column contract_mode1_code            varchar(32) null comment '工程承包模式Code',
    add column contract_mode2_code            varchar(32) null comment '工程承包模式Code',
    add column investors_code                 varchar(32) null comment '投资主体Code',
    add column business_type_code             varchar(32) null comment '业务类型Code',
    add column customer_level_code            varchar(32) null comment '客户级别Code',
    add column enterprise_type_code           varchar(32) null comment '客户企业性质Code',
    add column advances_way_code              varchar(32) null comment '进度款付款方式Code',
    add column reward_punish_type_code        varchar(23) null comment '质量奖罚类型Code',
    add column standard_type1_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type2_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type3_code            varchar(32) null comment '局标准分类Code1',
    add column standard_type4_code            varchar(32) null comment '局标准分类Code1',
    add column innovative_business_type_code  varchar(32) null comment '创新业务分类code',
    add column innovative_business_type2_code varchar(32) null comment '创新业务分类2code',
    add column innovative_business_type3_code varchar(32) null comment '创新业务分类3code',
    ADD column customer_id                    varchar(32) null comment '客户ID';



alter table bureau_contract
    add column country_project_type_code varchar(32) null comment '工程类型Code（国家标准）',
    add column market_project_type_code  varchar(32) null comment '工程类型code（总公司市场口径）',
    add column market_project_type2_code varchar(32) null comment '工程类型code（总公司市场口径）2',
    add column project_type_code         varchar(32) null comment '工程类型code(总公司综合口径)',
    add column project_type2_code        varchar(32) null comment '工程类型code(总公司综合口径)2',
    add column project_type3_code        varchar(32) null comment '工程类型code(总公司综合口径)3',
    add column project_type4_code        varchar(32) null comment '工程类型code(总公司综合口径)4',
    add column contract_mode1_code       varchar(32) null comment '工程承包模式Code',
    add column contract_mode2_code       varchar(32) null comment '工程承包模式Code',
    add column business_type_code        varchar(32) null comment '业务类型Code',
    add column customer_level_code       varchar(32) null comment '客户级别Code',
    add column enterprise_type_code      varchar(32) null comment '客户企业性质Code',
    add column advances_way_code       varchar(32) null comment '进度款付款方式Code',
    add column reward_punish_type_code varchar(23) null comment '质量奖罚类型Code',
    ADD column customer_id             varchar(32) null comment '客户ID';


alter table bureau_supplementary_agreement
    add column business_type_code   varchar(32) null comment '业务类型Code',
    add column customer_level_code  varchar(32) null comment '客户级别Code',
    add column enterprise_type_code varchar(32) null comment '客户企业性质Code',
    ADD column customer_id          varchar(32) null comment '客户ID';


create table platform_event_log
(
    id               bigint unsigned auto_increment comment '主键id',
    msg_id           varchar(64)     not null comment '消息id',
    msg_type         varchar(32)     not null comment '消息类型 POST: 新增, PUT： 更新, DELETE： 删除',
    msg_body         text            not null comment '请求消息内容',
    decode_content text default null comment '解码后的内容',
    receive_time     bigint unsigned not null comment '接收时间',
    operation_record json default null comment '业务操作记录',
    response_result  varchar(256)    default null comment '响应结果',
    response_time    bigint unsigned default null comment '响应时间',
    create_at        bigint unsigned not null comment '创建时间',
    primary key (id),
    key idx_msg_id (msg_id),
    key idx_msg_type (msg_id, msg_type)
) engine = InnoDB comment '平台事件日志表';


alter table yunshu_org_sync
    add column tree_name varchar(128) default null comment '组织树名称';

ALTER TABLE project_management.bureau_supplementary_agreement
    ADD advances_way_code varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '进度款付款方式Code';

ALTER TABLE project
    ADD deleted BIGINT UNSIGNED DEFAULT 0 NULL COMMENT '是否删除：0： 未删除； 时间戳为删除时间';

alter table business_system_data_approval_form
    modify column target_field_content varchar(2048) default '*' not null comment '字段目标值';
alter table business_system_data_approval_form
    modify column raw_field_content varchar(2048) default '*' null comment '字段原始值';
alter table business_system_data_approval_form
    modify column current_field_content varchar(2048) DEFAULT '' NULL COMMENT '主数据表中当前字段的值';