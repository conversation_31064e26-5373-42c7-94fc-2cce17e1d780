ALTER TABLE sys_dict_data
    ADD `parent_code` bigint(20) unsigned default 0 NULL COMMENT '上级code',
    ADD `level`       int(11)                       NOT NULL DEFAULT '0' COMMENT '层级';

## 删除sys_dict_data 表中 id 大于134的数据
DELETE
FROM sys_dict_data
WHERE dict_code > 200;

DELETE
FROM sys_dict_type
WHERE dict_type = 'signed_bid_subject';

ALTER TABLE bureau_supplementary_agreement
    ADD signed_subject_value varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体value',
    ADD signed_subject_code  varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签约主体code';

update sys_dict_data
set `is_default` = null;

ALTER TABLE sys_dict_data
    MODIFY COLUMN `is_default` TINYINT UNSIGNED DEFAULT 0 NULL COMMENT '是否默认（1是 0否）';

update sys_dict_data
set `is_default` = 0