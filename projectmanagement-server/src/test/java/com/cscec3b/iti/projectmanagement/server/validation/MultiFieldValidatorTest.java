package com.cscec3b.iti.projectmanagement.server.validation;

import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringMappingArchiveReq;
import org.junit.Before;
import org.junit.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Arrays;
import java.util.Collections;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * 多字段校验器测试
 * 
 * <AUTHOR>
 * @date 2024/12/09
 */
public class MultiFieldValidatorTest {

    private Validator validator;

    @Before
    public void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    public void testValidRequest_OnlyCpmProjectKeys() {
        // 测试只传入 cpmProjectKeys 的情况
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Arrays.asList("project1", "project2"));
        req.setProjectIds(Collections.emptyList());

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertTrue("只传入cpmProjectKeys应该通过校验", violations.isEmpty());
    }

    @Test
    public void testValidRequest_OnlyProjectIds() {
        // 测试只传入 projectIds 的情况
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Collections.emptyList());
        req.setProjectIds(Arrays.asList(1L, 2L));

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertTrue("只传入projectIds应该通过校验", violations.isEmpty());
    }

    @Test
    public void testInvalidRequest_BothEmpty() {
        // 测试两个字段都为空的情况
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Collections.emptyList());
        req.setProjectIds(Collections.emptyList());

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertFalse("两个字段都为空应该校验失败", violations.isEmpty());
        
        boolean foundExpectedMessage = violations.stream()
                .anyMatch(v -> v.getMessage().contains("cpmProjectKeys和projectIds不能同时为空"));
        assertTrue("应该包含预期的错误消息", foundExpectedMessage);
    }

    @Test
    public void testInvalidRequest_BothNotEmpty() {
        // 测试两个字段都不为空的情况
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Arrays.asList("project1"));
        req.setProjectIds(Arrays.asList(1L));

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertFalse("两个字段都不为空应该校验失败", violations.isEmpty());
        
        boolean foundExpectedMessage = violations.stream()
                .anyMatch(v -> v.getMessage().contains("cpmProjectKeys和projectIds不能同时传入"));
        assertTrue("应该包含预期的错误消息", foundExpectedMessage);
    }

    @Test
    public void testValidRequest_NullLists() {
        // 测试 null 列表的情况（应该被初始化为空列表）
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Arrays.asList("project1"));
        // projectIds 保持默认的空列表

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertTrue("只传入cpmProjectKeys（projectIds为默认空列表）应该通过校验", violations.isEmpty());
    }

    @Test
    public void testEdgeCases() {
        // 测试边界情况：空字符串
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Arrays.asList(""));  // 包含空字符串
        req.setProjectIds(Collections.emptyList());

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        // 注意：这里的校验只检查列表是否为空，不检查列表内容
        assertTrue("包含空字符串的列表仍然被认为是非空列表", violations.isEmpty());
    }

    @Test
    public void testMultipleViolations() {
        // 测试可能产生多个校验错误的情况
        OpenEngineeringMappingArchiveReq req = new OpenEngineeringMappingArchiveReq();
        req.setCpmProjectKeys(Arrays.asList("project1", "project2"));
        req.setProjectIds(Arrays.asList(1L, 2L, 3L));

        Set<ConstraintViolation<OpenEngineeringMappingArchiveReq>> violations = validator.validate(req);
        assertFalse("两个字段都不为空应该校验失败", violations.isEmpty());
        
        // 应该只有一个校验错误（不能同时传入）
        assertEquals("应该只有一个校验错误", 1, violations.size());
        
        ConstraintViolation<OpenEngineeringMappingArchiveReq> violation = violations.iterator().next();
        assertTrue("错误消息应该包含'不能同时传入'", 
                violation.getMessage().contains("cpmProjectKeys和projectIds不能同时传入"));
    }
}
