package com.cscec3b.iti.projectmanagement.server.scheduled;

import com.cscec.data.http.DictionaryAPIHttp;
import com.cscec3b.iti.projectmanagement.server.service.SysDictDataService;
import com.cscec3b.iti.projectmanagement.server.service.SysDictTypeService;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


public class SyncMarketDictScheduledTest {


    @InjectMocks
    private SyncMarketDictScheduled syncMarketDictScheduled;

    @Mock
    private DictionaryAPIHttp dictionaryAPIHttp;

    @Mock
    private SysDictTypeService dictTypeService;

    @Mock
    private SysDictDataService dictDataService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(syncMarketDictScheduled);
    }

    @Test
    public void syncMarketDict() throws InterruptedException {
        syncMarketDictScheduled.syncMarketDictAll();
    }
}