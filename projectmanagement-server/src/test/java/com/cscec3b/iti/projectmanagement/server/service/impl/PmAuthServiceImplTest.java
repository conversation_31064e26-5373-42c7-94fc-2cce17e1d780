//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.when;
//
//import java.lang.reflect.Method;
//import java.util.ArrayList;
//import java.util.Date;
//
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.reflect.Whitebox;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//
//import com.cscec3b.iti.common.base.api.GenericityResponse;
//import com.cscec3b.iti.common.web.exception.BusinessException;
//import com.cscec3b.iti.projectmanagement.api.dto.dto.CurrentOrg;
//import com.cscec3b.iti.projectmanagement.api.dto.response.PmUserInfoResp;
//import com.cscec3b.iti.projectmanagement.server.constant.PmAuthConstants;
//import com.cscec3b.iti.projectmanagement.server.feign.IAppStdOrgService;
//import com.cscec3b.iti.projectmanagement.server.util.UserUtil;
//import com.cscec3b.iti.usercenter.sdk.api.extendapp.dto.resp.oauth.AppAuthRes;
//import com.cscec3b.iti.usercenter.sdk.api.stdorganization.response.EntityOrgInfoMappingInfoRes;
//import com.cscec3b.iti.usercenter.sdk.api.usercenter.dto.response.UserDetailOrgRes;
//import com.cscec3b.iti.usercenter.sdk.api.usercenter.dto.response.UserDetailRes;
//import com.cscec3b.iti.usercenter.server.apiauth.service.IAuthService;
//import com.cscec3b.iti.usercenter.server.appcofig.UcApiProperties;
//import com.cscec3b.iti.usercenter.server.client.entity.SdkTokenExtends;
//import com.cscec3b.iti.usercenter.server.utils.AuthUtil;
//
//import cn.hutool.core.lang.UUID;
//import cn.hutool.json.JSONUtil;
//
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({PmAuthServiceImpl.class, AuthUtil.class, UserUtil.class, JSONUtil.class})
//public class PmAuthServiceImplTest {
//
//    @Mock
//    private IAuthService authService;
//
//    @Mock
//    private StringRedisTemplate stringRedisTemplate;
//
//    @Mock
//    private IAppStdOrgService stdOrgService;
//
//    @Mock
//    private ValueOperations valueOperations;
//
//    @Mock
//    private UcApiProperties apiProperties;
//
//    @InjectMocks
//    PmAuthServiceImpl pmAuthService;
//
//    @Before
//    public void setUp() {
//        PowerMockito.mockStatic(AuthUtil.class);
//        PowerMockito.mockStatic(UserUtil.class);
//        // PowerMockito.mockStatic(BeanUtil.class);
//        PowerMockito.mockStatic(JSONUtil.class);
//        when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
//        pmAuthService = PowerMockito.spy(pmAuthService);
//        PowerMockito.doNothing().when(valueOperations).set(Mockito.any(), Mockito.any(), Mockito.anyLong(),
//            Mockito.any());
//
//    }
//
//    @Test
//    public void loginUrl() {
//        // clientId is null
//        String url = "http://www.baidu.com";
//        PowerMockito.doReturn(url).when(authService).authUrl(Mockito.any());
//        Assert.assertEquals(url, pmAuthService.loginUrl());
//    }
//
//    @Test
//    public void state() throws Exception {
//        String state = UUID.randomUUID(true).toString(true);
//        // callback is null
//        // PowerMockito.mockStatic(UUID.class);
//        // UUID mock = new UUID(1, 2);
//        // PowerMockito.doReturn(mock).when(UUID.class,"randomUUID",true);
//        // mock = PowerMockito.mock(UUID.class);
//        // PowerMockito.doReturn(state).when(mock,"toString",true);
//
//        Assert.assertNotNull(pmAuthService.state(""));
//
//        // callback is not null
//        String callback = "http://www.baidu.com";
//        Assert.assertNotNull(pmAuthService.state(callback));
//
//    }
//
//    @Test
//    public void authorize() throws Exception {
//        Date date = new Date();
//        SdkTokenExtends claims = new SdkTokenExtends();
//        claims.setJti("jti11111");
//        claims.setExp(date.getTime());
//        AppAuthRes appAuthRes = new AppAuthRes().setAccessToken("accessToken").setRefreshToken("refreshToken");
//        PowerMockito.when(authService.authorize(Mockito.any(), Mockito.any())).thenReturn(appAuthRes);
//
//        PowerMockito.when(AuthUtil.class, "parseToken", any()).thenReturn(claims);
//
//        // no exception
//        Assert.assertSame(appAuthRes, pmAuthService.authorize("code", "state"));
//
//        // exception
//        claims.setExp(null);
//        BusinessException exception =
//            Assert.assertThrows(BusinessException.class, () -> pmAuthService.authorize("code", "state"));
//        Assert.assertEquals(8010101, exception.getStatus());
//    }
//
//    @Test
//    public void getUserDetails() throws Exception {
//        String token = "token";
//        PowerMockito.when(AuthUtil.class, "getRequestHeader", any()).thenReturn(token);
//        PmUserInfoResp userDetailRes = new PmUserInfoResp().setUsername("cpm001");
//        PowerMockito.doReturn(userDetailRes).when(pmAuthService, "getUserInfo", Mockito.any());
//        PowerMockito.doNothing().when(pmAuthService, "setCurrentOrgInfo", Mockito.any(), Mockito.any());
//        PowerMockito.doNothing().when(pmAuthService, "cacheUserInfo", Mockito.any(), Mockito.any());
//        Assert.assertSame(userDetailRes, pmAuthService.getUserDetails());
//    }
//
//    @Test
//    public void testGetUserDetails() throws Exception {
//        PmUserInfoResp userDetailRes = new PmUserInfoResp().setUsername("cpm001");
//        PowerMockito.doReturn(userDetailRes).when(pmAuthService, "getUserInfo", Mockito.any());
//        String leanBuildOrgId = "leanBuildOrgId";
//        PowerMockito.when(UserUtil.class, "curLeanBuildOrgId").thenReturn(leanBuildOrgId);
//        PowerMockito.doNothing().when(pmAuthService, "setCurrentOrgInfo", Mockito.any(), Mockito.any());
//        PowerMockito.doNothing().when(pmAuthService, "cacheUserInfo", Mockito.any(), Mockito.any());
//        Assert.assertSame(userDetailRes, pmAuthService.getUserDetails("token"));
//    }
//
//    @Test
//    public void getUserInfo() throws Exception {
//        UserDetailRes userDetails = new UserDetailRes().setUsername("cpm001");
//        PmUserInfoResp userDetailRes = new PmUserInfoResp().setUsername("cpm001");
//        PowerMockito.when(authService.getUserDetails("token")).thenReturn(userDetails);
//        Assert.assertEquals(userDetailRes.getUsername(), pmAuthService.getUserInfo("token").getUsername());
//    }
//
//    @Test
//    public void getStdOrgByLeanBuildOrg() {
//        GenericityResponse<EntityOrgInfoMappingInfoRes> response = new GenericityResponse<>();
//        response.setData(new EntityOrgInfoMappingInfoRes().setStdOrgName("name"));
//        PowerMockito.when(stdOrgService.getEntityMappingInfoByUuid(Mockito.anyString())).thenReturn(response);
//
//        // rest exception
//        response.setStatus(1);
//        Assert.assertThrows(BusinessException.class, () -> pmAuthService.getStdOrgByLeanBuildOrg("code"));
//
//        // stdOrgCode is null
//        response.setStatus(0);
//        EntityOrgInfoMappingInfoRes data = response.getData();
//        Assert.assertThrows(BusinessException.class, () -> pmAuthService.getStdOrgByLeanBuildOrg("code"));
//
//        // ok
//        data.setStdOrgCode("stdOrgCode");
//        Assert.assertSame(data, pmAuthService.getStdOrgByLeanBuildOrg("code"));
//    }
//
//    @Test
//    public void refreshToken() throws Exception {
//        String token = "token";
//        PowerMockito.doReturn(token).when(AuthUtil.class, "getToken");
//        AppAuthRes appAuthRes = new AppAuthRes().setAccessToken(token);
//        PowerMockito.when(authService.refreshToken(token)).thenReturn(appAuthRes);
//        Assert.assertSame(appAuthRes, pmAuthService.refreshToken());
//    }
//
//    @Test
//    public void logout() throws Exception {
//        String token = "token";
//        PowerMockito.doReturn(token).when(AuthUtil.class, "getToken");
//        PowerMockito.when(authService.logout(token)).thenReturn(true);
//        Assert.assertTrue(pmAuthService.logout());
//    }
//
//    @Test
//    public void changeOrg() throws Exception {
//        String token = "token";
//        PowerMockito.doReturn(token).when(AuthUtil.class, "getRequestHeader", Mockito.any());
//        PmUserInfoResp userDetailRes = new PmUserInfoResp().setUsername("cpm001");
//        PowerMockito.doReturn(userDetailRes).when(pmAuthService, "getUserInfo", Mockito.any());
//        PowerMockito.doNothing().when(pmAuthService, "setCurrentOrgInfo", Mockito.any(), Mockito.any());
//        PowerMockito.doNothing().when(pmAuthService, "cacheUserInfo", Mockito.any(), Mockito.any());
//        Assert.assertNotNull(pmAuthService.changeOrg("orgId"));
//    }
//
//    @Test
//    public void cacheUserInfo() throws Exception {
//        SdkTokenExtends claims = new SdkTokenExtends();
//        claims.setJti("jti11111");
//        claims.setExp(new Date().getTime());
//        PowerMockito.doReturn(claims).when(AuthUtil.class, "parseToken", Mockito.anyString());
//        PowerMockito.doReturn("jsonStr").when(JSONUtil.class, "toJsonStr", Mockito.any());
//        //PowerMockito.when(pmAuthService, "parseToken", Mockito.anyString()).thenReturn(claims);
//        Method method = PmAuthServiceImpl.class.getDeclaredMethod("cacheUserInfo", String.class, PmUserInfoResp
//        .class);
//        method.setAccessible(true);
//        PowerMockito.doNothing().when(valueOperations).set(Mockito.any(), Mockito.any(), Mockito.anyLong(), Mockito
//        .any());
//        method.invoke(pmAuthService,"token",new PmUserInfoResp());
//
//    }
//
//    /**
//     * 测试私有方法<br>
//     * 1.  method = PowerMockito.method(PmAuthServiceImpl.class, "setCurrentOrgInfo", PmUserInfoResp.class, String
//     .class);
//     *         Object say = method.invoke(controller, "hi");<br>
//     * 2. Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, "uuid2")
//     *
//     * @throws Exception
//     */
//    @Test
//    public void setCurrentOrgInfo() throws Exception {
//        //Method method = PmAuthServiceImpl.class.getDeclaredMethod("setCurrentOrgInfo", PmUserInfoResp.class,
//        String.class);
//        //method.setAccessible(true);
//        PmUserInfoResp pmUserInfoResp = new PmUserInfoResp();
//        ArrayList<UserDetailOrgRes> list = new ArrayList<>();
//        pmUserInfoResp.setOrgList(list);
//
//        // 精准建造组织 为空 -> org.getOrgPath() is null and  !org.getOrgPath().contains(PmAuthConstants.LEAN_BUILD_ROOT_PATH)
//        BusinessException exception = Assert.assertThrows(BusinessException.class, () -> Whitebox.invokeMethod
//        (pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, "uuid2"));
//        Assert.assertEquals(8010102, exception.getStatus());
//
//        // 精准建造组织为空 -> org.getOrgPath() not null and  !org.getOrgPath().contains(PmAuthConstants.LEAN_BUILD_ROOT_PATH)
//        new UserDetailOrgRes().setOrgPath("/测试");
//        list.add(new UserDetailOrgRes().setOrgPath("/测试"));
//        BusinessException exception1 = Assert.assertThrows(BusinessException.class, () -> Whitebox.invokeMethod
//        (pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, "uuid2"));
//        Assert.assertEquals(8010102, exception1.getStatus());
//
//        // 未匹配到目标组织信息 targetOrgId is not blank
//        UserDetailOrgRes orgRes = new UserDetailOrgRes().setOrgId("UUID").setOrgUuid("UUID").setOrgPath
//        (PmAuthConstants.LEAN_BUILD_ROOT_PATH + "/组织");
//        list.add(orgRes);
//        BusinessException exception2 = Assert.assertThrows(BusinessException.class, () -> Whitebox.invokeMethod
//        (pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, "uuid2"));
//        Assert.assertEquals(8010103, exception2.getStatus());
//
//        // targetOrgId is null
//        EntityOrgInfoMappingInfoRes stdOrg = new EntityOrgInfoMappingInfoRes();
//        stdOrg.setStdOrgCode("uuid").setStdOrgName("标准组织").setUuid("uuid1").setName("非标组织");
//        PowerMockito.doReturn(stdOrg).when(pmAuthService).getStdOrgByLeanBuildOrg(Mockito.any());
//        pmUserInfoResp.setCurrentOrg(new CurrentOrg().setLeanBuildOrgId("UUID").setStdOrgCode(stdOrg.getStdOrgCode
//        ()).setStdOrgName(stdOrg.getStdOrgName()));
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//        // targetOrgId is  null
//        //Assert.assertSame(pmUserInfoResp, Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo",
//        pmUserInfoResp, null));
//
//        // targetOrgId is  null and cacheUserInfoResp  is not null and cacheUserInfoResp.getCurrentOrg() is null
//        PmUserInfoResp cacheUserInfoResp = new PmUserInfoResp();
//        PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(cacheUserInfoResp);
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//
//        // targetOrgId is  null and cacheUserInfoResp  is not null and cacheUserInfoResp.getCurrentOrg() is not  null
//        cacheUserInfoResp.setCurrentOrg(new CurrentOrg());
//        PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(cacheUserInfoResp);
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//
//        // targetOrgId is  null and cacheUserInfoResp  is not null and cacheUserInfoResp.getCurrentOrg() is not
//        null and LeanBuildOrgId is null
//        cacheUserInfoResp.setCurrentOrg(new CurrentOrg());
//        PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(cacheUserInfoResp);
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//
//        // targetOrgId is  null and cacheUserInfoResp  is not null and cacheUserInfoResp.getCurrentOrg() is not
//        null and LeanBuildOrgId is not  null
//        cacheUserInfoResp.setCurrentOrg(new CurrentOrg().setLeanBuildOrgId("LeanBuildOrgId"));
//        PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(cacheUserInfoResp);
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//
//        cacheUserInfoResp.setCurrentOrg(new CurrentOrg().setLeanBuildOrgId("UUID"));
//        PowerMockito.when(UserUtil.class, "getUserInfoResp").thenReturn(cacheUserInfoResp);
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, null);
//
//        // targetOrgId is not null and cacheUserInfoResp  is null
//        Whitebox.invokeMethod(pmAuthService,"setCurrentOrgInfo", pmUserInfoResp, "UUID");
//
//
//
//    }
//}
