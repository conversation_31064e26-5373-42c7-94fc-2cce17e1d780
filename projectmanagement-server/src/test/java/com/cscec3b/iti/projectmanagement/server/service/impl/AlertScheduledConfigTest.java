package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.ArrayList;

import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectProgressMapper;
import com.cscec3b.iti.projectmanagement.server.scheduled.AlertScheduledConfig;
import com.cscec3b.iti.projectmanagement.server.scheduled.dto.WarnInfoDto;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({AlertScheduledConfig.class})
public class AlertScheduledConfigTest {

    @InjectMocks
    private AlertScheduledConfig alertScheduledConfig;

    @Mock
    private ProjectProgressMapper projectProgressMapper;

    @Test
    public void alertSchedule() throws Exception {
        PowerMockito.doReturn(new ArrayList<>()).when(projectProgressMapper, "qryWarnInfos");
        alertScheduledConfig.alertSchedule();
        PowerMockito
            .doReturn(Lists.newArrayList(
                new WarnInfoDto().setApproveStatus(YesNoEnum.YES.getDictCode())
                    .setSignStatus(YesNoEnum.NO.getDictCode()).setBusinessType("测试").setSignTime(19021342l)
                    .setApproveFinishTime(System.currentTimeMillis() - 1l).setProjectId(1l),
                new WarnInfoDto().setApproveStatus(YesNoEnum.NO.getDictCode()).setSignStatus(YesNoEnum.NO.getDictCode())
                    .setBusinessType("水务水利").setSignTime(19021342l)
                    .setApproveFinishTime(System.currentTimeMillis() - 1l).setProjectId(1l),
                new WarnInfoDto().setSignStatus(YesNoEnum.YES.getDictCode())
                    .setApproveStatus(YesNoEnum.NO.getDictCode()).setBusinessType("水务水利").setSignTime(19021342l)
                    .setApproveFinishTime(System.currentTimeMillis() - 1l).setProjectId(2l),
                new WarnInfoDto().setSignStatus(YesNoEnum.NO.getDictCode())
                    .setApproveStatus(YesNoEnum.YES.getDictCode()).setBusinessType("水务水利").setSignTime(19021342l)
                    .setApproveFinishTime(System.currentTimeMillis() - 1l).setProjectId(3l)))
            .when(projectProgressMapper, "qryWarnInfos");
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectProgressMapper, "batchUpdateById", Mockito.anyList(),
            Mockito.anyInt());
        alertScheduledConfig.alertSchedule();
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectProgressMapper, "batchUpdateById", Mockito.anyList(),
            Mockito.anyInt());
        alertScheduledConfig.alertSchedule();
        PowerMockito.doReturn(Lists.newArrayList(
            new WarnInfoDto().setSignStatus(YesNoEnum.YES.getDictCode()).setApproveStatus(YesNoEnum.NO.getDictCode())
                .setBusinessType("水务水利").setSignTime(19021342l).setApproveFinishTime(19021342l).setProjectId(2l)))
            .when(projectProgressMapper, "qryWarnInfos");
        alertScheduledConfig.alertSchedule();
    }
}
