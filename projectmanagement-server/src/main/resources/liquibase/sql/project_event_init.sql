INSERT INTO `project_management`.`project_event`(`id`, `event_code`, `event_name`, `event_desc`,
                                                 `business_column`, `event_api`, `create_at`, `create_by`,
                                                 `update_at`, `update_by`, `status`)
VALUES (100001, 'A100001', '立项事件', '立项事件',
        '[{
            "table": "project",
            "columns": [{
                "desc": "项目主键",
                "type": "Long",
                "column": "id"
            },
            {
                "desc": "工程名称",
                "type": "String",
                "column": "project_name"
            },
            {
                "desc": "项目地址",
                "type": "String",
                "column": "project_address"
            },
            {
                "desc": "承包模式",
                "type": "String",
                "column": "contract_mode"
            },
            {
                "desc": "项目分类名称",
                "type": "String",
                "column": "project_class_name"
            },
            {
                "desc": "是否生态敏感区项目",
                "type": "Integer",
                "column": "is_ecology_sensitive"
            },
            {
                "desc": "是否边小远散项目",
                "type": "Integer",
                "column": "is_edge_small"
            },
            {
                "desc": "投资主体",
                "type": "String",
                "column": "investors"
            },
            {
                "desc": "合同总金额",
                "type": "BigDecimal",
                "column": "contract_amount"
            },
            {
                "desc": "合同开工日期",
                "type": "Long",
                "column": "worker_begin_time"
            },
            {
                "desc": "合同竣工日期",
                "type": "Long",
                "column": "worker_end_time"
            },
            {
                "desc": "工期奖罚类型",
                "type": "String",
                "column": "worker_date_reward_punish"
            },
            {
                "desc": "工期奖罚条款",
                "type": "String",
                "column": "worker_reward_punish_appoint"
            },
            {
                "desc": "质量奖罚类型",
                "type": "String",
                "column": "reward_punish_type"
            },
            {
                "desc": "质量奖罚条款",
                "type": "String",
                "column": "reward_punish_terms"
            },
            {
                "desc": "客户名称",
                "type": "String",
                "column": "customer_name"
            },
            {
                "desc": "客户母公司",
                "type": "String",
                "column": "superior_company_name"
            },
            {
                "desc": "现场业主代表姓名",
                "type": "String",
                "column": "scene_owner_represent_name"
            },
            {
                "desc": "现场业主代表职务",
                "type": "String",
                "column": "scene_owner_represent_duty"
            },
            {
                "desc": "现场业主代表联系电话",
                "type": "String",
                "column": "scene_owner_represent_phone"
            },
            {
                "desc": "监理单位",
                "type": "String",
                "column": "supervisor"
            },
            {
                "desc": "设计单位",
                "type": "String",
                "column": "designer"
            },
            {
                "desc": "质量要求",
                "type": "String",
                "column": "quality_guarantee"
            },
            {
                "desc": "安全文明施工要求",
                "type": "String",
                "column": "safety_requirement"
            },
            {
                "desc": "项目经理",
                "type": "String",
                "column": "project_manager"
            },
            {
                "desc": "客户企业性质",
                "type": "String",
                "column": "enterprise_type"
            },
            {
                "desc": "客户级别",
                "type": "String",
                "column": "customer_level"
            },
            {
                "desc": "云枢执行单位",
                "type": "String",
                "column": "yunshu_execute_unit"
            },
            {
                "desc": "云枢执行单位code",
                "type": "String",
                "column": "yunshu_execute_unit_code"
            },
            {
                "desc": "云枢执行单位Id",
                "type": "String",
                "column": "yunshu_execute_unit_id"
            },
            {
                "desc": "云枢执行单位id_path",
                "type": "String",
                "column": "yunshu_execute_unit_id_path"
            }]
        },
        {
            "table": "bid_summary",
            "columns": [{
                "desc": "中标项目经理",
                "type": "String",
                "column": "winning_project_manager"
            },
            {
                "desc": "执行项目经理",
                "type": "String",
                "column": "executive_project_manager"
            }]
        }]',
        'cpm/api/external/open/id', NULL, NULL, NULL, NULL, 1);