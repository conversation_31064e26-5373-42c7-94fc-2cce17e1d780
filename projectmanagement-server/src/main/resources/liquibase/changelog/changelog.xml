<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="liquibase-gen" id="1669255348670-1">
        <createTable tableName="api_retry_call">
            <column name="id" remarks="主键id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="class_name" remarks="类名或接口名称" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="method_name" remarks="方法名" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="param_type" remarks="方法参数类型" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="param" remarks="方法参数值" type="VARCHAR(4096)"/>
            <column name="result" remarks="api返回结果" type="VARCHAR(2048)"/>
            <column defaultValueNumeric="0" name="success" remarks="请求结果 1:成功; 2:失败" type="TINYINT(3)"/>
            <column name="try_result" remarks="定时任务重试结果" type="VARCHAR(1024)"/>
            <column defaultValueNumeric="0" name="human_intervention" remarks="是否需要人工干预 0:否; 1:是;"
                    type="TINYINT(3)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="update_at" remarks="更新时间" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-2">
        <createTable tableName="bid_opening_records">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="relation_id" remarks="关联投标总结id" type="BIGINT"/>
            <column name="bidder" remarks="投标单位" type="VARCHAR(128)"/>
            <column name="tender_offer" remarks="投标报价" type="DECIMAL(20, 2)"/>
            <column name="duration" remarks="工期" type="VARCHAR(20)"/>
            <column name="credit_mark" remarks="资信标得分" type="DECIMAL(20, 2)"/>
            <column name="technical_proposal_score" remarks="技术标得分" type="DECIMAL(20, 2)"/>
            <column name="score_of_commercial_proposal" remarks="商务标得分" type="DECIMAL(20, 2)"/>
            <column name="total_score" remarks="总得分" type="DECIMAL(20, 2)"/>
            <column name="quotation_score" remarks="报价得分" type="DECIMAL(20, 2)"/>
            <column name="quality" remarks="质量" type="VARCHAR(128)"/>
            <column name="company_no" remarks="企业编号" type="VARCHAR(32)"/>
            <column name="data_from" remarks="Y:我司 N:竞争对手" type="VARCHAR(2)"/>
            <column name="bid_rate" remarks="投标费率" type="VARCHAR(32)"/>
            <column name="ranking" remarks="排名" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-3">
        <createTable tableName="bid_summary">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="approval_person" remarks="复核审批用户id" type="VARCHAR(20)"/>
            <column name="submit_person" remarks="发起人单位" type="VARCHAR(50)"/>
            <column name="is_create_head" type="VARCHAR(2)"/>
            <column name="execute_unit" remarks="执行单位" type="VARCHAR(128)"/>
            <column name="is_independent" remarks="独立性" type="VARCHAR(2)"/>
            <column name="associated_project" remarks="关联工程/合同" type="VARCHAR(32)"/>
            <column name="project_code" remarks="工程编号" type="VARCHAR(32)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="project_abbreviation" remarks="工程简称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="sign_form_office" remarks="所属办事处" type="VARCHAR(20)"/>
            <column name="province" remarks="省" type="VARCHAR(10)"/>
            <column name="city" remarks="市" type="VARCHAR(10)"/>
            <column name="region" remarks="区" type="VARCHAR(10)"/>
            <column name="address" remarks="具体地址" type="VARCHAR(128)"/>
            <column name="country" remarks="国别" type="VARCHAR(10)"/>
            <column name="country_project_type" remarks="工程类型（国家标准）" type="VARCHAR(20)"/>
            <column name="market_project_type" remarks="工程类型（总公司市场口径）" type="VARCHAR(20)"/>
            <column name="market_project_type2" remarks="工程类型(总公司市场口径)2" type="VARCHAR(20)"/>
            <column name="project_type" type="VARCHAR(50)"/>
            <column name="project_type2" remarks="工程类型(总公司综合口径)2" type="VARCHAR(20)"/>
            <column name="project_type3" remarks="工程类型(总公司综合口径)3" type="VARCHAR(20)"/>
            <column name="project_type4" remarks="工程类型(总公司综合口径)4" type="VARCHAR(20)"/>
            <column name="total_subcontracting_category" remarks="总分包类别" type="VARCHAR(32)"/>
            <column name="investment_projects" remarks="是否投资项目" type="VARCHAR(2)"/>
            <column name="investors" remarks="投资主体" type="VARCHAR(20)"/>
            <column name="structural_style" remarks="结构形式" type="VARCHAR(20)"/>
            <column name="structural_style2" remarks="结构形式2" type="VARCHAR(20)"/>
            <column name="including_steel" remarks="是否有钢结构" type="VARCHAR(20)"/>
            <column name="volume" type="VARCHAR(32)"/>
            <column name="fabricated" remarks="是否装配式" type="VARCHAR(20)"/>
            <column name="estimated_cost" remarks="预计造价" type="DECIMAL(20, 2)"/>
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="上级相关方" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(32)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话" type="VARCHAR(11)"/>
            <column name="designer" remarks="设计单位" type="VARCHAR(64)"/>
            <column name="supervisor" remarks="监理单位" type="VARCHAR(64)"/>
            <column name="bidding_agency" remarks="招标代理机构" type="VARCHAR(64)"/>
            <column name="bid_opening_personnel" remarks="参与开标人员" type="VARCHAR(32)"/>
            <column name="in_bid_type" remarks="是否中标" type="VARCHAR(2)"/>
            <column name="in_bid_value" remarks="中标主体" type="VARCHAR(20)"/>
            <column name="is_statistic" remarks="是否纳入统计" type="VARCHAR(2)"/>
            <column name="contract_mode1" remarks="承包模式" type="VARCHAR(20)"/>
            <column name="contract_mode2" remarks="承包模式2" type="VARCHAR(20)"/>
            <column name="bidding_type" remarks="招标模式" type="VARCHAR(20)"/>
            <column name="bidding_range" remarks="招标范围" type="VARCHAR(128)"/>
            <column name="subpackage_engineering" remarks="发包人指定分包、独立分包的工程" type="VARCHAR(128)"/>
            <column name="total_duration" remarks="总工期（天）" type="VARCHAR(10)"/>
            <column name="duration_award_type" remarks="工期奖罚类型" type="VARCHAR(20)"/>
            <column name="duration_award_clause" remarks="工期奖罚条款" type="TEXT"/>
            <column name="quality_requirement" remarks="质量要求" type="VARCHAR(20)"/>
            <column name="quality_award_type" remarks="质量奖罚类型" type="VARCHAR(20)"/>
            <column name="quality_award_clause" remarks="质量奖罚条款" type="TEXT"/>
            <column name="safe_construction" remarks="安全文明施工要求" type="TEXT"/>
            <column name="safe_construction_award" remarks="安全文明施工要求奖罚" type="TEXT"/>
            <column name="pricing_method" remarks="计价方式" type="VARCHAR(20)"/>
            <column name="contract_form" remarks="合同形式" type="VARCHAR(20)"/>
            <column name="labor_cost_type" remarks="人工费可调" type="VARCHAR(2)"/>
            <column name="material_science_type" remarks="主材费可调" type="VARCHAR(2)"/>
            <column name="advance_charge_type" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="payment_type" remarks="进度款付款方式" type="VARCHAR(20)"/>
            <column name="be_completed_proportion" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="be_completed_cycle" remarks="竣工验收收款周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_payment_proportion" remarks="结算支付比例" type="VARCHAR(10)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="advance_or_not" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="investment_guarantee_mode" remarks="投标担保方式" type="VARCHAR(20)"/>
            <column name="warranty_money" remarks="保修金" type="VARCHAR(20)"/>
            <column name="warranty_money_proportion" remarks="保修金支付比例" type="VARCHAR(10)"/>
            <column name="warranty_money_mode" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
            <column name="payment_mode" remarks="现金支付方式" type="VARCHAR(20)"/>
            <column name="performance_guarantee_mode" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="bid_evaluation_method" remarks="评标办法" type="VARCHAR(255)"/>
            <column name="contract_style" remarks="合同范本类型" type="VARCHAR(20)"/>
            <column name="tax_included_money" remarks="含税中标金额" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_money" remarks="不含税中标金额" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_construction_money" remarks="不含税自行施工金额" type="DECIMAL(20, 2)"/>
            <column name="winning_project_manager" remarks="中标项目经理" type="VARCHAR(128)"/>
            <column name="winning_project_manager_number" remarks="中标项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="executive_project_manager" remarks="执行项目经理" type="VARCHAR(128)"/>
            <column name="executive_project_manager_number" remarks="联系方式" type="VARCHAR(32)"/>
            <column name="bid_winning_notice_url" remarks="中标通知书" type="TEXT"/>
            <column name="bid_opening_record_url" remarks="开标记录" type="TEXT"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="project_attachment" remarks="项目附件信息对象" type="JSON"/>
            <column name="estimated_advance_amount" remarks="预估垫资金额" type="VARCHAR(20)"/>
            <column name="origin_file_id" type="BIGINT"/>
            <column name="belong_id" remarks="所属源文件id" type="BIGINT"/>
            <column name="bid_opening_time" remarks="开标时间" type="BIGINT"/>
            <column name="calibration_time" remarks="定标时间" type="BIGINT"/>
            <column name="cross_bidding_time" remarks="交标时间" type="BIGINT"/>
            <column name="start_time" remarks="开工时间" type="BIGINT"/>
            <column name="be_completed_time" remarks="竣工时间" type="BIGINT"/>
            <column name="execute_unit_id" remarks="执行单位id UC标准组织id" type="VARCHAR(128)"/>
            <column name="execute_unit_code" remarks="执行单位Code UC标准组织uuid" type="VARCHAR(128)"/>
            <column name="break_bottom" remarks="突破底线条款" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-4">
        <createTable tableName="bureau_contract">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="approval_person" remarks="复核审批用户id" type="VARCHAR(20)"/>
            <column name="submit_person" remarks="发起人单位" type="VARCHAR(50)"/>
            <column name="is_create_head" remarks="是否创建指挥部" type="VARCHAR(2)"/>
            <column name="execute_unit" remarks="执行单位" type="VARCHAR(128)"/>
            <column name="is_independent" remarks="独立性" type="VARCHAR(2)"/>
            <column name="associated_project" remarks="关联工程/合同" type="VARCHAR(32)"/>
            <column name="contract_code" remarks="合同编号" type="VARCHAR(32)"/>
            <column name="project_code" remarks="工程编号" type="VARCHAR(32)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="project_short_name" remarks="工程简称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="province" remarks="省" type="VARCHAR(10)"/>
            <column name="city" remarks="市" type="VARCHAR(10)"/>
            <column name="region" remarks="区" type="VARCHAR(10)"/>
            <column name="address" remarks="具体地址" type="VARCHAR(128)"/>
            <column name="country" remarks="国别" type="VARCHAR(10)"/>
            <column name="country_project_type" remarks="工程类型（国家标准）" type="VARCHAR(20)"/>
            <column name="market_project_type" remarks="工程类型（总公司市场口径）" type="VARCHAR(20)"/>
            <column name="market_project_type2" remarks="工程类型(总公司市场口径)2" type="VARCHAR(20)"/>
            <column name="project_type" type="VARCHAR(50)"/>
            <column name="project_type2" remarks="工程类型(总公司综合口径)2" type="VARCHAR(20)"/>
            <column name="project_type3" remarks="工程类型(总公司综合口径)3" type="VARCHAR(20)"/>
            <column name="project_type4" remarks="工程类型(总公司综合口径)4" type="VARCHAR(20)"/>
            <column name="project_attachment" remarks="项目附件信息对象" type="JSON"/>
            <column name="total_subcontracting_category" remarks="总分包类别" type="VARCHAR(20)"/>
            <column name="structural_style" remarks="结构形式" type="VARCHAR(20)"/>
            <column name="structural_style2" remarks="结构形式2" type="VARCHAR(20)"/>
            <column name="including_steel" remarks="是否有钢结构" type="VARCHAR(2)"/>
            <column name="project_maxLength" remarks="最长桩基长度" type="VARCHAR(10)"/>
            <column name="project_max_width" remarks="最大桩径" type="VARCHAR(10)"/>
            <column name="contract_type" remarks="合同类型" type="VARCHAR(20)"/>
            <column name="fabricated" remarks="是否装配式" type="VARCHAR(2)"/>
            <column name="company_assessment_indicators" remarks="是否纳入公司考核指标" type="VARCHAR(2)"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="客户母公司" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(64)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话" type="VARCHAR(11)"/>
            <column name="designer" remarks="设计单位" type="VARCHAR(64)"/>
            <column name="supervisor" remarks="监理单位" type="VARCHAR(64)"/>
            <column name="signed_subject_value" remarks="签约主体" type="VARCHAR(20)"/>
            <column name="do_unit" remarks="实施单位" type="VARCHAR(128)"/>
            <column name="total_amount" remarks="含税合同总价（人民币）" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_money" remarks="不含税金额" type="DECIMAL(20, 2)"/>
            <column name="mid_amount_self" remarks="自行施工不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_civil_amount" remarks="土建不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_install_amount" remarks="安装不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_steel_structure_amount" remarks="钢结构不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_total_service_amount" remarks="总包服务费" type="DECIMAL(20, 2)"/>
            <column name="self_other_amount" remarks="其他" type="DECIMAL(20, 2)"/>
            <column name="subcontract_amount" remarks="暂列金额或甲指分包金额" type="DECIMAL(20, 2)"/>
            <column name="project_tax_amount" remarks="销项税额" type="DECIMAL(20, 2)"/>
            <column name="subcontract_content" remarks="暂列金额工作内容" type="VARCHAR(1024)"/>
            <column name="bid_manager" remarks="中标项目经理" type="VARCHAR(64)"/>
            <column name="bid_manager_code" remarks="中标项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="excute_manager" remarks="执行项目经理" type="VARCHAR(64)"/>
            <column name="excute_manager_code" remarks="执行项目经理联系方式" type="VARCHAR(11)"/>
            <column name="contract_manager" remarks="合同项目经理" type="VARCHAR(64)"/>
            <column name="contract_manager_code" remarks="合同项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="government_manager" remarks="政府备案项目经理" type="VARCHAR(64)"/>
            <column name="government_manager_code" remarks="政府备案项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="contract_mode1" remarks="承包模式" type="VARCHAR(20)"/>
            <column name="contract_mode2" remarks="承包模式2" type="VARCHAR(20)"/>
            <column name="contract_scope" remarks="合同承包范围" type="VARCHAR(1024)"/>
            <column name="issuer_project" remarks="发包人指定分包、独立分包的工程" type="VARCHAR(1024)"/>
            <column name="count_days" remarks="总工期（天）" type="INT"/>
            <column name="worker_date_reward_punish" remarks="工期奖罚类型" type="VARCHAR(20)"/>
            <column name="worker_reward_punish_appoint" remarks="工期奖罚条款" type="VARCHAR(255)"/>
            <column name="contract_style" remarks="合同范本类型" type="VARCHAR(20)"/>
            <column name="quality_guarantee" type="VARCHAR(20)"/>
            <column name="reward_punish_type" remarks="质量奖罚类型" type="VARCHAR(20)"/>
            <column name="reward_punish_terms" remarks="质量奖罚条款" type="VARCHAR(255)"/>
            <column name="safety_requirement" remarks="安全文明施工要求" type="VARCHAR(255)"/>
            <column name="safety_reward_punish_terms" remarks="安全文明施工奖罚条款" type="VARCHAR(255)"/>
            <column name="pricing_method" remarks="计价方式" type="VARCHAR(20)"/>
            <column name="contract_form" remarks="合同形式" type="VARCHAR(20)"/>
            <column name="cost_ofLabor_change" remarks="人工费是否可调" type="VARCHAR(2)"/>
            <column name="change_way" remarks="人工费调差形式" type="VARCHAR(20)"/>
            <column name="terms" remarks="人工费调差条款" type="VARCHAR(1024)"/>
            <column name="cost_ofLabor_change2" remarks="主材费是否可调" type="VARCHAR(2)"/>
            <column name="change_rate2" remarks="主材费调差幅度" type="VARCHAR(15)"/>
            <column name="terms2" remarks="主材费调差条款" type="VARCHAR(1024)"/>
            <column name="advances_flag" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="advances_way" remarks="进度款付款方式" type="VARCHAR(20)"/>
            <column name="completed_rate" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="completed_cycle" remarks="竣工验收收款周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_rate" remarks="结算支付比例" type="VARCHAR(10)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="warranty_premium" type="VARCHAR(20)"/>
            <column name="warranty_premium_rate" remarks="保修金比例" type="VARCHAR(10)"/>
            <column name="warranty_premium_way" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
            <column name="no_cash_pay_way" remarks="非现金支付方式" type="VARCHAR(20)"/>
            <column name="advances_fund_flag" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="guarantee_way" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="landLegality_flag" remarks="项目及土地是否合法" type="VARCHAR(2)"/>
            <column name="give_up_compensate_flag" remarks="是否放弃优先受偿权" type="VARCHAR(2)"/>
            <column name="pay_rateLess_eighty_flag" remarks="付款比例是否低于百分之八十" type="VARCHAR(4)"/>
            <column name="node_more_two_month_flag" remarks="支付节点时间是否超过2个月" type="VARCHAR(4)"/>
            <column name="commercial_ticket_proportion" remarks="商票比例" type="VARCHAR(10)"/>
            <column name="big_risk_measure" remarks="重大风险化解措施" type="VARCHAR(128)"/>
            <column name="presentation_user" remarks="交底人" type="VARCHAR(64)"/>
            <column name="recipient" remarks="接收人" type="VARCHAR(64)"/>
            <column name="remark" remarks="备注" type="VARCHAR(128)"/>
            <column name="bureau_contract_code" remarks="局内分包合同编号" type="VARCHAR(50)"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="origin_file_id" type="BIGINT"/>
            <column name="belong_id" remarks="所属源文件id" type="BIGINT"/>
            <column name="successful_time" remarks="实际中标日期" type="BIGINT"/>
            <column name="actual_signed_time" remarks="实际签约日期" type="BIGINT"/>
            <column name="worker_begin_time" remarks="合同开工日期" type="BIGINT"/>
            <column name="worker_end_time" remarks="合同竣工日期" type="BIGINT"/>
            <column name="real_work_begin_time" remarks="实际开工日期" type="BIGINT"/>
            <column name="predict_work_end_time" remarks="预计实际竣工日期" type="BIGINT"/>
            <column name="presentation_time" remarks="交底日期" type="BIGINT"/>
            <column name="break_bottom" remarks="突破底线条款" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-5">
        <createTable tableName="bureau_supplementary_agreement">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="submit_person" remarks="发起人单位" type="VARCHAR(50)"/>
            <column name="agreement_code" remarks="补充协议编号" type="VARCHAR(32)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="bureau_project" remarks="是否局重点项目" type="VARCHAR(2)"/>
            <column name="mandate_foreign" remarks="是否授权外" type="VARCHAR(2)"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="客户母公司" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(32)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话" type="VARCHAR(11)"/>
            <column name="contract_responsible_person" remarks="合同经办人" type="VARCHAR(64)"/>
            <column name="company_assessment_indicators" remarks="是否纳入公司考核指标" type="VARCHAR(2)"/>
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
            <column name="supplement_amount" remarks="补充协议金额" type="DECIMAL(20, 2)"/>
            <column name="pricing_method" remarks="计价方式" type="VARCHAR(20)"/>
            <column name="contract_form" remarks="合同形式" type="VARCHAR(20)"/>
            <column name="cost_of_labor_change" remarks="人工费是否可调" type="VARCHAR(2)"/>
            <column name="cost_of_labor_change2" remarks="主材费是否可调" type="VARCHAR(2)"/>
            <column name="advances_flag" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="advances_way" remarks="付款方式" type="VARCHAR(20)"/>
            <column name="advances_month_rate" remarks="月进度付款比例" type="VARCHAR(10)"/>
            <column name="completed_rate" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="completed_cycle" remarks="竣工验收周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_rate" remarks="结算支付比例" type="VARCHAR(20)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="warranty_premium" type="VARCHAR(20)"/>
            <column name="warranty_premium_rate" remarks="保修金比例" type="VARCHAR(10)"/>
            <column name="warranty_premium_way" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
            <column name="specific_pay_way" remarks="现金支付方式" type="VARCHAR(20)"/>
            <column name="advances_fund_flag" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="guarantee_way" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="agreement_url" remarks="补充协议文本" type="TEXT"/>
            <column name="contract_term_url" remarks="合同主要条款对比表" type="TEXT"/>
            <column name="law_url" remarks="法律意见书" type="TEXT"/>
            <column name="bureau_supplementary_agreement_code" remarks="局内补充协议编号" type="VARCHAR(100)"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="belong_id" remarks="所属源文件id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-6">
        <createTable tableName="contract">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="submit_person" remarks="发起人单位" type="VARCHAR(50)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="project_code" remarks="工程编号" type="VARCHAR(32)"/>
            <column name="project_short_name" remarks="工程简称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="province" remarks="省" type="VARCHAR(10)"/>
            <column name="city" remarks="市" type="VARCHAR(10)"/>
            <column name="region" remarks="区" type="VARCHAR(10)"/>
            <column name="address" remarks="具体地址" type="VARCHAR(128)"/>
            <column name="country" remarks="国别" type="VARCHAR(10)"/>
            <column name="country_project_type" remarks="工程类型（国家标准）" type="VARCHAR(20)"/>
            <column name="market_project_type" remarks="工程类型（总公司市场口径）" type="VARCHAR(20)"/>
            <column name="market_project_type2" remarks="工程类型(总公司市场口径)2" type="VARCHAR(20)"/>
            <column name="project_type" type="VARCHAR(50)"/>
            <column name="project_type2" remarks="工程类型(总公司综合口径)2" type="VARCHAR(20)"/>
            <column name="project_type3" remarks="工程类型(总公司综合口径)3" type="VARCHAR(20)"/>
            <column name="project_type4" remarks="工程类型(总公司综合口径)4" type="VARCHAR(20)"/>
            <column name="project_attachment" remarks="项目附件信息对象" type="JSON"/>
            <column name="total_subcontracting_category" remarks="总分包类别" type="VARCHAR(20)"/>
            <column name="structural_style" remarks="结构形式" type="VARCHAR(20)"/>
            <column name="structural_style2" remarks="结构形式2" type="VARCHAR(20)"/>
            <column name="including_steel" remarks="是否有钢结构" type="VARCHAR(2)"/>
            <column name="project_max_length" remarks="最长桩基长度" type="VARCHAR(10)"/>
            <column name="project_max_width" remarks="最大桩径" type="VARCHAR(10)"/>
            <column name="contract_type" remarks="合同类型" type="VARCHAR(20)"/>
            <column name="fabricated" remarks="是否装配式" type="VARCHAR(2)"/>
            <column name="is_investment_financing_driven_projects" remarks="是否为投融资带动项目" type="VARCHAR(2)"/>
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
            <column name="investment_projects" remarks="是否投资项目" type="VARCHAR(2)"/>
            <column name="investors" remarks="投资主体" type="VARCHAR(20)"/>
            <column name="sign_form_office" remarks="所属办事处" type="VARCHAR(20)"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="客户母公司" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(32)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话" type="VARCHAR(11)"/>
            <column name="designer" remarks="设计单位" type="VARCHAR(64)"/>
            <column name="supervisor" remarks="监理单位" type="VARCHAR(64)"/>
            <column name="signed_subject_value" remarks="签约主体" type="VARCHAR(20)"/>
            <column name="do_unit" type="VARCHAR(128)"/>
            <column name="total_amount" remarks="含税合同总价（人民币）" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_money" remarks="不含税金额" type="DECIMAL(20, 2)"/>
            <column name="mid_amount_self" remarks="自行施工不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_civil_amount" remarks="土建不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_install_amount" remarks="安装不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_steel_structure_amount" remarks="钢结构不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_total_service_amount" remarks="总包服务费" type="DECIMAL(20, 2)"/>
            <column name="subcontract_amount" remarks="暂列金额或甲指分包金额" type="DECIMAL(20, 2)"/>
            <column name="project_tax_amount" remarks="销项税额" type="DECIMAL(20, 2)"/>
            <column name="contract_optimize_clause" remarks="合同优化条款" type="VARCHAR(1024)"/>
            <column name="contract_optimize_amount" remarks="合同优化金额" type="DECIMAL(20, 2)"/>
            <column name="contract_optimize_ratio" remarks="合同优化率" type="VARCHAR(10)"/>
            <column name="subcontract_content" remarks="暂列金额工作内容" type="VARCHAR(1024)"/>
            <column name="bid_manager" remarks="中标项目经理" type="VARCHAR(64)"/>
            <column name="bid_manager_code" remarks="中标项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="excute_manager" remarks="执行项目经理" type="VARCHAR(64)"/>
            <column name="excute_manager_code" remarks="执行项目经理联系方式" type="VARCHAR(11)"/>
            <column name="contract_manager" remarks="合同项目经理" type="VARCHAR(64)"/>
            <column name="contract_manager_code" remarks="合同项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="government_manager" remarks="政府备案项目经理" type="VARCHAR(64)"/>
            <column name="government_manager_code" remarks="政府备案项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="contract_mode1" remarks="承包模式" type="VARCHAR(20)"/>
            <column name="contract_mode2" remarks="承包模式2" type="VARCHAR(20)"/>
            <column name="contract_scope" remarks="合同承包范围" type="VARCHAR(1024)"/>
            <column name="issuer_project" remarks="发包人指定分包、独立分包的工程" type="VARCHAR(1024)"/>
            <column name="count_days" remarks="总工期（天）" type="INT"/>
            <column name="worker_date_reward_punish" remarks="工期奖罚类型" type="VARCHAR(20)"/>
            <column name="worker_reward_punish_appoint" remarks="工期奖罚条款" type="VARCHAR(255)"/>
            <column name="contract_style" remarks="合同范本类型" type="VARCHAR(20)"/>
            <column name="quality_guarantee" remarks="质量要求" type="VARCHAR(20)"/>
            <column name="reward_punish_type" remarks="质量奖罚类型" type="VARCHAR(20)"/>
            <column name="reward_punish_terms" remarks="质量奖罚条款" type="VARCHAR(255)"/>
            <column name="safety_requirement" remarks="安全文明施工要求" type="VARCHAR(255)"/>
            <column name="safety_reward_punish_terms" remarks="安全文明施工奖罚条款" type="VARCHAR(255)"/>
            <column name="pricing_method" remarks="计价方式" type="VARCHAR(20)"/>
            <column name="contract_form" remarks="合同形式" type="VARCHAR(20)"/>
            <column name="cost_of_labor_change" remarks="人工费是否可调" type="VARCHAR(2)"/>
            <column name="cost_of_labor_change2" remarks="主材费是否可调" type="VARCHAR(2)"/>
            <column name="advances_flag" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="advances_way" remarks="进度款付款方式" type="VARCHAR(20)"/>
            <column name="advances_month_rate" remarks="月进度付款比例" type="VARCHAR(10)"/>
            <column name="completed_rate" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="completed_cycle" remarks="竣工验收收款周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_rate" type="VARCHAR(10)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="contract_code" remarks="合同编号" type="VARCHAR(50)"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="origin_file_id" type="BIGINT"/>
            <column name="warranty_premium" type="VARCHAR(20)"/>
            <column name="warranty_premium_rate" remarks="保修金比例" type="VARCHAR(10)"/>
            <column name="warranty_premium_way" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="advances_fund_flag" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="guarantee_way" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="land_legality_flag" remarks="项目及土地是否合法" type="VARCHAR(2)"/>
            <column name="give_up_compensate_flag" remarks="是否放弃优先受偿权" type="VARCHAR(2)"/>
            <column name="pay_rate_less_eighty_flag" remarks="付款比例是否低于百分之八十" type="VARCHAR(4)"/>
            <column name="node_more_two_month_flag" remarks="支付节点时间是否超2个月" type="VARCHAR(4)"/>
            <column name="belong_id" remarks="所属源文件id" type="BIGINT"/>
            <column name="is_first_contract" remarks="是否首合同" type="TINYINT(3)"/>
            <column name="successful_time" remarks="实际中标日期" type="BIGINT"/>
            <column name="actual_signed_time" remarks="实际签约日期" type="BIGINT"/>
            <column name="worker_begin_time" remarks="合同开工日期" type="BIGINT"/>
            <column name="worker_end_time" remarks="合同竣工日期" type="BIGINT"/>
            <column name="real_work_begin_time" remarks="实际开工日期" type="BIGINT"/>
            <column name="predict_work_end_time" remarks="预计实际竣工日期" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-7">
        <createTable tableName="project">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="project_dept_type" remarks="所属部门类型（项目部/指挥部），长度与类型与UC保持一致" type="INT"/>
            <column name="project_status" remarks="0：立项中 1：完成立项" type="INT"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="project_remark" remarks="项目备注" type="VARCHAR(500)"/>
            <column name="is_ecology_sensitive" remarks="是否生态敏感区项目" type="TINYINT(3)"/>
            <column name="is_edge_small" remarks="是否边小远散项目" type="TINYINT(3)"/>
            <column name="project_level" remarks="项目级别" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(50)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话 项目中心" type="VARCHAR(50)"/>
            <column name="scene_owner_represent_name" remarks="现场业主代表姓名" type="VARCHAR(50)"/>
            <column name="scene_owner_represent_duty" remarks="现场业主代表职务" type="VARCHAR(50)"/>
            <column name="scene_owner_represent_phone" remarks="现场业主代表联系电话" type="VARCHAR(50)"/>
            <column name="engineer_parameter" remarks="工程参数json" type="VARCHAR(2000)"/>
            <column name="country_project_type" remarks="工程类型（国家标准）" type="VARCHAR(20)"/>
            <column name="region" remarks="行政区域（地理位置）" type="VARCHAR(128)"/>
            <column name="project_address" remarks="项目地址" type="VARCHAR(128)"/>
            <column name="investment_projects" remarks="是否投资项目" type="VARCHAR(2)"/>
            <column name="investors" remarks="投资主体" type="VARCHAR(20)"/>
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
            <column name="contract_amount" remarks="合同总金额（元）" type="DECIMAL(20, 2)"/>
            <column name="designer" remarks="设计单位" type="VARCHAR(64)"/>
            <column name="supervisor" remarks="监理单位" type="VARCHAR(64)"/>
            <column name="project_manager" remarks="项目经理" type="VARCHAR(20)"/>
            <column name="customer_level" remarks="客户级别" type="VARCHAR(20)"/>
            <column name="count_days" remarks="总工期" type="INT"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="客户母公司" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="signed_subject_value" remarks="签约主体" type="VARCHAR(20)"/>
            <column name="do_unit" remarks="实施单位" type="VARCHAR(128)"/>
            <column name="total_amount" remarks="含税合同总价（RMB）" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_money" remarks="不含税金额" type="DECIMAL(20, 2)"/>
            <column name="mid_amount_self" remarks="自行施工不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_civil_amount" remarks="土建不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_install_amount" remarks="安装不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_steel_structure_amount" remarks="钢结构不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_total_service_amount" remarks="总包服务费" type="DECIMAL(20, 2)"/>
            <column name="self_other_amount" remarks="其他" type="DECIMAL(20, 2)"/>
            <column name="project_tax_amount" remarks="销项税额" type="DECIMAL(20, 2)"/>
            <column name="subcontract_amount" remarks="暂列金或甲指分包金额" type="DECIMAL(20, 2)"/>
            <column name="worker_date_reward_punish" remarks="工期奖罚类型" type="VARCHAR(20)"/>
            <column name="worker_reward_punish_appoint" remarks="工期奖罚条款" type="VARCHAR(255)"/>
            <column name="contract_scope" remarks="合同承包范围" type="VARCHAR(1024)"/>
            <column name="issuer_project" remarks="发包人指定分包、独立分包的工程" type="VARCHAR(1024)"/>
            <column name="quality_guarantee" remarks="质量要求" type="VARCHAR(20)"/>
            <column name="reward_punish_type" remarks="质量奖罚类型" type="VARCHAR(20)"/>
            <column name="reward_punish_terms" remarks="质量奖罚条款" type="VARCHAR(255)"/>
            <column name="safety_requirement" remarks="安全文明施工要求" type="VARCHAR(255)"/>
            <column name="safety_reward_punish_terms" remarks="安全文明施工奖罚条款" type="VARCHAR(255)"/>
            <column name="advances_flag" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="advances_way" remarks="进度款支付方式" type="VARCHAR(20)"/>
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
            <column name="completed_rate" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="completed_cycle" remarks="竣工验收收款周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_rate" remarks="结算支付比例" type="VARCHAR(20)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="warranty_premium" type="VARCHAR(20)"/>
            <column name="warranty_premium_rate" remarks="保修金比例" type="VARCHAR(10)"/>
            <column name="warranty_premium_way" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="advances_fund_flag" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="guarantee_way" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="land_legality_flag" remarks="项目及土地是否合法" type="VARCHAR(2)"/>
            <column name="give_up_compensate_flag" remarks="是否放弃优先受偿权" type="VARCHAR(2)"/>
            <column name="pay_rate_less_eighty_flag" remarks="付款比例是否低于80%" type="VARCHAR(4)"/>
            <column name="node_more_two_month_flag" remarks="支付节点是否超2个月" type="VARCHAR(4)"/>
            <column name="project_finance_name" remarks="财商立项名称" type="VARCHAR(128)"/>
            <column name="project_finance_code" remarks="财商立项编号" type="VARCHAR(32)"/>
            <column name="project_finance_abbreviation" remarks="财商立项项目简称（中文）" type="VARCHAR(50)"/>
            <column name="project_code" remarks="工程编号" type="VARCHAR(32)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="project_abbreviation" remarks="工程简称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="execute_unit" remarks="执行单位" type="VARCHAR(128)"/>
            <column name="a8_project_code" remarks="A8项目编码" type="VARCHAR(50)"/>
            <column name="project_dept_id" remarks="所属部门id（项目部/指挥部），长度与类型与UC保持一致"
                    type="VARCHAR(36)"/>
            <column name="project_dept_name" remarks="所属部门名称，长度与类型与UC保持一致" type="VARCHAR(255)"/>
            <column name="contract_mode" remarks="承包模式" type="VARCHAR(40)"/>
            <column name="market_project_type" remarks="工程类型（总公司市场口径）" type="VARCHAR(40)"/>
            <column name="project_type" remarks="工程类型(总公司综合口径)" type="VARCHAR(80)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT"/>
            <column name="update_at" remarks="更新时间" type="BIGINT"/>
            <column name="real_enter_time" remarks="实际进场日期" type="BIGINT"/>
            <column name="work_end_time" remarks="实际竣工日期" type="BIGINT"/>
            <column name="real_open_traffic_time" remarks="五方主体验收日期（实际通车时间）" type="BIGINT"/>
            <column name="worker_begin_time" remarks="合同开工日期" type="BIGINT"/>
            <column name="worker_end_time" remarks="合同竣工日期" type="BIGINT"/>
            <column name="real_work_begin_time" remarks="实际开工日期" type="BIGINT"/>
            <column name="predict_work_end_time" remarks="预计实际竣工日期" type="BIGINT"/>
            <column name="successful_time" remarks="实际中标日期" type="BIGINT"/>
            <column name="actual_signed_time" remarks="实际签约日期" type="BIGINT"/>
            <column name="project_dept_id_path" remarks="所属(项目部/指挥部)的组织路径(根节点到当前节点)"
                    type="VARCHAR(1024)"/>
            <column name="execute_unit_id" remarks="执行单位id UC标准组织id" type="VARCHAR(128)"/>
            <column name="execute_unit_code" remarks="执行单位Code UC标准组织uuid" type="VARCHAR(128)"/>
            <column name="execute_unit_id_path" remarks="执行单位的在标准组织中的idPath" type="VARCHAR(1024)"/>
            <column name="is_create_head" remarks="是否创建指挥部 Y：是 N：否" type="VARCHAR(2)"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-8">
        <createTable tableName="project_progress">
            <column name="project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sign_time" remarks="接收合同的时间(入库时间)" type="BIGINT"/>
            <column name="approve_finish_time" remarks="立项完成时间(财商回调成功时间)" type="BIGINT"/>
            <column name="sign_status" remarks="签约进度:0 未签约 1 已签约" type="TINYINT(3)"/>
            <column name="approve_status" remarks="立项进度:0 未立项 1 已立项" type="TINYINT(3)"/>
            <column name="warn_status" remarks="预警状态:0 未触发 1 已触发 2 已解除" type="TINYINT(3)"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-9">
        <createTable tableName="supplementary_agreement">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="approval_person" remarks="复核审批用户id" type="VARCHAR(20)"/>
            <column name="submit_person" remarks="发起人单位" type="VARCHAR(50)"/>
            <column name="is_create_head" remarks="是否创建指挥部" type="VARCHAR(2)"/>
            <column name="execute_unit" remarks="执行单位" type="VARCHAR(128)"/>
            <column name="is_independent" remarks="独立性" type="VARCHAR(2)"/>
            <column name="associated_project" remarks="关联工程/合同" type="VARCHAR(32)"/>
            <column name="project_code" remarks="工程编号" type="VARCHAR(32)"/>
            <column name="project_name" remarks="工程名称" type="VARCHAR(128)"/>
            <column name="sign_form_office" remarks="所属办事处" type="VARCHAR(20)"/>
            <column name="project_abbreviation" remarks="工程简称" type="VARCHAR(128)"/>
            <column name="project_belong" remarks="工程属地" type="VARCHAR(2)"/>
            <column name="province" remarks="省" type="VARCHAR(10)"/>
            <column name="city" remarks="市" type="VARCHAR(10)"/>
            <column name="region" remarks="区" type="VARCHAR(10)"/>
            <column name="address" remarks="具体地址" type="VARCHAR(128)"/>
            <column name="country" remarks="国别" type="VARCHAR(10)"/>
            <column name="country_project_type" remarks="工程类型（国家标准）" type="VARCHAR(20)"/>
            <column name="market_project_type" remarks="工程类型（总公司市场口径）" type="VARCHAR(20)"/>
            <column name="market_project_type2" remarks="工程类型(总公司市场口径)2" type="VARCHAR(20)"/>
            <column name="project_type" type="VARCHAR(50)"/>
            <column name="project_type2" remarks="工程类型(总公司综合口径)2" type="VARCHAR(20)"/>
            <column name="project_type3" remarks="工程类型(总公司综合口径)3" type="VARCHAR(20)"/>
            <column name="project_type4" remarks="工程类型(总公司综合口径)4" type="VARCHAR(20)"/>
            <column name="total_subcontracting_category" remarks="总分包类别" type="VARCHAR(20)"/>
            <column name="structural_style" remarks="结构形式" type="VARCHAR(20)"/>
            <column name="structural_style2" remarks="结构形式2" type="VARCHAR(20)"/>
            <column name="including_steel" remarks="是否有钢结构" type="VARCHAR(2)"/>
            <column name="project_max_length" remarks="最长桩基长度" type="VARCHAR(20)"/>
            <column name="project_max_width" remarks="最大桩径" type="VARCHAR(20)"/>
            <column name="contract_type" remarks="合同类型" type="VARCHAR(20)"/>
            <column name="fabricated" remarks="是否装配式" type="VARCHAR(2)"/>
            <column name="fabricated_rate" remarks="装配率" type="VARCHAR(20)"/>
            <column name="is_investment_financing_driven_projects" remarks="是否为投融资带动项目" type="VARCHAR(2)"/>
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
            <column name="investment_projects" remarks="是否投资项目" type="VARCHAR(2)"/>
            <column name="investors" remarks="投资主体" type="VARCHAR(20)"/>
            <column name="customer_name" remarks="客户名称" type="VARCHAR(128)"/>
            <column name="superior_company_name" remarks="客户母公司" type="VARCHAR(128)"/>
            <column name="enterprise_type" remarks="客户企业性质" type="VARCHAR(20)"/>
            <column name="contact_person" remarks="建设单位（甲方）联系人" type="VARCHAR(64)"/>
            <column name="contact_person_mobile" remarks="建设单位（甲方）联系人电话" type="VARCHAR(11)"/>
            <column name="designer" remarks="设计单位" type="VARCHAR(64)"/>
            <column name="supervisor" remarks="监理单位" type="VARCHAR(64)"/>
            <column name="signed_subject_value" remarks="签约主体" type="VARCHAR(20)"/>
            <column name="do_unit" remarks="实施单位" type="VARCHAR(128)"/>
            <column name="total_amount" remarks="含税合同总价（人民币）" type="DECIMAL(20, 2)"/>
            <column name="no_tax_included_money" remarks="不含税金额" type="DECIMAL(20, 2)"/>
            <column name="mid_amount_self" remarks="自行施工不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_civil_amount" remarks="土建不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_install_amount" remarks="安装不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_steel_structure_amount" remarks="钢结构不含税金额" type="DECIMAL(20, 2)"/>
            <column name="self_total_service_amount" remarks="总包服务费" type="DECIMAL(20, 2)"/>
            <column name="self_other_amount" remarks="其他" type="DECIMAL(20, 2)"/>
            <column name="subcontract_amount" remarks="暂列金额或甲指分包金额" type="DECIMAL(20, 2)"/>
            <column name="project_tax_amount" remarks="销项税额" type="DECIMAL(20, 2)"/>
            <column name="contract_optimize_clause" remarks="合同优化条款" type="VARCHAR(1024)"/>
            <column name="contract_optimize_amount" remarks="合同优化金额" type="DECIMAL(20, 2)"/>
            <column name="contract_optimize_ratio" remarks="合同优化率" type="VARCHAR(10)"/>
            <column name="subcontract_content" remarks="暂列金额工作内容" type="VARCHAR(1024)"/>
            <column name="bid_manager" remarks="中标项目经理" type="VARCHAR(64)"/>
            <column name="bid_manager_code" remarks="中标项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="excute_manager" remarks="执行项目经理" type="VARCHAR(64)"/>
            <column name="excute_manager_code" remarks="执行项目经理联系方式" type="VARCHAR(11)"/>
            <column name="contract_manager" remarks="合同项目经理" type="VARCHAR(64)"/>
            <column name="contract_manager_code" remarks="合同项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="government_manager" remarks="政府备案项目经理" type="VARCHAR(64)"/>
            <column name="government_manager_code" remarks="政府备案项目经理注册证书编号" type="VARCHAR(32)"/>
            <column name="contract_mode1" remarks="承包模式" type="VARCHAR(20)"/>
            <column name="contract_mode2" remarks="承包模式2" type="VARCHAR(20)"/>
            <column name="contract_scope" remarks="合同承包范围" type="VARCHAR(1024)"/>
            <column name="issuer_project" remarks="发包人指定分包、独立分包的工程" type="VARCHAR(1024)"/>
            <column name="countdays" remarks="总工期（天）" type="INT"/>
            <column name="worker_datetime_reward_punish" remarks="工期奖罚类型" type="VARCHAR(20)"/>
            <column name="worker_reward_punish_appoint" remarks="工期奖罚条款" type="VARCHAR(255)"/>
            <column name="contract_style" remarks="合同范本类型" type="VARCHAR(20)"/>
            <column name="quality_guarantee" remarks="质量要求" type="VARCHAR(20)"/>
            <column name="reward_punish_type" remarks="质量奖罚类型" type="VARCHAR(20)"/>
            <column name="reward_punish_terms" remarks="质量奖罚条款" type="VARCHAR(255)"/>
            <column name="safety_requirement" remarks="安全文明施工要求" type="VARCHAR(255)"/>
            <column name="safety_reward_punish_terms" remarks="安全文明施工奖罚条款" type="VARCHAR(255)"/>
            <column name="pricing_method" remarks="计价方式" type="VARCHAR(20)"/>
            <column name="contract_form" remarks="合同形式" type="VARCHAR(20)"/>
            <column name="cost_of_labor_change" remarks="人工费是否可调" type="VARCHAR(2)"/>
            <column name="change_way" remarks="人工费调差形式" type="VARCHAR(64)"/>
            <column name="change_rate" remarks="信息价" type="VARCHAR(15)"/>
            <column name="terms" remarks="人工费调差条款" type="VARCHAR(1024)"/>
            <column name="cost_of_labor_change2" remarks="主材费是否可调" type="VARCHAR(2)"/>
            <column name="change_rate2" remarks="主材费调差幅度" type="VARCHAR(15)"/>
            <column name="terms2" remarks="主材费调差条款" type="VARCHAR(1024)"/>
            <column name="advances_flag" remarks="是否有预付款" type="VARCHAR(2)"/>
            <column name="advances_rate" remarks="预付款比例" type="VARCHAR(10)"/>
            <column name="advances_amount" remarks="预付款金额" type="DECIMAL(20, 2)"/>
            <column name="advances_deduction_flag" remarks="预付款是否抵扣" type="VARCHAR(2)"/>
            <column name="advances_deduction" remarks="预付款抵扣方式" type="VARCHAR(20)"/>
            <column name="advances_way" remarks="进度款付款方式" type="VARCHAR(20)"/>
            <column name="advances_month_rate" remarks="月进度付款比例" type="VARCHAR(10)"/>
            <column name="completed_rate" remarks="竣工验收支付比例" type="VARCHAR(10)"/>
            <column name="completed_cycle" remarks="竣工验收收款周期（月）" type="VARCHAR(10)"/>
            <column name="settlement_rate" remarks="结算支付比例" type="VARCHAR(10)"/>
            <column name="settlement_cycle" remarks="结算周期（月）" type="VARCHAR(10)"/>
            <column name="warranty_premium" type="VARCHAR(20)"/>
            <column name="warranty_premium_rate" remarks="保修金比例" type="VARCHAR(10)"/>
            <column name="warranty_premium_way" remarks="保修金支付方式" type="VARCHAR(20)"/>
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
            <column name="specific_pay_way" remarks="现金支付方式" type="VARCHAR(20)"/>
            <column name="advances_fund_flag" remarks="是否垫资" type="VARCHAR(2)"/>
            <column name="guarantee_way" remarks="履约担保方式" type="VARCHAR(20)"/>
            <column name="guarantee_rate" remarks="履约担保比例" type="VARCHAR(10)"/>
            <column name="guarantee_amount" remarks="履约担保金额" type="DECIMAL(20, 2)"/>
            <column name="other_guarantee_name" remarks="其他保函名称" type="VARCHAR(64)"/>
            <column name="is_been_audited_by_third_party" remarks="是否经第三方审计" type="VARCHAR(4)"/>
            <column name="land_legality_flag" remarks="项目及土地是否合法" type="VARCHAR(2)"/>
            <column name="give_up_compensate_flag" remarks="是否放弃优先受偿权" type="VARCHAR(2)"/>
            <column name="pay_rate_less_eighty_flag" remarks="付款比例是否低于百分之八十" type="VARCHAR(4)"/>
            <column name="node_more_two_month_flag" remarks="支付节点时间是否超过2个月" type="VARCHAR(4)"/>
            <column name="big_risk_measure" remarks="重大风险化解措施" type="VARCHAR(128)"/>
            <column name="presentation_user" remarks="交底人" type="VARCHAR(64)"/>
            <column name="recipient" remarks="接收人" type="VARCHAR(64)"/>
            <column name="remark" remarks="备注" type="VARCHAR(128)"/>
            <column name="supplementary_agreement_code" type="VARCHAR(50)"/>
            <column name="independent_contract_id" remarks="独立合同ID" type="BIGINT"/>
            <column name="independent_contract_type" remarks="独立合同类型：1投标总结；2补充协议；3局内分包合同"
                    type="INT"/>
            <column name="project_attachment" remarks="项目附件信息对象" type="JSON"/>
            <column name="origin_file_id" type="BIGINT"/>
            <column name="belong_id" remarks="所属源文件id" type="BIGINT"/>
            <column name="successful_time" remarks="实际中标日期" type="BIGINT"/>
            <column name="actual_signed_time" remarks="实际签约日期" type="BIGINT"/>
            <column name="worker_begin_time" remarks="合同开工日期" type="BIGINT"/>
            <column name="worker_end_time" remarks="合同竣工日期" type="BIGINT"/>
            <column name="real_work_begin_time" remarks="实际开工日期" type="BIGINT"/>
            <column name="predict_work_end_time" remarks="预计实际竣工日期" type="BIGINT"/>
            <column name="presentation_time" remarks="交底日期" type="BIGINT"/>
            <column name="break_bottom" remarks="突破底线条款" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-10">
        <createTable tableName="task">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints primaryKey="true"/>
            </column>
            <column name="relation_id" remarks="关联id" type="BIGINT"/>
            <column name="task_name" remarks="任务名称" type="VARCHAR(150)"/>
            <column name="task_type" remarks="任务类型" type="INT"/>
            <column name="init_person" remarks="发起人" type="VARCHAR(50)"/>
            <column name="handler_person" remarks="经办人" type="VARCHAR(50)"/>
            <column name="status" remarks="状态(0:待办;1:在办;2:已办)" type="INT"/>
            <column name="init_person_page_param" remarks="发起人页面参数" type="JSON"/>
            <column name="handler_person_page_param" remarks="经办人页面参数" type="JSON"/>
            <column name="create_person" remarks="创建人" type="VARCHAR(50)"/>
            <column name="update_person" remarks="修改人" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="delete_flag" remarks="是否删除(0:未删除;1:已删除)" type="INT"/>
            <column name="init_time" remarks="发起时间" type="BIGINT"/>
            <column name="finish_time" remarks="完成时间" type="BIGINT"/>
            <column name="create_time" remarks="创建时间" type="BIGINT"/>
            <column name="update_time" remarks="修改时间" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-12">
        <addPrimaryKey columnNames="id" constraintName="PRIMARY" tableName="api_retry_call"/>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-13">
        <addPrimaryKey columnNames="project_id" constraintName="PRIMARY" tableName="project_progress"/>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-14">
        <addUniqueConstraint columnNames="belong_id" constraintName="belong_id_unique_key" tableName="bureau_contract"/>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-15">
        <addUniqueConstraint columnNames="belong_id" constraintName="belong_id_unique_key"
                             tableName="bureau_supplementary_agreement"/>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-16">
        <addUniqueConstraint columnNames="independent_contract_id, id, independent_contract_type"
                             constraintName="contract_independent_contract_id_IDX" tableName="contract"/>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-17">
        <createIndex indexName="independent_union_key" tableName="project">
            <column name="independent_contract_id"/>
            <column name="independent_contract_type"/>
        </createIndex>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-18">
        <createIndex indexName="project_dept_id_normal_key" tableName="project">
            <column name="project_dept_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="liquibase-gen" id="1669255348670-19">
        <createIndex indexName="project_status_normal_key" tableName="project">
            <column name="project_status"/>
        </createIndex>
    </changeSet>
    <changeSet id="00000000000017" author="jiangmeng">
        <addColumn tableName="bid_summary">
            <column name="bid_project_code" type="VARCHAR(32)" remarks="工程中标编号"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000018" author="jiangmeng">
        <addColumn tableName="bid_summary">
            <column name="customer_level" type="VARCHAR(50)" remarks="客户级别"/>
        </addColumn>
        <addColumn tableName="bureau_contract">
            <column name="customer_level" type="VARCHAR(50)" remarks="客户级别"/>
        </addColumn>
        <addColumn tableName="bureau_supplementary_agreement">
            <column name="customer_level" type="VARCHAR(50)" remarks="客户级别"/>
        </addColumn>
        <addColumn tableName="contract">
            <column name="customer_level" type="VARCHAR(50)" remarks="客户级别"/>
        </addColumn>
        <addColumn tableName="supplementary_agreement">
            <column name="customer_level" type="VARCHAR(50)" remarks="客户级别"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000019" author="jiangmeng">
        <dropColumn tableName="bid_summary" columnName="contact_person_mobile"/>
        <addColumn tableName="bid_summary">
            <column name="contact_person_mobile" type="VARCHAR(13)" remarks="建设单位（甲方）联系人电话"/>
        </addColumn>
        <dropColumn tableName="bureau_contract" columnName="contact_person_mobile"/>
        <addColumn tableName="bureau_contract">
            <column name="contact_person_mobile" type="VARCHAR(13)" remarks="建设单位（甲方）联系人电话"/>
        </addColumn>
        <dropColumn tableName="bureau_supplementary_agreement" columnName="contact_person_mobile"/>
        <addColumn tableName="bureau_supplementary_agreement">
            <column name="contact_person_mobile" type="VARCHAR(13)" remarks="建设单位（甲方）联系人电话"/>
        </addColumn>
        <dropColumn tableName="contract" columnName="contact_person_mobile"/>
        <addColumn tableName="contract">
            <column name="contact_person_mobile" type="VARCHAR(13)" remarks="建设单位（甲方）联系人电话"/>
        </addColumn>
        <dropColumn tableName="supplementary_agreement" columnName="contact_person_mobile"/>
        <addColumn tableName="supplementary_agreement">
            <column name="contact_person_mobile" type="VARCHAR(13)" remarks="建设单位（甲方）联系人电话"/>
        </addColumn>
        <dropColumn tableName="contract" columnName="excute_manager_code"/>
        <addColumn tableName="contract">
            <column name="excute_manager_code" type="VARCHAR(13)" remarks="执行项目经理联系方式"/>
        </addColumn>
        <dropColumn tableName="supplementary_agreement" columnName="excute_manager_code"/>
        <addColumn tableName="supplementary_agreement">
            <column name="excute_manager_code" type="VARCHAR(13)" remarks="执行项目经理联系方式"/>
        </addColumn>
        <dropColumn tableName="bureau_contract" columnName="excute_manager_code"/>
        <addColumn tableName="bureau_contract">
            <column name="excute_manager_code" type="VARCHAR(13)" remarks="执行项目经理联系方式"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000020" author="jiangmeng">
        <addColumn tableName="contract">
            <column name="self_other_amount" remarks="其他" type="DECIMAL(20, 2)"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000001" author="ZhaoDong">
        <addColumn tableName="bureau_contract">
            <column name="attach" remarks="附件" type="VARCHAR(1024)"/>
        </addColumn>
    </changeSet>

    <changeSet id="00000000000021" author="jiangmeng">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/column_change.sql"/>
    </changeSet>
    <changeSet id="00000000000022" author="jiangmeng">
        <addColumn tableName="contract">
            <column name="pay_type_new" remarks="支付方式" type="VARCHAR(20)"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000002" author="ZhaoDong">
        <addColumn tableName="bureau_supplementary_agreement">
            <column name="break_bottom" remarks="突破底线条款" type="TEXT"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000003" author="ZhaoDong">
        <addColumn tableName="project_progress">
            <column name="to_finance_time" remarks="财商传参时间" type="BIGINT"/>
            <column name="finance_return_time" remarks="财商返参时间" type="BIGINT"/>
            <column name="smart_dept_name" remarks="项目部名称(智慧工地传回)" type="VARCHAR(500)"/>
            <column name="smart_dept_address" remarks="项目部地址(智慧工地传回)" type="VARCHAR(1024)"/>
            <column name="smart_query_time" remarks="智慧工地查询时间" type="BIGINT"/>
            <column name="smart_query_count" remarks="智慧工地查询次数" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="smart_approve_status" remarks="智慧工地立项状态:0立项中1立项完成" type="INT"
                    defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="to_uc_time" remarks="UC传参时间" type="BIGINT"/>
            <column name="uc_depart_status" remarks="UC创建项目部状态:0创建中1创建完成" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="finance_remarks" remarks="财商系统备注" type="VARCHAR(1024)"/>
            <column name="smart_remarks" remarks="智慧工地系统备注" type="VARCHAR(1024)"/>
            <column name="uc_remarks" remarks="UC系统备注" type="VARCHAR(1024)"/>
        </addColumn>
        <addNotNullConstraint tableName="project_progress" columnName="sign_status" columnDataType="INT"/>
        <addDefaultValue tableName="project_progress" columnName="sign_status" defaultValue="0"/>
        <addNotNullConstraint tableName="project_progress" columnName="approve_status" columnDataType="INT"/>
        <addDefaultValue tableName="project_progress" columnName="approve_status" defaultValue="0"/>
        <!--<setColumnRemarks tableName="project_progress" columnName="approve_status" remarks="立项进度:0未立项 1已推到财商 2财商已返回"/>-->
        <addNotNullConstraint tableName="project_progress" columnName="warn_status" columnDataType="INT"/>
        <addDefaultValue tableName="project_progress" columnName="warn_status" defaultValue="0"/>
    </changeSet>
    <changeSet id="00000000000004" author="ZhaoDong">
        <dropColumn tableName="project_progress" columnName="finance_return_time"/>
    </changeSet>
    <changeSet id="00000000000005" author="ZhaoDong">
        <addColumn tableName="project_progress">
            <column name="finance_return_count" remarks="财商回传次数" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000006" author="ZhaoDong">
        <addColumn tableName="project_progress">
            <column name="a8_status" remarks="A8回传状态(-1:A8未回传 0:回传一次成功 1:补充前次为空 2:a8已经修改)"
                    type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000007" author="ZhaoDong">
        <addColumn tableName="bureau_contract">
            <column name="business_type" remarks="业务类型" type="VARCHAR(20)"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000001" author="tianyuan">
        <addColumn tableName="project">
            <column name="independent_contract_no" remarks="独立合同文件编号" type="VARCHAR(50)"></column>
        </addColumn>
    </changeSet>
    <changeSet id="000000000000001" author="huangcy">
        <addColumn tableName="project">
            <column name="yunshu_org_id" remarks="云枢组织id" type="VARCHAR(64)"></column>
        </addColumn>
    </changeSet>
    <changeSet author="xinfa" id="1673502594593-1">
        <createTable tableName="attachment">
            <column autoIncrement="true" name="id" remarks="主键id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="business_id" remarks="业务id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="business_type" remarks="业务类型: 1: 通知公告; 2: 帮助中心" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="original_name" remarks="原始文件名称" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="file_id" remarks="文件id" type="VARCHAR(64)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="file_size" remarks="文件大小(bytes)" type="BIGINT"/>
            <column name="file_path" remarks="附件访问路径，oss中的objectName" type="VARCHAR(256)">
                <constraints nullable="false"/>
            </column>
            <column name="file_md5" remarks="文件MD5" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="create_at" remarks="上传时间" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="create_by" remarks="上传人" type="VARCHAR(64)"/>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="1673502594593-2">
        <createTable tableName="help_center">
            <column autoIncrement="true" name="id" remarks="主键id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="title" remarks="标题" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="status" remarks="发布状态 0: 未发布; 1: 已发布" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="publish_time" remarks="发布时间" type="BIGINT UNSIGNED"/>
            <column name="create_by" remarks="创建人" type="VARCHAR(64)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="update_at" remarks="更新时间" type="BIGINT UNSIGNED"/>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="1673502594593-3">
        <createTable tableName="notice_center">
            <column autoIncrement="true" name="id" remarks="通知公告id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="notice_title" remarks="公告标题" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="notice_type" remarks="公告类型（1：系统公告，2：业务公告）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="notice_content" remarks="公告内容" type="LONGTEXT"/>
            <column name="publish_status" remarks="发布状态（0：未发布，1：已发布）" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="publish_organization" remarks="发布单位" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="publish_time" remarks="发布时间" type="BIGINT"/>
            <column name="read_status" remarks="阅读状态（0：未读，1：已读）" type="TINYINT(3)"/>
            <column name="reading_quantity" remarks="阅读量" type="BIGINT"/>
            <column name="create_by" remarks="创建人" type="VARCHAR(64)"/>
            <column name="create_time" remarks="创建时间" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="update_time" remarks="更新时间" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="1673502594593-4">
        <addUniqueConstraint columnNames="file_md5, file_id" constraintName="index_file_id_md5" tableName="attachment"/>
        <createIndex tableName="help_center" indexName="help_center_status_IDX">
            <column name="status"/>
            <column name="update_at"/>
        </createIndex>
    </changeSet>
    <changeSet id="000000000000001" author="xinfa">
        <addNotNullConstraint tableName="notice_center" columnName="reading_quantity" columnDataType="INT" defaultNullValue="0"/>
        <addDefaultValue tableName="notice_center" columnName="reading_quantity" defaultValue="0"/>
    </changeSet>
    <changeSet author="xinfa" id="1673860240213-1">
        <createTable  tableName="user_belong_notice">
            <column autoIncrement="true" name="id" remarks="id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="notice_id" remarks="公告id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="user_uuid" remarks="用户id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="create_at" remarks="创建时间(阅读时间)" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="1673860240213-2">
        <addUniqueConstraint columnNames="notice_id, user_uuid" constraintName="user_belong_notic_un" tableName="user_belong_notice"/>
    </changeSet>
    <changeSet id="000000000000002" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/project_import.sql"/>
    </changeSet>
    <changeSet id="000000000000003" author="huangcy">
        <addColumn tableName="project">
            <column name="project_dept_abbreviation" remarks="所属项目部/指挥部简称" type="VARCHAR(50)"></column>
            <column name="execute_unit_abbreviation" remarks="执行单位简称" type="VARCHAR(50)"></column>
            <column name="source_system" remarks="来源系统" type="INT(11)"></column>
            <column name="project_desc" remarks="立项描述" type="VARCHAR(250)"></column>
            <column name="region_id_path" remarks="行政区域idpath" type="VARCHAR(256)"></column>
            <column name="project_class_id" remarks="项目分类编号" type="VARCHAR(50)"></column>
            <column name="project_class_name" remarks="项目分类名称" type="VARCHAR(50)"></column>
            <column name="project_class_id_path" remarks="项目分类idpath" type="VARCHAR(256)"></column>
        </addColumn>
    </changeSet>
    <changeSet id="000000000000003" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/set_the_project_table_source_system_value.sql"/>
    </changeSet>
    <changeSet id="000000000000004" author="xinfa">
        <addColumn tableName="bid_summary">
            <column name="execute_unit_abbreviation" remarks="执行单位简称" type="VARCHAR(50)"></column>
        </addColumn>
        <addColumn tableName="supplementary_agreement">
            <column name="execute_unit_abbreviation" remarks="执行单位简称" type="VARCHAR(50)"></column>
        </addColumn>
        <addColumn tableName="bureau_contract">
            <column name="execute_unit_abbreviation" remarks="执行单位简称" type="VARCHAR(50)"></column>
        </addColumn>

    </changeSet>

    <changeSet id="0000000000000005" author="xinfa">
        <addColumn tableName="project">
            <column name="project_status_eng" type="VARCHAR(16)" remarks="项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;"></column>
            <column name="project_status_fin" type="VARCHAR(16)" remarks="项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;"></column>
            <column name="project_status_biz" type="VARCHAR(16)" remarks="项目状态(商务): 05:未结; 06:已结"></column>
        </addColumn>
    </changeSet>
    <changeSet id="0000000000000006" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0314_project_import.sql"/>
    </changeSet>
    <changeSet id="0000000000000007" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0315_project_import.sql"/>
    </changeSet>
    <changeSet id="0000000000000008" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0316_project_update_103.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0318_project_update_3891.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0318_project_import_1996.sql"/>
    </changeSet>
    <changeSet id="0000000000000009" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0321_project_update_4686.sql"/>
    </changeSet>
    <changeSet id="0000000000000010" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0322_project_update_4686.sql"/>
    </changeSet>
    <changeSet id="0000000000000011" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0322_project_import.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0322_project_update_yunshu_org_id.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0322_project_update_finance_code_by_yunshu_id.sql"/>
    </changeSet>
    <changeSet id="0000000000000012" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0323_project_update_finance_code_by_yunshu_id_v1.sql"/>
    </changeSet>
    <changeSet id="0000000000000013" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0327_project_update_finance_code_by_yunshu_id.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0327_project_import.sql"/>
    </changeSet>
    <changeSet id="0000000000000014" author="xinfa">
        <renameColumn tableName="project" oldColumnName="execute_unit" newColumnName="execute_unit"
                      columnDataType="VARCHAR(200)" remarks="执行单位"/>
        <renameColumn tableName="project" oldColumnName="execute_unit_abbreviation"
                      newColumnName="execute_unit_abbreviation"
                      columnDataType="VARCHAR(200)" remarks="执行单位简称"/>
        <renameColumn tableName="project" oldColumnName="project_dept_abbreviation"
                      newColumnName="project_dept_abbreviation"
                      columnDataType="VARCHAR(200)" remarks="所属项目部/指挥部简称"/>
        <renameColumn tableName="bid_summary" oldColumnName="execute_unit" newColumnName="execute_unit"
                      columnDataType="VARCHAR(200)" remarks="执行单位"/>
        <renameColumn tableName="bid_summary" oldColumnName="execute_unit_abbreviation"
                      newColumnName="execute_unit_abbreviation"
                      columnDataType="VARCHAR(200)" remarks="执行单位简称"/>
        <renameColumn tableName="bureau_contract" oldColumnName="execute_unit" newColumnName="execute_unit"
                      columnDataType="VARCHAR(200)" remarks="执行单位"/>
        <renameColumn tableName="bureau_contract" oldColumnName="execute_unit_abbreviation"
                      newColumnName="execute_unit_abbreviation"
                      columnDataType="VARCHAR(200)" remarks="执行单位简称"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="execute_unit" newColumnName="execute_unit"
                      columnDataType="VARCHAR(200)" remarks="执行单位"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="execute_unit_abbreviation"
                      newColumnName="execute_unit_abbreviation"
                      columnDataType="VARCHAR(200)" remarks="执行单位简称"/>
    </changeSet>

    <changeSet id="0000000000000015" author="xinfa">
        <setTableRemarks tableName="attachment" remarks="附件信息表"/>
        <setTableRemarks tableName="bid_opening_records" remarks="开标记录表"/>
        <setTableRemarks tableName="bid_summary" remarks="投标总结记录表"/>
        <setTableRemarks tableName="bureau_contract" remarks="局内分包合同表"/>
        <setTableRemarks tableName="bureau_supplementary_agreement" remarks="局内补充协议表"/>
        <setTableRemarks tableName="contract" remarks="合同宝案表"/>
        <setTableRemarks tableName="help_center" remarks="帮助中心"/>
        <setTableRemarks tableName="notice_center" remarks="公告中心"/>
        <setTableRemarks tableName="project" remarks="项目信息表"/>
        <setTableRemarks tableName="supplementary_agreement" remarks="补充协议表"/>
        <setTableRemarks tableName="task" remarks="任务信息记录表"/>
        <setTableRemarks tableName="user_belong_notice" remarks="用户公告关联表"/>
    </changeSet>

    <changeSet id="0000000000000016" author="xinfa">
        <renameColumn tableName="bid_summary" oldColumnName="project_abbreviation"
                      newColumnName="project_abbreviation" columnDataType="VARCHAR(255)" remarks="工程简称"/>
        <renameColumn tableName="bid_summary" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="bid_summary" oldColumnName="sign_form_office"
                      newColumnName="sign_form_office" columnDataType="VARCHAR(128)" remarks="所属办事处"/>
        <renameColumn tableName="bid_summary" oldColumnName="address"
                      newColumnName="address" columnDataType="VARCHAR(255)" remarks="具体地址"/>
        <renameColumn tableName="bid_summary" oldColumnName="country"
                      newColumnName="country" columnDataType="VARCHAR(128)" remarks="国别"/>
        <renameColumn tableName="bid_summary" oldColumnName="country_project_type"
                      newColumnName="country_project_type" columnDataType="VARCHAR(32)" remarks="工程类型（国家标准）"/>
        <renameColumn tableName="bid_summary" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="bid_summary" oldColumnName="market_project_type2"
                      newColumnName="market_project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司市场口径)2"/>
        <renameColumn tableName="bid_summary" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)"/>
        <renameColumn tableName="bid_summary" oldColumnName="project_type2"
                      newColumnName="project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)2"/>
        <renameColumn tableName="bid_summary" oldColumnName="project_type3"
                      newColumnName="project_type3" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)3"/>
        <renameColumn tableName="bid_summary" oldColumnName="project_type4"
                      newColumnName="project_type4" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)4"/>
        <renameColumn tableName="bid_summary" oldColumnName="investment_projects"
                      newColumnName="investment_projects" columnDataType="VARCHAR(32)"
                      remarks="是否投资项目"/>
        <renameColumn tableName="bid_summary" oldColumnName="structural_style"
                      newColumnName="structural_style" columnDataType="VARCHAR(64)"
                      remarks="结构形式"/>
        <renameColumn tableName="bid_summary" oldColumnName="structural_style2"
                      newColumnName="structural_style2" columnDataType="VARCHAR(64)"
                      remarks="结构形式2"/>
        <renameColumn tableName="bid_summary" oldColumnName="including_steel"
                      newColumnName="including_steel" columnDataType="VARCHAR(4)"
                      remarks="是否有钢结构"/>
        <renameColumn tableName="bid_summary" oldColumnName="volume"
                      newColumnName="volume" columnDataType="VARCHAR(128)"
                      remarks="钢结构体量"/>
        <renameColumn tableName="bid_summary" oldColumnName="fabricated"
                      newColumnName="fabricated" columnDataType="VARCHAR(4)"
                      remarks="是否装配式"/>
        <renameColumn tableName="bid_summary" oldColumnName="business_type"
                      newColumnName="business_type" columnDataType="VARCHAR(32)"
                      remarks="业务类型"/>
        <renameColumn tableName="bid_summary" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="bid_summary" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方"/>
        <renameColumn tableName="bid_summary" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="bid_summary" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="bid_summary" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="bid_summary" oldColumnName="designer"
                      newColumnName="designer" columnDataType="VARCHAR(255)"
                      remarks="设计单位"/>
        <renameColumn tableName="bid_summary" oldColumnName="supervisor"
                      newColumnName="supervisor" columnDataType="VARCHAR(255)"
                      remarks="监理单位"/>
        <renameColumn tableName="bid_summary" oldColumnName="bidding_agency"
                      newColumnName="bidding_agency" columnDataType="VARCHAR(255)"
                      remarks="招标代理机构"/>
        <renameColumn tableName="bid_summary" oldColumnName="bid_opening_personnel"
                      newColumnName="bid_opening_personnel" columnDataType="VARCHAR(128)"
                      remarks="参与开标人员"/>
        <renameColumn tableName="bid_summary" oldColumnName="in_bid_type"
                      newColumnName="in_bid_type" columnDataType="VARCHAR(32)"
                      remarks="是否中标"/>
        <renameColumn tableName="bid_summary" oldColumnName="bid_project_code"
                      newColumnName="bid_project_code" columnDataType="VARCHAR(64)"
                      remarks="工程中标编号"/>
        <renameColumn tableName="bid_summary" oldColumnName="contract_mode1"
                      newColumnName="contract_mode1" columnDataType="VARCHAR(64)"
                      remarks="承包模式"/>
        <renameColumn tableName="bid_summary" oldColumnName="contract_mode2"
                      newColumnName="contract_mode2" columnDataType="VARCHAR(64)"
                      remarks="承包模式2"/>
        <renameColumn tableName="bid_summary" oldColumnName="bidding_type"
                      newColumnName="bidding_type" columnDataType="VARCHAR(64)"
                      remarks="招标模式"/>
        <renameColumn tableName="bid_summary" oldColumnName="bidding_range"
                      newColumnName="bidding_range" columnDataType="VARCHAR(1024)"
                      remarks="招标范围"/>
        <renameColumn tableName="bid_summary" oldColumnName="duration_award_type"
                      newColumnName="duration_award_type" columnDataType="VARCHAR(32)"
                      remarks="工期奖罚类型"/>
        <renameColumn tableName="bid_summary" oldColumnName="pricing_method"
                      newColumnName="pricing_method" columnDataType="VARCHAR(64)"
                      remarks="计价方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="contract_form"
                      newColumnName="contract_form" columnDataType="VARCHAR(64)"
                      remarks="合同形式"/>
        <renameColumn tableName="bid_summary" oldColumnName="labor_cost_type"
                      newColumnName="labor_cost_type" columnDataType="VARCHAR(32)"
                      remarks="人工费可调"/>
        <renameColumn tableName="bid_summary" oldColumnName="material_science_type"
                      newColumnName="material_science_type" columnDataType="VARCHAR(32)"
                      remarks="主材费可调"/>
        <renameColumn tableName="bid_summary" oldColumnName="advance_charge_type"
                      newColumnName="advance_charge_type" columnDataType="VARCHAR(32)"
                      remarks="是否有预付款"/>
        <renameColumn tableName="bid_summary" oldColumnName="payment_type"
                      newColumnName="payment_type" columnDataType="VARCHAR(128)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="be_completed_proportion"
                      newColumnName="be_completed_proportion" columnDataType="VARCHAR(22)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="bid_summary" oldColumnName="settlement_payment_proportion"
                      newColumnName="settlement_payment_proportion" columnDataType="VARCHAR(22)"
                      remarks="结算支付比例"/>
        <renameColumn tableName="bid_summary" oldColumnName="estimated_advance_amount"
                      newColumnName="estimated_advance_amount" columnDataType="VARCHAR(22)"
                      remarks="预估垫资金额"/>
        <renameColumn tableName="bid_summary" oldColumnName="warranty_money_proportion"
                      newColumnName="warranty_money_proportion" columnDataType="VARCHAR(22)"
                      remarks="保修金支付比例"/>
        <renameColumn tableName="bid_summary" oldColumnName="be_completed_cycle"
                      newColumnName="be_completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="bid_summary" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="bid_summary" oldColumnName="advance_or_not"
                      newColumnName="advance_or_not" columnDataType="VARCHAR(32)"
                      remarks="是否垫资"/>
        <renameColumn tableName="bid_summary" oldColumnName="investment_guarantee_mode"
                      newColumnName="investment_guarantee_mode" columnDataType="VARCHAR(128)"
                      remarks="投标担保方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="warranty_money"
                      newColumnName="warranty_money" columnDataType="VARCHAR(32)"
                      remarks="保修金"/>
        <renameColumn tableName="bid_summary" oldColumnName="warranty_money_mode"
                      newColumnName="warranty_money_mode" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="pay_type_new"
                      newColumnName="pay_type_new" columnDataType="VARCHAR(128)"
                      remarks="支付方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="payment_mode"
                      newColumnName="payment_mode" columnDataType="VARCHAR(128)"
                      remarks="现金支付方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="performance_guarantee_mode"
                      newColumnName="performance_guarantee_mode" columnDataType="VARCHAR(128)"
                      remarks="履约担保方式"/>
        <renameColumn tableName="bid_summary" oldColumnName="contract_style"
                      newColumnName="contract_style" columnDataType="VARCHAR(128)"
                      remarks="合同范本类型"/>
        <renameColumn tableName="bid_summary" oldColumnName="winning_project_manager_number"
                      newColumnName="winning_project_manager_number" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理注册证书编号"/>
        <renameColumn tableName="bid_summary" oldColumnName="executive_project_manager_number"
                      newColumnName="executive_project_manager_number" columnDataType="VARCHAR(128)"
                      remarks="联系方式"/>
    </changeSet>
    <!--补充协议-->
    <changeSet id="0000000000000017" author="xinfa">
        <renameColumn tableName="supplementary_agreement" oldColumnName="sign_form_office"
                      newColumnName="sign_form_office" columnDataType="VARCHAR(128)" remarks="所属办事处"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_abbreviation"
                      newColumnName="project_abbreviation" columnDataType="VARCHAR(255)" remarks="工程简称"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="address"
                      newColumnName="address" columnDataType="VARCHAR(255)" remarks="具体地址"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="country"
                      newColumnName="country" columnDataType="VARCHAR(128)" remarks="国别"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="country_project_type"
                      newColumnName="country_project_type" columnDataType="VARCHAR(32)" remarks="工程类型（国家标准）"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="market_project_type2"
                      newColumnName="market_project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司市场口径)2"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_type2"
                      newColumnName="project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)2"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_type3"
                      newColumnName="project_type3" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)3"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_type4"
                      newColumnName="project_type4" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)4"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="total_subcontracting_category"
                      newColumnName="total_subcontracting_category" columnDataType="VARCHAR(32)"
                      remarks="总分包类别"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="structural_style"
                      newColumnName="structural_style" columnDataType="VARCHAR(64)"
                      remarks="结构形式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="structural_style2"
                      newColumnName="structural_style2" columnDataType="VARCHAR(64)"
                      remarks="结构形式2"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="including_steel"
                      newColumnName="including_steel" columnDataType="VARCHAR(4)"
                      remarks="是否有钢结构"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_max_length"
                      newColumnName="project_max_length" columnDataType="VARCHAR(32)"
                      remarks="最长桩基长度"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="project_max_width"
                      newColumnName="project_max_width" columnDataType="VARCHAR(32)"
                      remarks="最大桩径"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_type"
                      newColumnName="contract_type" columnDataType="VARCHAR(64)"
                      remarks="合同类型"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="fabricated"
                      newColumnName="fabricated" columnDataType="VARCHAR(4)"
                      remarks="是否装配式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="fabricated_rate"
                      newColumnName="fabricated_rate" columnDataType="VARCHAR(255)"
                      remarks="装配率"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="is_investment_financing_driven_projects"
                      newColumnName="is_investment_financing_driven_projects" columnDataType="VARCHAR(4)"
                      remarks="是否为投融资带动项目"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="business_type"
                      newColumnName="business_type" columnDataType="VARCHAR(32)"
                      remarks="业务类型"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="investment_projects"
                      newColumnName="investment_projects" columnDataType="VARCHAR(32)"
                      remarks="是否投资项目"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方/客户母公司"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="designer"
                      newColumnName="designer" columnDataType="VARCHAR(255)"
                      remarks="设计单位"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="supervisor"
                      newColumnName="supervisor" columnDataType="VARCHAR(255)"
                      remarks="监理单位"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="signed_subject_value"
                      newColumnName="signed_subject_value" columnDataType="VARCHAR(255)"
                      remarks="签约主体"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="do_unit"
                      newColumnName="do_unit" columnDataType="VARCHAR(255)"
                      remarks="实施单位"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_optimize_ratio"
                      newColumnName="contract_optimize_ratio" columnDataType="VARCHAR(22)"
                      remarks="合同优化率"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="warranty_premium"
                      newColumnName="warranty_premium" columnDataType="VARCHAR(22)"
                      remarks="保修金"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="bid_manager"
                      newColumnName="bid_manager" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="bid_manager_code"
                      newColumnName="bid_manager_code" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理注册证书编号"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="excute_manager"
                      newColumnName="excute_manager" columnDataType="VARCHAR(128)"
                      remarks="执行项目经理"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="excute_manager_code"
                      newColumnName="excute_manager_code" columnDataType="VARCHAR(64)"
                      remarks="执行项目经理联系方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_manager"
                      newColumnName="contract_manager" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_manager_code"
                      newColumnName="contract_manager_code" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理注册证书编号"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="government_manager"
                      newColumnName="government_manager" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="government_manager_code"
                      newColumnName="government_manager_code" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理注册证书编号"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="government_manager_code"
                      newColumnName="government_manager_code" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理注册证书编号"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_mode1"
                      newColumnName="contract_mode1" columnDataType="VARCHAR(64)"
                      remarks="承包模式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_mode2"
                      newColumnName="contract_mode2" columnDataType="VARCHAR(64)"
                      remarks="承包模式2"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_scope"
                      newColumnName="contract_scope" columnDataType="TEXT"
                      remarks="合同承包范围"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="issuer_project"
                      newColumnName="issuer_project" columnDataType="TEXT"
                      remarks="发包人指定分包、独立分包的工程"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="worker_datetime_reward_punish"
                      newColumnName="worker_datetime_reward_punish" columnDataType="VARCHAR(64)"
                      remarks="工期奖罚类型"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="worker_reward_punish_appoint"
                      newColumnName="worker_reward_punish_appoint" columnDataType="varchar(512)"
                      remarks="工期奖罚条款"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_style"
                      newColumnName="contract_style" columnDataType="VARCHAR(128)"
                      remarks="合同范本类型"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="quality_guarantee"
                      newColumnName="quality_guarantee" columnDataType="VARCHAR(64)"
                      remarks="质量要求"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="reward_punish_type"
                      newColumnName="reward_punish_type" columnDataType="VARCHAR(64)"
                      remarks="质量奖罚类型"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="reward_punish_terms"
                      newColumnName="reward_punish_terms" columnDataType="TEXT"
                      remarks="质量奖罚条款"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="safety_requirement"
                      newColumnName="safety_requirement" columnDataType="TEXT"
                      remarks="安全文明施工要求"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="safety_reward_punish_terms"
                      newColumnName="safety_reward_punish_terms" columnDataType="TEXT"
                      remarks="安全文明施工奖罚条款"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="pricing_method"
                      newColumnName="pricing_method" columnDataType="VARCHAR(64)"
                      remarks="计价方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="contract_form"
                      newColumnName="contract_form" columnDataType="VARCHAR(64)"
                      remarks="合同形式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="terms"
                      newColumnName="terms" columnDataType="TEXT"
                      remarks="人工费调差条款"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="terms2"
                      newColumnName="terms2" columnDataType="TEXT"
                      remarks="主材费调差条款"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="advances_rate"
                      newColumnName="advances_rate" columnDataType="VARCHAR(16)"
                      remarks="预付款比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="advances_deduction"
                      newColumnName="advances_deduction" columnDataType="VARCHAR(255)"
                      remarks="预付款抵扣方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="advances_way"
                      newColumnName="advances_way" columnDataType="VARCHAR(64)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="advances_month_rate"
                      newColumnName="advances_month_rate" columnDataType="VARCHAR(64)"
                      remarks="月进度付款比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="completed_rate"
                      newColumnName="completed_rate" columnDataType="VARCHAR(16)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="completed_cycle"
                      newColumnName="completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="settlement_rate"
                      newColumnName="settlement_rate" columnDataType="VARCHAR(20)"
                      remarks="结算支付比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="warranty_premium_rate"
                      newColumnName="warranty_premium_rate" columnDataType="VARCHAR(50)"
                      remarks="保修金比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="warranty_premium_way"
                      newColumnName="warranty_premium_way" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="pay_type_new"
                      newColumnName="pay_type_new" columnDataType="VARCHAR(128)"
                      remarks="支付方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="specific_pay_way"
                      newColumnName="specific_pay_way" columnDataType="VARCHAR(128)"
                      remarks="现金支付方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="guarantee_way"
                      newColumnName="guarantee_way" columnDataType="VARCHAR(64)"
                      remarks="履约担保方式"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="guarantee_rate"
                      newColumnName="guarantee_rate" columnDataType="VARCHAR(64)"
                      remarks="履约担保比例"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="big_risk_measure"
                      newColumnName="big_risk_measure" columnDataType="TEXT"
                      remarks="重大风险化解措施"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="presentation_user"
                      newColumnName="presentation_user" columnDataType="VARCHAR(255)"
                      remarks="交底人"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="recipient"
                      newColumnName="recipient" columnDataType="TEXT"
                      remarks="接收人"/>
        <renameColumn tableName="supplementary_agreement" oldColumnName="remark"
                      newColumnName="remark" columnDataType="TEXT"
                      remarks="备注"/>

    </changeSet>
    <!--局内分包合同-->
    <changeSet id="0000000000000018" author="xinfa">
        <renameColumn tableName="bureau_contract" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="bureau_contract" oldColumnName="address"
                      newColumnName="address" columnDataType="VARCHAR(255)" remarks="具体地址"/>
        <renameColumn tableName="bureau_contract" oldColumnName="country"
                      newColumnName="country" columnDataType="VARCHAR(128)" remarks="国别"/>
        <renameColumn tableName="bureau_contract" oldColumnName="country_project_type"
                      newColumnName="country_project_type" columnDataType="VARCHAR(32)" remarks="工程类型（国家标准）"/>
        <renameColumn tableName="bureau_contract" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="bureau_contract" oldColumnName="market_project_type2"
                      newColumnName="market_project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司市场口径)2"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_type2"
                      newColumnName="project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)2"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_type3"
                      newColumnName="project_type3" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)3"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_type4"
                      newColumnName="project_type4" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)4"/>
        <renameColumn tableName="bureau_contract" oldColumnName="total_subcontracting_category"
                      newColumnName="total_subcontracting_category" columnDataType="VARCHAR(32)"
                      remarks="总分包类别"/>
        <renameColumn tableName="bureau_contract" oldColumnName="structural_style"
                      newColumnName="structural_style" columnDataType="VARCHAR(64)"
                      remarks="结构形式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="structural_style2"
                      newColumnName="structural_style2" columnDataType="VARCHAR(64)"
                      remarks="结构形式2"/>
        <renameColumn tableName="bureau_contract" oldColumnName="including_steel"
                      newColumnName="including_steel" columnDataType="VARCHAR(4)"
                      remarks="是否有钢结构"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_maxLength"
                      newColumnName="project_maxLength" columnDataType="VARCHAR(32)"
                      remarks="最长桩基长度"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_max_width"
                      newColumnName="project_max_width" columnDataType="VARCHAR(32)"
                      remarks="最大桩径"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_type"
                      newColumnName="contract_type" columnDataType="VARCHAR(64)"
                      remarks="合同类型"/>
        <renameColumn tableName="bureau_contract" oldColumnName="fabricated"
                      newColumnName="fabricated" columnDataType="VARCHAR(4)"
                      remarks="是否装配式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="bureau_contract" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方/客户母公司"/>
        <renameColumn tableName="bureau_contract" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="bureau_contract" oldColumnName="designer"
                      newColumnName="designer" columnDataType="VARCHAR(255)"
                      remarks="设计单位"/>
        <renameColumn tableName="bureau_contract" oldColumnName="supervisor"
                      newColumnName="supervisor" columnDataType="VARCHAR(255)"
                      remarks="监理单位"/>
        <renameColumn tableName="bureau_contract" oldColumnName="signed_subject_value"
                      newColumnName="signed_subject_value" columnDataType="VARCHAR(255)"
                      remarks="签约主体"/>
        <renameColumn tableName="bureau_contract" oldColumnName="do_unit"
                      newColumnName="do_unit" columnDataType="VARCHAR(255)"
                      remarks="实施单位"/>
        <renameColumn tableName="bureau_contract" oldColumnName="bid_manager"
                      newColumnName="bid_manager" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理"/>
        <renameColumn tableName="bureau_contract" oldColumnName="bid_manager_code"
                      newColumnName="bid_manager_code" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理注册证书编号"/>
        <renameColumn tableName="bureau_contract" oldColumnName="excute_manager"
                      newColumnName="excute_manager" columnDataType="VARCHAR(128)"
                      remarks="执行项目经理"/>
        <renameColumn tableName="bureau_contract" oldColumnName="excute_manager_code"
                      newColumnName="excute_manager_code" columnDataType="VARCHAR(64)"
                      remarks="执行项目经理联系方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_manager"
                      newColumnName="contract_manager" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_manager_code"
                      newColumnName="contract_manager_code" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理注册证书编号"/>
        <renameColumn tableName="bureau_contract" oldColumnName="government_manager"
                      newColumnName="government_manager" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理"/>
        <renameColumn tableName="bureau_contract" oldColumnName="government_manager_code"
                      newColumnName="government_manager_code" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理注册证书编号"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_mode1"
                      newColumnName="contract_mode1" columnDataType="VARCHAR(64)"
                      remarks="承包模式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_mode2"
                      newColumnName="contract_mode2" columnDataType="VARCHAR(64)"
                      remarks="承包模式2"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_scope"
                      newColumnName="contract_scope" columnDataType="TEXT"
                      remarks="合同承包范围"/>
        <renameColumn tableName="bureau_contract" oldColumnName="issuer_project"
                      newColumnName="issuer_project" columnDataType="TEXT"
                      remarks="发包人指定分包、独立分包的工程"/>
        <renameColumn tableName="bureau_contract" oldColumnName="worker_date_reward_punish"
                      newColumnName="worker_date_reward_punish" columnDataType="VARCHAR(64)"
                      remarks="工期奖罚类型"/>
        <renameColumn tableName="bureau_contract" oldColumnName="worker_reward_punish_appoint"
                      newColumnName="worker_reward_punish_appoint" columnDataType="VARCHAR(512)"
                      remarks="工期奖罚条款"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_style"
                      newColumnName="contract_style" columnDataType="VARCHAR(128)"
                      remarks="合同范本类型"/>
        <renameColumn tableName="bureau_contract" oldColumnName="quality_guarantee"
                      newColumnName="quality_guarantee" columnDataType="VARCHAR(64)"
                      remarks="质量要求"/>
        <renameColumn tableName="bureau_contract" oldColumnName="reward_punish_type"
                      newColumnName="reward_punish_type" columnDataType="VARCHAR(64)"
                      remarks="质量奖罚类型"/>
        <renameColumn tableName="bureau_contract" oldColumnName="reward_punish_terms"
                      newColumnName="reward_punish_terms" columnDataType="TEXT"
                      remarks="质量奖罚条款"/>
        <renameColumn tableName="bureau_contract" oldColumnName="safety_requirement"
                      newColumnName="safety_requirement" columnDataType="TEXT"
                      remarks="安全文明施工要求"/>
        <renameColumn tableName="bureau_contract" oldColumnName="safety_reward_punish_terms"
                      newColumnName="safety_reward_punish_terms" columnDataType="TEXT"
                      remarks="安全文明施工奖罚条款"/>
        <renameColumn tableName="bureau_contract" oldColumnName="pricing_method"
                      newColumnName="pricing_method" columnDataType="VARCHAR(64)"
                      remarks="计价方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="contract_form"
                      newColumnName="contract_form" columnDataType="VARCHAR(64)"
                      remarks="合同形式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="change_way"
                      newColumnName="change_way" columnDataType="VARCHAR(64)"
                      remarks="人工费调差形式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="terms"
                      newColumnName="terms" columnDataType="TEXT"
                      remarks="人工费调差条款"/>
        <renameColumn tableName="bureau_contract" oldColumnName="terms2"
                      newColumnName="terms2" columnDataType="VARCHAR(2048)"
                      remarks="主材费调差条款"/>
        <renameColumn tableName="bureau_contract" oldColumnName="advances_way"
                      newColumnName="advances_way" columnDataType="VARCHAR(64)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="completed_rate"
                      newColumnName="completed_rate" columnDataType="VARCHAR(16)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="bureau_contract" oldColumnName="completed_cycle"
                      newColumnName="completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="bureau_contract" oldColumnName="settlement_rate"
                      newColumnName="settlement_rate" columnDataType="VARCHAR(20)"
                      remarks="结算支付比例"/>
        <renameColumn tableName="bureau_contract" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="bureau_contract" oldColumnName="warranty_premium"
                      newColumnName="warranty_premium" columnDataType="VARCHAR(22)"
                      remarks="保修金"/>
        <renameColumn tableName="bureau_contract" oldColumnName="warranty_premium_rate"
                      newColumnName="warranty_premium_rate" columnDataType="VARCHAR(50)"
                      remarks="保修金比例"/>
        <renameColumn tableName="bureau_contract" oldColumnName="warranty_premium_way"
                      newColumnName="warranty_premium_way" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="pay_type_new"
                      newColumnName="pay_type_new" columnDataType="VARCHAR(128)"
                      remarks="支付方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="no_cash_pay_way"
                      newColumnName="no_cash_pay_way" columnDataType="VARCHAR(128)"
                      remarks="非现金支付方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="guarantee_way"
                      newColumnName="guarantee_way" columnDataType="VARCHAR(64)"
                      remarks="履约担保方式"/>
        <renameColumn tableName="bureau_contract" oldColumnName="commercial_ticket_proportion"
                      newColumnName="commercial_ticket_proportion" columnDataType="VARCHAR(22)"
                      remarks="商票比例"/>
        <renameColumn tableName="bureau_contract" oldColumnName="big_risk_measure"
                      newColumnName="big_risk_measure" columnDataType="TEXT"
                      remarks="重大风险化解措施"/>
        <renameColumn tableName="bureau_contract" oldColumnName="presentation_user"
                      newColumnName="presentation_user" columnDataType="VARCHAR(255)"
                      remarks="交底人"/>
        <renameColumn tableName="bureau_contract" oldColumnName="recipient"
                      newColumnName="recipient" columnDataType="VARCHAR(512)"
                      remarks="接收人"/>
        <renameColumn tableName="bureau_contract" oldColumnName="remark"
                      newColumnName="remark" columnDataType="TEXT"
                      remarks="备注"/>
        <renameColumn tableName="bureau_contract" oldColumnName="project_short_name"
                      newColumnName="project_short_name" columnDataType="VARCHAR(255)"
                      remarks="工程简称"/>

    </changeSet>
    <!--合同定案-->
    <changeSet id="0000000000000019" author="xinfa">
        <renameColumn tableName="contract" oldColumnName="project_short_name"
                      newColumnName="project_short_name" columnDataType="VARCHAR(255)"
                      remarks="工程简称"/>
        <renameColumn tableName="contract" oldColumnName="sign_form_office"
                      newColumnName="sign_form_office" columnDataType="VARCHAR(128)" remarks="所属办事处"/>
        <renameColumn tableName="contract" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="contract" oldColumnName="address"
                      newColumnName="address" columnDataType="VARCHAR(255)" remarks="具体地址"/>
        <renameColumn tableName="contract" oldColumnName="country"
                      newColumnName="country" columnDataType="VARCHAR(128)" remarks="国别"/>
        <renameColumn tableName="contract" oldColumnName="country_project_type"
                      newColumnName="country_project_type" columnDataType="VARCHAR(32)" remarks="工程类型（国家标准）"/>
        <renameColumn tableName="contract" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="contract" oldColumnName="market_project_type2"
                      newColumnName="market_project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司市场口径)2"/>
        <renameColumn tableName="contract" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)"/>
        <renameColumn tableName="contract" oldColumnName="project_type2"
                      newColumnName="project_type2" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)2"/>
        <renameColumn tableName="contract" oldColumnName="project_type3"
                      newColumnName="project_type3" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)3"/>
        <renameColumn tableName="contract" oldColumnName="project_type4"
                      newColumnName="project_type4" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)4"/>
        <renameColumn tableName="contract" oldColumnName="total_subcontracting_category"
                      newColumnName="total_subcontracting_category" columnDataType="VARCHAR(32)"
                      remarks="总分包类别"/>
        <renameColumn tableName="contract" oldColumnName="structural_style"
                      newColumnName="structural_style" columnDataType="VARCHAR(64)"
                      remarks="结构形式"/>
        <renameColumn tableName="contract" oldColumnName="structural_style2"
                      newColumnName="structural_style2" columnDataType="VARCHAR(64)"
                      remarks="结构形式2"/>
        <renameColumn tableName="contract" oldColumnName="including_steel"
                      newColumnName="including_steel" columnDataType="VARCHAR(4)"
                      remarks="是否有钢结构"/>
        <renameColumn tableName="contract" oldColumnName="project_max_length"
                      newColumnName="project_max_length" columnDataType="VARCHAR(32)"
                      remarks="最长桩基长度"/>
        <renameColumn tableName="contract" oldColumnName="project_max_width"
                      newColumnName="project_max_width" columnDataType="VARCHAR(32)"
                      remarks="最大桩径"/>
        <renameColumn tableName="contract" oldColumnName="contract_type"
                      newColumnName="contract_type" columnDataType="VARCHAR(64)"
                      remarks="合同类型"/>
        <renameColumn tableName="contract" oldColumnName="fabricated"
                      newColumnName="fabricated" columnDataType="VARCHAR(4)"
                      remarks="是否装配式"/>
        <renameColumn tableName="contract" oldColumnName="is_investment_financing_driven_projects"
                      newColumnName="is_investment_financing_driven_projects" columnDataType="VARCHAR(4)"
                      remarks="是否为投融资带动项目"/>
        <renameColumn tableName="contract" oldColumnName="business_type"
                      newColumnName="business_type" columnDataType="VARCHAR(32)"
                      remarks="业务类型"/>
        <renameColumn tableName="contract" oldColumnName="investment_projects"
                      newColumnName="investment_projects" columnDataType="VARCHAR(32)"
                      remarks="是否投资项目"/>
        <renameColumn tableName="contract" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="contract" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方/客户母公司"/>
        <renameColumn tableName="contract" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="contract" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="contract" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="contract" oldColumnName="designer"
                      newColumnName="designer" columnDataType="VARCHAR(255)"
                      remarks="设计单位"/>
        <renameColumn tableName="contract" oldColumnName="supervisor"
                      newColumnName="supervisor" columnDataType="VARCHAR(255)"
                      remarks="监理单位"/>
        <renameColumn tableName="contract" oldColumnName="signed_subject_value"
                      newColumnName="signed_subject_value" columnDataType="VARCHAR(255)"
                      remarks="签约主体"/>
        <renameColumn tableName="contract" oldColumnName="do_unit"
                      newColumnName="do_unit" columnDataType="VARCHAR(255)"
                      remarks="实施单位"/>
        <renameColumn tableName="contract" oldColumnName="bid_manager"
                      newColumnName="bid_manager" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理"/>
        <renameColumn tableName="contract" oldColumnName="bid_manager_code"
                      newColumnName="bid_manager_code" columnDataType="VARCHAR(128)"
                      remarks="中标项目经理注册证书编号"/>
        <renameColumn tableName="contract" oldColumnName="excute_manager"
                      newColumnName="excute_manager" columnDataType="VARCHAR(128)"
                      remarks="执行项目经理"/>
        <renameColumn tableName="contract" oldColumnName="excute_manager_code"
                      newColumnName="excute_manager_code" columnDataType="VARCHAR(64)"
                      remarks="执行项目经理联系方式"/>
        <renameColumn tableName="contract" oldColumnName="contract_manager"
                      newColumnName="contract_manager" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理"/>
        <renameColumn tableName="contract" oldColumnName="contract_manager_code"
                      newColumnName="contract_manager_code" columnDataType="VARCHAR(128)"
                      remarks="合同项目经理注册证书编号"/>
        <renameColumn tableName="contract" oldColumnName="government_manager"
                      newColumnName="government_manager" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理"/>
        <renameColumn tableName="contract" oldColumnName="government_manager_code"
                      newColumnName="government_manager_code" columnDataType="VARCHAR(128)"
                      remarks="政府备案项目经理注册证书编号"/>
        <renameColumn tableName="contract" oldColumnName="contract_mode1"
                      newColumnName="contract_mode1" columnDataType="VARCHAR(64)"
                      remarks="承包模式"/>
        <renameColumn tableName="contract" oldColumnName="contract_mode2"
                      newColumnName="contract_mode2" columnDataType="VARCHAR(64)"
                      remarks="承包模式2"/>
        <renameColumn tableName="contract" oldColumnName="contract_scope"
                      newColumnName="contract_scope" columnDataType="TEXT"
                      remarks="合同承包范围"/>
        <renameColumn tableName="contract" oldColumnName="issuer_project"
                      newColumnName="issuer_project" columnDataType="TEXT"
                      remarks="发包人指定分包、独立分包的工程"/>
        <renameColumn tableName="contract" oldColumnName="worker_date_reward_punish"
                      newColumnName="worker_date_reward_punish" columnDataType="VARCHAR(64)"
                      remarks="工期奖罚类型"/>
        <renameColumn tableName="contract" oldColumnName="worker_reward_punish_appoint"
                      newColumnName="worker_reward_punish_appoint" columnDataType="VARCHAR(512)"
                      remarks="工期奖罚条款"/>
        <renameColumn tableName="contract" oldColumnName="contract_style"
                      newColumnName="contract_style" columnDataType="VARCHAR(128)"
                      remarks="合同范本类型"/>
        <renameColumn tableName="contract" oldColumnName="quality_guarantee"
                      newColumnName="quality_guarantee" columnDataType="VARCHAR(64)"
                      remarks="质量要求"/>
        <renameColumn tableName="contract" oldColumnName="reward_punish_type"
                      newColumnName="reward_punish_type" columnDataType="VARCHAR(64)"
                      remarks="质量奖罚类型"/>
        <renameColumn tableName="contract" oldColumnName="reward_punish_terms"
                      newColumnName="reward_punish_terms" columnDataType="TEXT"
                      remarks="质量奖罚条款"/>
        <renameColumn tableName="contract" oldColumnName="safety_requirement"
                      newColumnName="safety_requirement" columnDataType="TEXT"
                      remarks="安全文明施工要求"/>
        <renameColumn tableName="contract" oldColumnName="safety_reward_punish_terms"
                      newColumnName="safety_reward_punish_terms" columnDataType="TEXT"
                      remarks="安全文明施工奖罚条款"/>
        <renameColumn tableName="contract" oldColumnName="pricing_method"
                      newColumnName="pricing_method" columnDataType="VARCHAR(64)"
                      remarks="计价方式"/>
        <renameColumn tableName="contract" oldColumnName="contract_form"
                      newColumnName="contract_form" columnDataType="VARCHAR(64)"
                      remarks="合同形式"/>
        <renameColumn tableName="contract" oldColumnName="advances_way"
                      newColumnName="advances_way" columnDataType="VARCHAR(64)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="contract" oldColumnName="advances_month_rate"
                      newColumnName="advances_month_rate" columnDataType="VARCHAR(64)"
                      remarks="月进度付款比例"/>
        <renameColumn tableName="contract" oldColumnName="completed_rate"
                      newColumnName="completed_rate" columnDataType="VARCHAR(16)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="contract" oldColumnName="completed_cycle"
                      newColumnName="completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="contract" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="contract" oldColumnName="warranty_premium_way"
                      newColumnName="warranty_premium_way" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="contract" oldColumnName="project_short_name"
                      newColumnName="project_short_name" columnDataType="VARCHAR(255)"
                      remarks="工程简称"/>
        <renameColumn tableName="contract" oldColumnName="contract_optimize_ratio"
                      newColumnName="contract_optimize_ratio" columnDataType="VARCHAR(22)"
                      remarks="合同优化率"/>

    </changeSet>
    <!--局内补充协议-->
    <changeSet id="0000000000000020" author="xinfa">
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="business_type"
                      newColumnName="business_type" columnDataType="VARCHAR(32)"
                      remarks="业务类型"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方/客户母公司"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="warranty_premium"
                      newColumnName="warranty_premium" columnDataType="VARCHAR(22)"
                      remarks="保修金"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="pricing_method"
                      newColumnName="pricing_method" columnDataType="VARCHAR(64)"
                      remarks="计价方式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="contract_form"
                      newColumnName="contract_form" columnDataType="VARCHAR(64)"
                      remarks="合同形式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="advances_way"
                      newColumnName="advances_way" columnDataType="VARCHAR(64)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="advances_month_rate"
                      newColumnName="advances_month_rate" columnDataType="VARCHAR(64)"
                      remarks="月进度付款比例"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="completed_rate"
                      newColumnName="completed_rate" columnDataType="VARCHAR(16)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="completed_cycle"
                      newColumnName="completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="settlement_rate"
                      newColumnName="settlement_rate" columnDataType="VARCHAR(20)"
                      remarks="结算支付比例"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="warranty_premium_rate"
                      newColumnName="warranty_premium_rate" columnDataType="VARCHAR(50)"
                      remarks="保修金比例"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="warranty_premium_way"
                      newColumnName="warranty_premium_way" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="pay_type_new"
                      newColumnName="pay_type_new" columnDataType="VARCHAR(128)"
                      remarks="支付方式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="specific_pay_way"
                      newColumnName="specific_pay_way" columnDataType="VARCHAR(128)"
                      remarks="现金支付方式"/>
        <renameColumn tableName="bureau_supplementary_agreement" oldColumnName="guarantee_way"
                      newColumnName="guarantee_way" columnDataType="VARCHAR(64)"
                      remarks="履约担保方式"/>
    </changeSet>
    <!--项目表-->
    <changeSet id="0000000000000021" author="xinfa">
        <renameColumn tableName="project" oldColumnName="contact_person"
                      newColumnName="contact_person" columnDataType="VARCHAR(255)"
                      remarks="建设单位（甲方）联系人"/>
        <renameColumn tableName="project" oldColumnName="contact_person_mobile"
                      newColumnName="contact_person_mobile" columnDataType="VARCHAR(64)"
                      remarks="建设单位（甲方）联系人电话"/>
        <renameColumn tableName="project" oldColumnName="country_project_type"
                      newColumnName="country_project_type" columnDataType="VARCHAR(32)" remarks="工程类型（国家标准）"/>
        <renameColumn tableName="project" oldColumnName="investment_projects"
                      newColumnName="investment_projects" columnDataType="VARCHAR(32)"
                      remarks="是否投资项目"/>
        <renameColumn tableName="project" oldColumnName="business_type"
                      newColumnName="business_type" columnDataType="VARCHAR(32)"
                      remarks="业务类型"/>
        <renameColumn tableName="project" oldColumnName="designer"
                      newColumnName="designer" columnDataType="VARCHAR(255)"
                      remarks="设计单位"/>
        <renameColumn tableName="project" oldColumnName="supervisor"
                      newColumnName="supervisor" columnDataType="VARCHAR(255)"
                      remarks="监理单位"/>
        <renameColumn tableName="project" oldColumnName="customer_name"
                      newColumnName="customer_name" columnDataType="VARCHAR(255)"
                      remarks="客户名称"/>
        <renameColumn tableName="project" oldColumnName="superior_company_name"
                      newColumnName="superior_company_name" columnDataType="VARCHAR(255)"
                      remarks="上级相关方/客户母公司"/>
        <renameColumn tableName="project" oldColumnName="enterprise_type"
                      newColumnName="enterprise_type" columnDataType="VARCHAR(255)"
                      remarks="客户企业性质"/>
        <renameColumn tableName="project" oldColumnName="signed_subject_value"
                      newColumnName="signed_subject_value" columnDataType="VARCHAR(255)"
                      remarks="签约主体"/>
        <renameColumn tableName="project" oldColumnName="do_unit"
                      newColumnName="do_unit" columnDataType="VARCHAR(255)"
                      remarks="实施单位"/>
        <renameColumn tableName="project" oldColumnName="worker_date_reward_punish"
                      newColumnName="worker_date_reward_punish" columnDataType="VARCHAR(64)"
                      remarks="工期奖罚类型"/>
        <renameColumn tableName="project" oldColumnName="worker_reward_punish_appoint"
                      newColumnName="worker_reward_punish_appoint" columnDataType="varchar(512)"
                      remarks="工期奖罚条款"/>
        <renameColumn tableName="project" oldColumnName="contract_scope"
                      newColumnName="contract_scope" columnDataType="TEXT"
                      remarks="合同承包范围"/>
        <renameColumn tableName="project" oldColumnName="issuer_project"
                      newColumnName="issuer_project" columnDataType="TEXT"
                      remarks="发包人指定分包、独立分包的工程"/>
        <renameColumn tableName="project" oldColumnName="quality_guarantee"
                      newColumnName="quality_guarantee" columnDataType="VARCHAR(64)"
                      remarks="质量要求"/>
        <renameColumn tableName="project" oldColumnName="reward_punish_type"
                      newColumnName="reward_punish_type" columnDataType="VARCHAR(64)"
                      remarks="质量奖罚类型"/>
        <renameColumn tableName="project" oldColumnName="reward_punish_terms"
                      newColumnName="reward_punish_terms" columnDataType="TEXT"
                      remarks="质量奖罚条款"/>
        <renameColumn tableName="project" oldColumnName="safety_requirement"
                      newColumnName="safety_requirement" columnDataType="TEXT"
                      remarks="安全文明施工要求"/>
        <renameColumn tableName="project" oldColumnName="safety_reward_punish_terms"
                      newColumnName="safety_reward_punish_terms" columnDataType="TEXT"
                      remarks="安全文明施工奖罚条款"/>
        <renameColumn tableName="project" oldColumnName="advances_way"
                      newColumnName="advances_way" columnDataType="VARCHAR(64)"
                      remarks="进度款付款方式"/>
        <renameColumn tableName="project" oldColumnName="pay_type_new"
                      newColumnName="pay_type_new" columnDataType="VARCHAR(128)"
                      remarks="支付方式"/>
        <renameColumn tableName="project" oldColumnName="completed_rate"
                      newColumnName="completed_rate" columnDataType="VARCHAR(16)"
                      remarks="竣工验收支付比例"/>
        <renameColumn tableName="project" oldColumnName="completed_cycle"
                      newColumnName="completed_cycle" columnDataType="VARCHAR(64)"
                      remarks="竣工验收收款周期（月）"/>
        <renameColumn tableName="project" oldColumnName="settlement_rate"
                      newColumnName="settlement_rate" columnDataType="VARCHAR(20)"
                      remarks="结算支付比例"/>
        <renameColumn tableName="project" oldColumnName="settlement_cycle"
                      newColumnName="settlement_cycle" columnDataType="VARCHAR(64)"
                      remarks="结算周期（月）"/>
        <renameColumn tableName="project" oldColumnName="warranty_premium"
                      newColumnName="warranty_premium" columnDataType="VARCHAR(22)"
                      remarks="保修金"/>
        <renameColumn tableName="project" oldColumnName="warranty_premium_rate"
                      newColumnName="warranty_premium_rate" columnDataType="VARCHAR(50)"
                      remarks="保修金比例"/>
        <renameColumn tableName="project" oldColumnName="warranty_premium_way"
                      newColumnName="warranty_premium_way" columnDataType="VARCHAR(128)"
                      remarks="保修金支付方式"/>
        <renameColumn tableName="project" oldColumnName="guarantee_way"
                      newColumnName="guarantee_way" columnDataType="VARCHAR(64)"
                      remarks="履约担保方式"/>
        <renameColumn tableName="project" oldColumnName="project_abbreviation"
                      newColumnName="project_abbreviation" columnDataType="VARCHAR(255)" remarks="工程简称"/>
        <renameColumn tableName="project" oldColumnName="project_belong"
                      newColumnName="project_belong" columnDataType="VARCHAR(128)" remarks="工程属地"/>
        <renameColumn tableName="project" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="project" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(32)"
                      remarks="工程类型(总公司综合口径)"/>
    </changeSet>

    <!--开标记录表-->
    <changeSet id="0000000000000022" author="xinfa">
        <renameColumn tableName="bid_opening_records" oldColumnName="duration"
                      newColumnName="duration" columnDataType="VARCHAR(64)"
                      remarks="工期"/>
        <renameColumn tableName="bid_opening_records" oldColumnName="data_from"
                      newColumnName="data_from" columnDataType="VARCHAR(128)"
                      remarks="Y:我司 N:竞争对手"/>

    </changeSet>

    <!--项目表拼接字段加长-->
    <changeSet id="0000000000000023" author="huangchunyang">
        <renameColumn tableName="project" oldColumnName="contract_mode"
                      newColumnName="contract_mode" columnDataType="VARCHAR(128)"
                      remarks="承包模式"/>
        <renameColumn tableName="project" oldColumnName="project_address"
                      newColumnName="project_address" columnDataType="VARCHAR(360)"
                      remarks="项目地址"/>
        <renameColumn tableName="project" oldColumnName="market_project_type"
                      newColumnName="market_project_type" columnDataType="VARCHAR(128)"
                      remarks="工程类型（总公司市场口径）"/>
        <renameColumn tableName="project" oldColumnName="project_type"
                      newColumnName="project_type" columnDataType="VARCHAR(128)"
                      remarks="工程类型(总公司综合口径)"/>
    </changeSet>

    <changeSet author="DELL (generated)" id="1682234087873-12">
        <createTable tableName="msg_push_log">
            <column name="msg_id" remarks="消息id" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="production_time" remarks="消息生产时间" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="msg_content" remarks="消息内容" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="consumption_time" remarks="消息消费时间" type="BIGINT UNSIGNED"/>
            <column defaultValueNumeric="0" name="retry_count" remarks="消息重试次数" type="INT"/>
            <column name="consumption_result" remarks="最终消费结果: 0:失败；1:成功;" type="BIT(1)"/>
            <column name="create_at" remarks="创建时间(消息推送时间)" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="update_at" remarks="消息更新时间" type="BIGINT UNSIGNED"/>
            <column name="remark" remarks="备注信息" type="VARCHAR(512)"/>
        </createTable>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-15">
        <createTable tableName="project_event">
            <column autoIncrement="true" name="id" remarks="主键id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column name="event_code" remarks="事件code" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="event_name" remarks="事件名称" type="VARCHAR(64)">
                <constraints nullable="false"/>
            </column>
            <column name="event_desc" remarks="事件描述" type="VARCHAR(256)"/>
            <column name="business_column" remarks="业务字段" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="event_api" remarks="事件查询API" type="VARCHAR(256)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT UNSIGNED"/>
            <column name="create_by" remarks="创建用户" type="VARCHAR(64)"/>
            <column name="update_at" remarks="修改时间" type="BIGINT UNSIGNED"/>
            <column name="update_by" remarks="修改用户" type="VARCHAR(64)"/>
            <column defaultValueNumeric="1" name="status" remarks="状态: 0:删除; 1:正常;" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-16">
        <createTable tableName="project_event_subscribe">
            <column autoIncrement="true" name="consumer_id" remarks="消费对象ID" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column name="subscriber" remarks="订阅者，唯一" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="http" name="protocol" remarks="协议类型: http; https" type="VARCHAR(100)"/>
            <column name="certificate" remarks="证书" type="BLOB"/>
            <column name="push_url" remarks="推送url" type="VARCHAR(256)"/>
            <column name="event_ids" remarks="事件类型：事件id,逗号分隔" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
            <column name="responsible" remarks="系统负责人" type="VARCHAR(64)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT UNSIGNED"/>
            <column name="create_by" remarks="创建用户" type="VARCHAR(64)"/>
            <column name="update_at" remarks="修改时间" type="BIGINT UNSIGNED"/>
            <column name="update_by" remarks="修改用户" type="VARCHAR(64)"/>
            <column defaultValueNumeric="1" name="status" remarks="状态 0:删除; 1:正常" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-25">
        <addPrimaryKey columnNames="msg_id" constraintName="PRIMARY" tableName="msg_push_log"/>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-33">
        <addUniqueConstraint columnNames="event_code" constraintName="project_event_code_un" tableName="project_event"/>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-34">
        <addUniqueConstraint columnNames="event_name" constraintName="project_event_name_un" tableName="project_event"/>
    </changeSet>
    <changeSet author="DELL (generated)" id="1682234087873-35">
        <addUniqueConstraint columnNames="subscriber" constraintName="project_event_subscribe_un"
                             tableName="project_event_subscribe"/>
    </changeSet>

    <changeSet id="0000000000000025" author="huangchunyang">
        <!--初始化事件表，订阅表起始自增id-->
        <sql>alter table project_event AUTO_INCREMENT= 100001 </sql>
        <sql>alter table project_event_subscribe AUTO_INCREMENT= 100001 </sql>
    </changeSet>
    <changeSet id="0000000000000026" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/project_event_init.sql"/>
    </changeSet>

    <changeSet id="0000000000000027" author="xinfa">
        <addColumn tableName="project">
            <column name="smart_site_approval_at" type="bigint" remarks="智慧工地项目创建时间"/>
            <column name="project_structure" type="varchar(256)" remarks="工程结构"/>
            <column name="project_range" type="varchar(256)" remarks="施工范围"/>
            <column name="project_description" type="varchar(512)" remarks="项目概述(智慧工地)"/>
            <column name="smart_site_project_description" type="varchar(512)" remarks="智慧工地项目概述"/>
            <column name="has_assembled" type="tinyint" remarks="是否有装配项 1:是; 0:否"/>
            <column name="assembled_info" type="varchar(128)" remarks="装配内容"/>
            <column name="assembled_ratio" type="varchar(64)" remarks="装配率"/>
            <column name="assembled_area" type="varchar(64)" remarks="装配面积"/>
            <column name="is_innovation" type="tinyint" remarks="是否是创新项目 1:是; 0:否"/>
            <column name="deputy" type="varchar(128)" remarks="代建单位名称"/>
            <column name="subpackage_info" type="varchar(1024)" remarks="施工方分包信息,json"/>
            <!--            <column name="contract_pm" type="varchar(64)" remarks="合同项目经理名称"/>-->
            <!--            <column name="contract_telephone" type="varchar(64)" remarks="合同项目经理电话"/>-->
            <!--            <column name="operator_pm" type="varchar(64)" remarks="执行项目经理"/>-->
            <!--            <column name="operator_telephone" type="varchar(64)" remarks="执行项目经理电话"/>-->
            <!--            <column name="bid_pm" type="varchar(64)" remarks="中标项目经理"/>-->
            <!--            <column name="bid_telephone" type="varchar(64)" remarks="中标项目经理电话"/>-->
            <!--            <column name="gov_pm" type="varchar(64)" remarks="政府备案项目经理"/>-->
            <!--            <column name="gov_telephone" type="varchar(64)" remarks="政府备案项目经理电话"/>-->
            <!--            <column name="project_approval_date" type="bigint" remarks="项目进场日期"/>-->
            <column name="owner_start_date" type="bigint" remarks="业主下发开工令日期"/>
            <column name="manager_start_date" type="bigint" remarks="监理下发开工令日期"/>
            <column name="record_date" type="bigint" remarks="竣工备案时间"/>
        </addColumn>
    </changeSet>

    <changeSet id="0000000000000028" author="xinfa">
        <createTable tableName="project_yunshu_revision_record">
            <column autoIncrement="true" name="id" remarks="主键id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="project_id" remarks="项目id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="original_value" remarks="原始值" type="VARCHAR(64)"/>
            <column name="revised_value" remarks="修订值" type="VARCHAR(64)"/>
            <column name="user_id" remarks="修订用户id" type="VARCHAR(64)"/>
            <column name="username" remarks="修订用户名" type="VARCHAR(64)"/>
            <column name="create_at" remarks="修订时间" type="BIGINT UNSIGNED"/>
        </createTable>
    </changeSet>

    <changeSet id="0000000000000032" author="xinfa">
        <addColumn tableName="project">
            <column name="yunshu_execute_unit" type="VARCHAR(128)" remarks="云枢执行单位"/>
            <column name="yunshu_execute_unit_code" type="VARCHAR(64)" remarks="云枢执行单位code"/>
            <column name="yunshu_execute_unit_id" type="VARCHAR(64)" remarks="云枢执行单位Id"/>
            <column name="yunshu_execute_unit_id_path" type="TEXT" remarks="云枢执行单位id_path"/>
        </addColumn>
    </changeSet>

    <changeSet id="0000000000000034" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/0523_project_update_yunshu_execute_unit_by_execute_unit_code.sql"/>
    </changeSet>


    <changeSet id="0000000000000029" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/0427_project_update_project_dept_134.sql"/>
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/0427_project_update_project_unit_38.sql"/>
    </changeSet>

    <changeSet id="0000000000000035" author="huangcy">
        <addColumn tableName="project_progress">
            <column name="cpm_remarks" type="VARCHAR(128)" remarks="项目中心系统备注"/>
            <column name="create_command_status" type="tinyint" remarks="创建指挥部状态，创建中:0、创建完成:1"/>
            <column name="create_command_start_time" type="bigint" remarks="创建指挥部发起时间"/>
            <column name="create_command_end_time" type="bigint" remarks="创建指挥部完成时间"/>
        </addColumn>
    </changeSet>

    <changeSet id="0000000000000036" author="huangcy">
        <addColumn tableName="project">
            <column name="innovative_business_type" type="VARCHAR(255)" remarks="创新业务分类"/>
            <column name="standard_type" type="VARCHAR(255)" remarks="局标准分类"/>
            <column name="financial_business_segment" type="VARCHAR(255)" remarks="财商业务板块"/>
        </addColumn>
        <addColumn tableName="bid_summary">
            <column name="if_innovative_business" type="VARCHAR(11)" remarks="是否创新业务"/>
            <column name="innovative_business_type" type="VARCHAR(64)" remarks="创新业务分类"/>
            <column name="innovative_business_type2" type="VARCHAR(64)" remarks="创新业务分类2"/>
            <column name="innovative_business_type3" type="VARCHAR(64)" remarks="创新业务分类3"/>
            <column name="standard_type1" type="VARCHAR(64)" remarks="局标准分类1"/>
            <column name="standard_type2" type="VARCHAR(64)" remarks="局标准分类2"/>
            <column name="standard_type3" type="VARCHAR(64)" remarks="局标准分类3"/>
            <column name="standard_type4" type="VARCHAR(64)" remarks="局标准分类4"/>
        </addColumn>
        <addColumn tableName="supplementary_agreement">
            <column name="if_innovative_business" type="VARCHAR(11)" remarks="是否创新业务"/>
            <column name="innovative_business_type" type="VARCHAR(64)" remarks="创新业务分类"/>
            <column name="innovative_business_type2" type="VARCHAR(64)" remarks="创新业务分类2"/>
            <column name="innovative_business_type3" type="VARCHAR(64)" remarks="创新业务分类3"/>
        </addColumn>
    </changeSet>
    <changeSet id="0000000000000037" author="xinfa">
        <addColumn tableName="project">
            <column name="original_contract_amount" type="DECIMAL(20, 2)"
                    remarks="通过脚本初始化,保存的项目初始合同金额"/>
        </addColumn>
    </changeSet>

    <changeSet id="0000000000000030" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/0518_project_update.sql"/>
    </changeSet>

    <changeSet author="xinfa" id="0000000000000038">
        <createTable schemaName="project_management" tableName="yunshu_stand_org_mapping">
            <column autoIncrement="true" name="id" remarks="主键id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="yunshu_execute_unit" remarks="云枢组织名称" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="yunshu_execute_unit_id" remarks="云枢组织id" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="yunshu_execute_unit_code" remarks="云枢组织编码" type="VARCHAR(128)"/>
            <column name="yunshu_execute_unit_id_path" remarks="云枢组织idPath" type="VARCHAR(2048)">
                <constraints nullable="false"/>
            </column>
            <column name="stand_unit" remarks="标准组织" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="stand_unit_id" remarks="标准组织id" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="stand_unit_code" remarks="标准组织编码" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="stand_unit_id_path" remarks="标准组织idPath" type="VARCHAR(1024)">
                <constraints nullable="false"/>
            </column>
            <column name="stand_unit_abbreviation" remarks="标准组织简称" type="VARCHAR(200)"/>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000038-1">
        <addUniqueConstraint columnNames="yunshu_execute_unit_id, stand_unit_id"
                             constraintName="yunshu_stand_org_mapping_un" schemaName="project_management"
                             tableName="yunshu_stand_org_mapping"/>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000038-2">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8"
                 path="liquibase/sql/init_yunshu_stand_org_mapping.sql"/>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000039">
        <createTable tableName="project_event_receive_record">
            <column autoIncrement="true" name="id" remarks="主键" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="event_code" remarks="事件编码" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="trigger_time" remarks="事件触发时间" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="receive_time" remarks="事件通知接收时间" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
            <column name="receive_param" remarks="接收参数" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="pull_result" remarks="反查结果" type="JSON">
                <constraints nullable="false"/>
            </column>
            <column name="change_data" remarks="需更新数据" type="JSON"/>
            <column name="before_change_data" remarks="变更前数据" type="JSON"/>
            <column name="final_chage_data" remarks="最终变更数据" type="JSON"/>
            <column name="project_id" remarks="project表id" type="BIGINT UNSIGNED">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="0000000000000040" author="xinfa">
        <dropColumn tableName="project" columnName="smart_site_approval_at"/>
        <dropColumn tableName="project" columnName="project_structure"/>
        <dropColumn tableName="project" columnName="project_range"/>
        <dropColumn tableName="project" columnName="project_description"/>
        <dropColumn tableName="project" columnName="smart_site_project_description"/>
        <dropColumn tableName="project" columnName="has_assembled"/>
        <dropColumn tableName="project" columnName="assembled_info"/>
        <dropColumn tableName="project" columnName="assembled_ratio"/>
        <dropColumn tableName="project" columnName="assembled_area"/>
        <dropColumn tableName="project" columnName="is_innovation"/>
        <dropColumn tableName="project" columnName="deputy"/>
        <dropColumn tableName="project" columnName="subpackage_info"/>
        <dropColumn tableName="project" columnName="owner_start_date"/>
        <dropColumn tableName="project" columnName="manager_start_date"/>
        <dropColumn tableName="project" columnName="record_date"/>
    </changeSet>

    <changeSet author="xinfa" id="0000000000000041">
        <createTable tableName="smartsite_project_agreement_info">
            <column autoIncrement="true" name="id" remarks="自增id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="project_id" remarks="与基础信息表关联" type="INT">

            </column>
            <column defaultValueNumeric="0" name="contract_start_date" remarks="合同开工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="contract_end_date" remarks="合同竣工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="contract_day" remarks="合同天数" type="INT">

            </column>
            <column defaultValueNumeric="0" name="reality_start_date" remarks="实际开工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="reality_end_date" remarks="实际竣工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="reality_day" remarks="实际天数" type="INT">

            </column>
            <column defaultValueNumeric="0" name="project_approval_date" remarks="项目进场日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="owner_start_date" remarks="业主下发开工令日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="manager_start_date" remarks="监理下发开工令日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="check_date" remarks="五方主体验收日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="record_date" remarks="竣工备案日期" type="BIGINT">

            </column>
            <column name="display_info" remarks="审批通过的信息集合" type="MEDIUMTEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" remarks="创建时间" type="datetime">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" remarks="更新时间" type="datetime">

            </column>
            <column defaultValue="" name="project_status" remarks="项目状态" type="VARCHAR(100)">

            </column>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000042">
        <createTable tableName="smartsite_project_basic_info">
            <column autoIncrement="true" name="id" remarks="自增id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="project_id" remarks="立项id" type="INT">

            </column>
            <column defaultValue="" name="project_abbreviation" remarks="项目简称" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_a8no" remarks="a8no" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_address" remarks="工程地点" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_address_cn" remarks="工程地点中文" type="VARCHAR(255)"/>
            <column defaultValue="" name="project_type" remarks="工程分类" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_structure" remarks="工程结构" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_range" remarks="施工范围" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_company" remarks="施工单位" type="VARCHAR(255)">

            </column>
            <column name="project_description" remarks="项目概述" type="TEXT"/>
            <column defaultValue="" name="service_model" remarks="劳务模式" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="project_scale" remarks="项目规模" type="VARCHAR(255)">

            </column>
            <column name="project_parameter" remarks="施工参数" type="TEXT"/>
            <column defaultValue="" name="project_feature" remarks="项目特征" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="contract_model" remarks="承包模式" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="assembled_info" remarks="装配内容" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="assembled_ratio" remarks="装配率" type="VARCHAR(255)">

            </column>
            <column defaultValueNumeric="0" name="has_assembled" remarks="是否有装配项" type="TINYINT(3)">

            </column>
            <column defaultValue="" name="assembled_area" remarks="装配面积" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="lng" remarks="经度" type="VARCHAR(100)">

            </column>
            <column defaultValue="" name="lat" remarks="纬度" type="VARCHAR(100)">

            </column>
            <column defaultValueNumeric="0" name="is_administration_core" remarks="是否局重点" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_company_core" remarks="是否公司重点" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_core_area" remarks="是否区域标杆项目" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_edge_area" remarks="是否边远散小项目" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_sensitive_area" remarks="是否生态敏感区" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_deadbeat" remarks="是否履约困难项目" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_innovation" remarks="是否是创新项目" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_bm" remarks="是否bm项目" type="TINYINT(3)">

            </column>
            <column defaultValueNumeric="0" name="is_jr" remarks="是否jr项目" type="TINYINT(3)">

            </column>
            <column name="display_info" remarks="审批通过的信息集合" type="MEDIUMTEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" remarks="创建时间" type="datetime">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" remarks="更新时间"
                    type="datetime">

            </column>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000043">
        <createTable tableName="smartsite_project_contact_info">
            <column autoIncrement="true" name="id" remarks="自增id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="project_id" remarks="与基础信息表关联" type="INT">
            </column>
            <column defaultValue="" name="customer_no" remarks="客户编号" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="customer_name" remarks="客户名称" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="customer_company_property" remarks="单位性质" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="customer_level" remarks="客户级别" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="customer_parent_company" remarks="上级单位名称" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="customer_unified_social_credit_no" remarks="客户统一社会信用代码，加密"
                    type="VARCHAR(255)">

            </column>
            <column name="owner_info" remarks="业主信息json形式" type="TEXT"/>
            <column name="subpackage_info" remarks="分包信息json形式" type="TEXT"/>
            <column name="construction" remarks="建设单位" type="TEXT"/>
            <column name="supervise" remarks="监理单位" type="TEXT"/>
            <column name="design" remarks="设计单位" type="TEXT"/>
            <column name="deputy" remarks="代建单位" type="TEXT"/>
            <column name="display_info" remarks="审批通过的信息集合" type="MEDIUMTEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" remarks="创建时间" type="datetime">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" remarks="更新时间" type="datetime">

            </column>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000044">
        <createTable tableName="smartsite_project_contract_info">
            <column autoIncrement="true" name="id" remarks="自增id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="project_id" remarks="与基础信息表关联" type="INT">

            </column>
            <column defaultValue="" name="contract_name" remarks="工程名称" type="VARCHAR(255)">

            </column>
            <column defaultValue="0" name="contract_amount" remarks="合同金额，单位万元" type="VARCHAR(64)">

            </column>
            <column defaultValueNumeric="0" name="contract_is_administration_main_body" remarks="是否局内投资主体"
                    type="TINYINT(3)">

            </column>
            <column defaultValue="" name="contract_main_body" remarks="投资主体" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="contract_signed_company" remarks="签约单位" type="VARCHAR(255)">

            </column>
            <column defaultValueNumeric="0" name="start_time" remarks="合同开工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="finish_time" remarks="合同竣工日期" type="BIGINT">

            </column>
            <column defaultValueNumeric="0" name="total_range" remarks="合同日历天数" type="INT">

            </column>
            <column defaultValue="" name="contract_rule_type" remarks="工期奖罚类型" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="contract_rule_detail" remarks="工期奖罚条款" type="VARCHAR(255)">

            </column>
            <column name="contract_milestone" remarks="里程碑节点，json结构，包含时间事项" type="TEXT"/>
            <column defaultValue="" name="contract_pm" remarks="合同项目经理" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="contract_pm_uid" remarks="合同项目经理UID" type="VARCHAR(64)">

            </column>
            <column defaultValue="" name="contract_telephone" remarks="合同项目经理电话，加密" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="operator_pm" remarks="执行项目经理" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="operator_pm_uid" remarks="执行项目经理UID" type="VARCHAR(64)">

            </column>
            <column defaultValue="" name="operator_telephone" remarks="执行项目经理电话，加密" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="bid_pm" remarks="中标项目经理" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="bid_pm_uid" remarks="中标项目经理UID" type="VARCHAR(64)">

            </column>
            <column defaultValue="" name="bid_telephone" remarks="中标项目经理电话，加密" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="gov_pm" remarks="政府备案项目经理" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="gov_pm_uid" remarks="政府备案项目经理UID" type="VARCHAR(64)"/>
            <column defaultValue="" name="gov_telephone" remarks="政府备案项目经理电话，加密" type="VARCHAR(255)">

            </column>
            <column name="quality_task" remarks="质量目标" type="TEXT"/>
            <column defaultValueNumeric="0" name="quality_is_good" remarks="是否创优" type="TINYINT(3)">

            </column>
            <column defaultValue="" name="quality_location" remarks="创优所在地" type="VARCHAR(255)">

            </column>
            <column name="quality_goal" remarks="创优目标" type="TEXT"/>
            <column defaultValue="" name="quality_award" remarks="创优奖项" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="quality_rule_type" remarks="奖罚类型" type="VARCHAR(255)">

            </column>
            <column name="quality_rule_detail" remarks="奖罚条款" type="TEXT"/>
            <column name="security_task" remarks="安全目标" type="TEXT"/>
            <column defaultValueNumeric="0" name="security_is_good" remarks="是否创优" type="TINYINT(3)">

            </column>
            <column defaultValue="" name="security_location" remarks="创优所在地" type="VARCHAR(255)">

            </column>
            <column name="security_goal" remarks="创优目标" type="TEXT"/>
            <column defaultValue="" name="security_award" remarks="创优奖项" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="security_rule_type" remarks="奖罚类型" type="VARCHAR(255)">

            </column>
            <column name="security_rule_detail" remarks="奖罚条款" type="TEXT"/>
            <column name="environment_task" remarks="环境目标" type="TEXT"/>
            <column defaultValueNumeric="0" name="environment_is_good" remarks="是否创优" type="TINYINT(3)">

            </column>
            <column defaultValue="" name="environment_location" remarks="创优所在地" type="VARCHAR(255)">

            </column>
            <column name="environment_goal" remarks="创优目标" type="TEXT"/>
            <column defaultValue="" name="environment_award" remarks="创优奖项" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="environment_rule_type" remarks="奖罚类型" type="VARCHAR(255)">

            </column>
            <column name="environment_rule_detail" remarks="奖罚条款" type="TEXT"/>
            <column name="display_info" remarks="审批通过的信息集合" type="MEDIUMTEXT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" remarks="创建时间" type="datetime">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" remarks="更新时间" type="datetime">

            </column>
        </createTable>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000045">
        <createTable tableName="smartsite_project_info">
            <column autoIncrement="true" name="id" remarks="自增id,立项id" type="INT">
                <constraints primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="project_apply_id" remarks="与立项表关联" type="INT">

            </column>
            <column defaultValue="" name="project_name" remarks="项目名称" type="VARCHAR(150)">

            </column>
            <column defaultValue="" name="standard_project_id" remarks="项目中心id" type="VARCHAR(128)">

            </column>
            <column defaultValue="" name="yunshu_org_id" remarks="云枢组织id" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="yunshu_tree_id" remarks="云枢treeid" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="yunshu_parent_org_id" remarks="云枢上级单位组织id" type="VARCHAR(255)">

            </column>
            <column defaultValue="" name="yunshu_parent_org_name" remarks="云枢上级单位组织name" type="VARCHAR(255)">

            </column>
            <column defaultValueNumeric="0" name="department_id" remarks="智慧工地上级单位组织id" type="INT">

            </column>
            <column defaultValue="" name="modifier_id" remarks="当前修改人id" type="VARCHAR(100)">

            </column>
            <column defaultValue="" name="modifier_name" remarks="当前修改人姓名" type="VARCHAR(100)">

            </column>
            <column defaultValue="" name="approver_id" remarks="当前审批人id" type="VARCHAR(100)">

            </column>
            <column defaultValue="" name="approver_name" remarks="当前审批人姓名" type="VARCHAR(100)">

            </column>
            <column defaultValueNumeric="0" name="status" remarks="状态 0：待立项，1：已立项，2：待审批" type="TINYINT(3)">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_at" remarks="创建时间" type="datetime">

            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="updated_at" remarks="更新时间" type="datetime">

            </column>
            <column name="display_info" remarks="审批通过的信息集合" type="MEDIUMTEXT"/>
        </createTable>
    </changeSet>
    <changeSet id="0000000000000046" author="xinfa">
        <addColumn tableName="project_event_subscribe">
            <column name="app_key" type="varchar(32)" remarks="订阅系统标识(使用神禹网关appKey)"/>
        </addColumn>
    </changeSet>
    <changeSet id="0000000000000047" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/project_event_update_init.sql"/>
    </changeSet>
    <changeSet id="0000000000000048" author="xinfa">
        <modifyDataType tableName="project_yunshu_revision_record" columnName="original_value"
                        newDataType="VARCHAR(1024)"/>
        <modifyDataType tableName="project_yunshu_revision_record" columnName="revised_value"
                        newDataType="VARCHAR(1024)"/>
        <!--        <setColumnRemarks tableName="project_yunshu_revision_record" columnName="original_value" remarks="原始值"/>-->
        <!--        <setColumnRemarks tableName="project_yunshu_revision_record" columnName="revised_value" remarks="修订值"/>-->
        <setTableRemarks tableName="project_yunshu_revision_record" remarks="项目修订日志记录表"/>
        <renameTable oldTableName="project_yunshu_revision_record" newTableName="project_revision_record"/>
    </changeSet>
    <changeSet author="xinfa" id="0000000000000049">
        <createTable schemaName="project_management" tableName="smartsite_sync_project_info">
            <column name="project_id" remarks="项目中心项目id" type="BIGINT UNSIGNED">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="smartsite_project_agreement_info" remarks="项目履约信息表" type="JSON"/>
            <column name="smartsite_project_basic_info" remarks="项目基础信息表" type="JSON"/>
            <column name="smartsite_project_contact_info" remarks="项目相关方表" type="JSON"/>
            <column name="smartsite_project_contract_info" remarks="项目合同信息表" type="JSON"/>
            <column name="smartsite_project_info" remarks="项目信息总表" type="JSON"/>
            <column name="update_time" remarks="末次更新时间" type="BIGINT UNSIGNED"/>
        </createTable>
    </changeSet>

    <changeSet id="00000000000000050" author="hcy">
        <createTable tableName="sys_api_log">
            <column autoIncrement="true" name="id" remarks="自增id,请求日志id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="request_url" remarks="接口请求全路径url" type="VARCHAR(255)">
            </column>
            <column name="api_name" remarks="接口controller层名称" type="VARCHAR(255)">
            </column>
            <column name="request_method" remarks="http请求方式" type="VARCHAR(10)">
            </column>
            <column name="request_param" remarks="接口请求参数" type="LONGTEXT">
            </column>
            <column name="response_json" remarks="接口响应报文" type="LONGTEXT">
            </column>
            <column name="response_status" remarks="请求结果 1:成功; 2:失败" type="BIGINT(16)" defaultValue="0">
            </column>
            <column name="error_msg" remarks="接口错误报文" type="LONGTEXT"/>
            <column name="operator_type" remarks="操作类型（0其它 1新增 2更新）" type="INTEGER(16)" defaultValue="0"/>
            <column name="request_time" remarks="请求接口时间" type="BIGINT(64)"/>
            <column name="create_at" remarks="创建时间" type="BIGINT(64)"/>
        </createTable>
    </changeSet>
    <changeSet id="00000000000000051" author="xinfa">
        <addColumn tableName="project">
            <column name="bureau_nominal_project_type" type="tinyint(4)" defaultValue="0"
                    remarks="项目局名义总包分包关系: 0: 非局名义; 1: 局名义总包; 2: 局名义分包"/>
            <column name="general_contract_project_id" type="BIGINT UNSIGNED" remarks="局名义总包项目id"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000052" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0811_project_grogress_status_update.sql"/>
    </changeSet>
    <changeSet id="00000000000000053" author="xinfa">
        <createTable tableName="smartsite_engineer_parameter" remarks="智慧工地工程参数">
            <column autoIncrement="true" name="code" remarks="自增id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="label" remarks="类型名称" type="VARCHAR(128)"/>
            <column name="data_key" remarks="工程类型标识" type="VARCHAR(64)"/>
            <column name="type" remarks="单位值类型" type="VARCHAR(32)"/>
            <column name="unit" remarks="单位" type="VARCHAR(128)"/>
            <column name="remark" remarks="备注" type="VARCHAR(128)"/>
            <column name="id" remarks="当前工程分类下的id" type="VARCHAR(16)"/>
        </createTable>
    </changeSet>
    <changeSet id="00000000000000054" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/smartsite_engineer_parameter.sql"/>
    </changeSet>
    <changeSet id="00000000000000055" author="xinfa">
        <addColumn tableName="project">
            <column name="yunshu_execute_unit_abbreviation" type="varchar(128)" remarks="云枢执行单位简称"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000056" author="xinfa">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/table_refactor.sql"/>
    </changeSet>
    <changeSet id="00000000000000057" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0819_normalization_of_tables.sql"/>
    </changeSet>
    <changeSet id="00000000000000059" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0819_normalization_character_sets.sql"/>
    </changeSet>
    <changeSet id="00000000000000061" author="huangcy">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/0822_complete_field_annotations.sql"/>
    </changeSet>
    <changeSet id="00000000000000060" author="xinfa">
        <addColumn tableName="project_revision_record">
            <column name="revision_type" type="TINYINT" defaultValue="0" remarks="修订类型: 1:云枢id; 2:财商; 3:执行单位;"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000062" author="xinfa">
        <createTable tableName="yunshu_org_sync" remarks="云枢组织同步信息">
            <column name="id" type="varchar(32)" remarks="组织treeId">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="id_path" type="varchar(700)" remarks="云枢树queryCode"/>
            <column name="dept_id" type="varchar(32)" remarks="部门id"/>
            <column name="code" type="varchar(32)" remarks="部门code"/>
            <column name="name" type="varchar(128)" remarks="组织全称"/>
            <column name="abbreviation" type="varchar(128)" remarks="组织简称"/>
            <column name="parent_id" type="varchar(32)" remarks="上级组织treeId"/>
            <column name="leaf" type="tinyint" remarks="是否叶子节点"/>
            <column name="org_type" type="varchar(8)" remarks="组织类型: 10:局; 15:托管单位; 20:分局; 30:公司; 40:分公司; 45:专业公司; 47:城市公司; 50:项目经理部; 60:指挥部; 70:项目部; 80:机关; 90:部门; "/>
            <column name="is_setup_sub" type="tinyint" remarks="能否直接下设项目部或指挥部: 0:不可添加; 1:可以添加;"/>
            <column name="id_path_name" type="varchar(2048)" remarks="全路径名称"/>
            <column name="id_path_abbreviation" type="varchar(2048)" remarks="全路径简称"/>
            <column name="level" type="tinyint" remarks="层级"/>
            <column name="sync_mark" type="varchar(32)" remarks="同步标识(每次同步更新)"/>
        </createTable>
        <sql>
            create unique index yunshu_org_sync_id_path_IDX on yunshu_org_sync (id_path);
            create fulltext index yunshu_org_sync_name_IDX on yunshu_org_sync (name, abbreviation);
            create index yunshu_org_sync_parent_id_IDX on yunshu_org_sync (parent_id);
        </sql>
    </changeSet>
    <changeSet id="00000000000000063" author="xinfa">
        <sql>
            alter table yunshu_org_sync
                add update_at datetime default current_timestamp null on update current_timestamp;
        </sql>
    </changeSet>
    <changeSet id="00000000000000064" author="xinfa">
        <sql>
            alter table yunshu_org_sync
                modify dept_id varchar(128) null comment '部门id';
        </sql>
    </changeSet>
       <changeSet id="00000000000000065" author="xinfa">
        <sql>
            CREATE UNIQUE INDEX yunshu_org_sync_dept_id_IDX USING BTREE ON project_management.yunshu_org_sync (dept_id);
        </sql>
    </changeSet>
    <changeSet id="00000000000000066" author="xinfa">
        <addColumn tableName="yunshu_org_sync">
            <column name="dept_sort" type="varchar(64)" remarks="排序"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000067" author="xinfa">
        <sql>
            alter table yunshu_org_sync modify code varchar(128) null comment '部门code';
        </sql>
    </changeSet>
    <changeSet id="00000000000000068" author="xinfa">
        <sql>
            alter table yunshu_org_sync modify dept_sort BIGINT null comment '排序';
        </sql>
    </changeSet>
    <changeSet id="00000000000000069" author="xinfa">
        <sql> alter table supplementary_agreement
            modify independent_contract_type int null comment '独立合同类型：1投标总结；2合同定案；3补充协议定案；4局内部合同定案；5局内部补充协议； 8:补充协议';
        alter table supplementary_agreement
            modify total_amount decimal(20, 2) null comment '补充协议定案-含税合同总价（人民币）; 补充协议-补充协议金额';
        </sql>
    </changeSet>
    <changeSet id="00000000000000070" author="xinfa">
        <addColumn tableName="supplementary_agreement">
            <column name="bureau_project" type="varchar(2)" remarks="是否局重点项目: Y:是; N:否"/>
            <column name="standard_type1" type="varchar(128)" remarks="局标准分类1"/>
            <column name="standard_type2" type="varchar(128)" remarks="局标准分类2"/>
            <column name="standard_type3" type="varchar(128)" remarks="局标准分类3"/>
            <column name="standard_type4" type="varchar(128)" remarks="局标准分类4"/>
            <column name="after_change_contract_amount" type="decimal(20,2)" remarks="变更后的合同金额"/>
            <column name="mandate_foreign" type="varchar(2)" remarks="是否授权外: Y:是; N:否"/>
<!--            <column name="customer_id" type="BIGINT UNSIGNED" remarks="业主Id"/>-->
            <column name="advances_fund_month" type="varchar(60)" remarks="预估垫资时间"/>
            <column name="estimated_advance_amount" type="decimal(20,2)" remarks="预估垫资金额（折算美元）"/>
            <column name="belong_file_type" type="TINYINT(1)" remarks="源文件类型: 8:补充协议; 3:补充协议定案"/>
            <column name="contract_responsible_person" type="varchar(64)" remarks="合同经办人"/>
            <column name="advances_fund_amount" type="decimal(20,2)" remarks="预估垫资金额"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000071" author="xinfa">
        <addColumn tableName="supplementary_agreement">
            <column name="no_cash_pay_way" type="varchar(128)" remarks="非现金支付方式"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000072" author="huangcy">
        <addColumn tableName="project">
            <column name="quality_task" type="TEXT" remarks="质量目标"/>
            <column name="security_task" type="TEXT" remarks="安全任务"/>
            <column name="record_date" type="BIGINT" remarks="竣工备案日期"/>
            <column name="smart_contract_model" type="VARCHAR(255)" remarks="承包模式（智慧工地）"/>
            <column name="smart_project_address" type="VARCHAR(360)" remarks="项目地址"/>
            <column name="yunshu_parent_org_id" type="varchar(255)" remarks="智慧工地项目直接上级云枢组织ID"/>
            <column name="yunshu_parent_org_name" type="varchar(255)" remarks="智慧工地项目直接上级全称"/>
            <column name="yunshu_parent_tree_id" type="varchar(255)" remarks="智慧工地项目直接上级treeID"/>
            <column name="project_scale" type="varchar(255)" remarks="项目规模"/>
            <column name="lng" type="varchar(100)" remarks="经度"/>
            <column name="lat" type="varchar(100)" remarks="纬度"/>
            <column name="cpm_project_mark" type="varchar(64)" remarks="项目唯一标识"/>
            <column name="contract_start_date" type="BIGINT" remarks="合同开工日期"/>
            <column name="contract_end_date" type="BIGINT" remarks="合同竣工日期"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000073" author="xinfa">
        <dropColumn tableName="project_event_subscribe" columnName="protocol"/>
        <dropColumn tableName="project_event_subscribe" columnName="certificate"/>
        <renameColumn tableName="project_event_subscribe" oldColumnName="consumer_id" newColumnName="id" columnDataType="int(11)" remarks="订阅id"/>
        <renameColumn tableName="project_event_subscribe" oldColumnName="app_key" newColumnName="app_code" columnDataType="varchar(32)" remarks="订阅者业务标识"/>
        <dropColumn tableName="project_event_subscribe" columnName="responsible"/>
    </changeSet>
    <changeSet id="00000000000000074" author="xinfa">
        <addColumn tableName="project_event_subscribe">
            <column name="app_key" type="varchar(64)" remarks="去业务系统app鉴权用：业务系统颁发给项目中心的神禹appKey,未接入则自定义填入">
                <constraints nullable="false"/>
            </column>
            <column name="app_secret" type="varchar(64)" remarks="去业务系统app鉴权用：业务系统颁发给项目中心的神禹secret,未接入则自定义填入">
                <constraints nullable="false"/>
            </column>
            <column name="extension" type="json" remarks="扩展字段，appkey,secret无法满足时，可以此处扩展"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000075" author="huangcy">
        <renameColumn tableName="project"  oldColumnName="cpm_project_mark" newColumnName="cpm_project_key" columnDataType="varchar(64)"/>
    </changeSet>
    <!-- Create the project_flow_event_record table -->
    <changeSet id="00000000000000076" author="huangcy">
        <createTable tableName="project_flow_event_record" remarks="项目流转事件监听记录表">
            <column name="id" type="VARCHAR(32)" remarks="主键id">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="project_id" type="BIGINT" defaultValueNumeric="0" remarks="项目id">
                <constraints nullable="false"/>
            </column>
            <column name="flow_node_code" type="VARCHAR(64)" remarks="项目流转节点">
                <constraints nullable="false"/>
            </column>
            <column name="flow_handler_code" type="VARCHAR(8)" remarks="项目流转节点监听切点：pre: 节点后;  post:节点后;">
                <constraints nullable="false"/>
            </column>
            <column name="project_archive" type="JSON" remarks="监听事件触发时的项目信息">
                <constraints nullable="true"/>
            </column>
            <column name="flow_data_type_code" type="VARCHAR(8)" remarks="项目信息类型：insert:新增; update:更新;">
                <constraints nullable="true"/>
            </column>
            <column name="create_at" type="BIGINT" defaultValueNumeric="0" remarks="触发时间">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Add indexes -->
    <changeSet id="00000000000000077" author="huangcy">
        <createIndex tableName="project_flow_event_record" indexName="project_flow_event_record_project_id_IDX">
            <column name="project_id"/>
        </createIndex>
        <createIndex tableName="project_flow_event_record" indexName="project_flow_event_record_project_IDX">
            <column name="project_id"/>
            <column name="flow_node_code"/>
            <column name="flow_handler_code"/>
        </createIndex>
    </changeSet>

    <!-- Create the project_flow_subscribe_config table -->
    <changeSet id="00000000000000078" author="huangcy">
        <createTable tableName="project_flow_subscribe_config" remarks="项目流转事件配置表">
            <column name="id" type="BIGINT" autoIncrement="true" remarks="主键">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(128)" remarks="配置名称">
                <constraints nullable="false"/>
            </column>
            <column name="flow_node_code" type="VARCHAR(64)" remarks="项目流转节点">
                <constraints nullable="false"/>
            </column>
            <column name="flow_handler_code" type="VARCHAR(8)" remarks="项目流转节点监听切点：pre: 节点后; post:节点后;">
                <constraints nullable="false"/>
            </column>
            <column name="flow_data_type_code" type="VARCHAR(8)" remarks="项目信息类型：insert:新增; update:更新;">
                <constraints nullable="false"/>
            </column>
            <column name="create_at" type="BIGINT" remarks="触发时间">
                <constraints nullable="false"/>
            </column>
            <column name="consumer_id" type="INT" remarks="推送系统id">
                <constraints nullable="false"/>
            </column>
            <column name="update_at" type="BIGINT" remarks="更新时间"/>
            <column name="status" type="TINYINT" defaultValueNumeric="1" remarks="状态 0:禁用; 1:启用;">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="00000000000000079" author="huangcy">
        <addColumn tableName="project">
            <column name="cpm_project_name" type="VARCHAR(128)" remarks="项目名称，含义：工程名称更新和财商名称更新均更新此字段（工程名称仅在首次新增时使用）"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000080" author="qiutian">
        <addColumn tableName="project_flow_subscribe_config">
            <column name="flow_code" type="VARCHAR(128)" remarks="事件编码"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000081" author="qiutian">
        <addColumn tableName="project_event_subscribe">
            <column name="responsible" type="VARCHAR(128)" remarks="系统负责人"/>
        </addColumn>
        <addColumn tableName="project_event_subscribe">
            <column name="yunshu_execute_unit_id" type="VARCHAR(128)" remarks="云枢执行单位id"/>
        </addColumn>
        <addColumn tableName="project_event_subscribe">
            <column name="yunshu_execute_unit" type="VARCHAR(128)" remarks="云枢执行单位"/>
        </addColumn>
        <addColumn tableName="project_event_subscribe">
            <column name="yunshu_execute_unit_abbreviation" type="VARCHAR(128)" remarks="云枢执行单位简称"/>
        </addColumn>
        <addColumn tableName="project_event_subscribe">
            <column name="yunshu_execute_unit_id_path" type="VARCHAR(128)" remarks="云枢执行单位idPath"/>
        </addColumn>
    </changeSet>

    <!-- 创建项目事件推送记录表 -->
    <changeSet id="00000000000000082" author="huangchunyang">
        <createTable tableName="project_event_push_record">
            <column name="id" type="BIGINT" autoIncrement="true" remarks="主键">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="push_system_id" type="INT(10)" remarks="订阅者系统id"/>
            <column name="push_url" type="VARCHAR(255)" remarks="推送订阅系统的接口地址"/>
            <column name="project_msg_id" type="VARCHAR(32)" remarks="项目事件消息id，对应project_flow_event_record主键id"/>
            <column name="project_id" type="BIGINT(30)" remarks="项目实体主键id"/>
            <column name="is_push_success" type="TINYINT(3)" remarks="是否推送成功，根据业务系统响应结果判断"/>
            <column name="err_msg" type="TEXT" remarks="推送错误日志"/>
            <column name="times" type="INT(10)" remarks="推送重试次数"/>
            <column name="push_time" type="BIGINT(32)" remarks="推送时间"/>
        </createTable>
    </changeSet>

    <changeSet id="00000000000000083" author="huangchunyang">
        <addColumn tableName="project">
            <column name="advances_rate" type="VARCHAR(16)" remarks="预付款比例"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000083" author="qiutian">
        <addColumn tableName="project_flow_subscribe_config">
            <column name="flow_items" type="json" remarks="节点项"/>
        </addColumn>
        <modifyDataType
                tableName="project_flow_subscribe_config"
                columnName="flow_node_code"
                newDataType="VARCHAR(64) DEFAULT NULL"/>
        <modifyDataType
                tableName="project_flow_subscribe_config"
                columnName="flow_handler_code"
                newDataType="VARCHAR(64) DEFAULT NULL"/>
    </changeSet>
    <changeSet id="00000000000000084" author="qiutian">
        <modifyDataType
            tableName="project_event_subscribe"
            columnName="id"
            newDataType="INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键id'"/>
        <modifyDataType
                tableName="project_event_subscribe"
                columnName="event_ids"
                newDataType="VARCHAR(64) DEFAULT NULL"/>
    </changeSet>
    <changeSet id="00000000000000085" author="qiutian">
        <modifyDataType
                tableName="project_event_subscribe"
                columnName="yunshu_execute_unit_id_path"
                newDataType="VARCHAR(2048)"/>
    </changeSet>
    <changeSet id="00000000000000086" author="xinfa">
        <addColumn tableName="project">
            <column name="yunshu_tree_id" type="varchar(255)" remarks="项目部云枢treeid"/>
            <column name="yunshu_query_code" type="varchar(255)" remarks="项目部云枢queryCode"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000087" author="qiutian">
        <addColumn tableName="project">
            <column name="yzw_project_id" type="varchar(12)" remarks="云筑网（集采）项目编码"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000088" author="qiutian">
        <modifyDataType
                tableName="project_event_subscribe"
                columnName="app_key"
                newDataType="varchar(64) DEFAULT NULL"/>
        <modifyDataType
                tableName="project_event_subscribe"
                columnName="app_secret"
                newDataType="varchar(64) DEFAULT NULL"/>
    </changeSet>
    <changeSet id="00000000000000089" author="qiutian">
        <modifyDataType
                tableName="project_event_subscribe"
                columnName="app_code"
                newDataType="varchar(64)"/>
    </changeSet>
    <changeSet id="00000000000000090" author="xinfa">
        <addColumn tableName="project_event_subscribe">
            <column name="spi_class_name" type="VARCHAR(64)" defaultValue="DefaultTransferService" remarks="SPI扩展实现类名称(默认DefaultTransferService)"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000091" author="huangchunyang">
        <addColumn tableName="project">
            <column name="standard_type_code_path" type="VARCHAR(50)"  remarks="局标准分类codePath"/>
            <column name="business_segment_code_path" type="VARCHAR(50)"  remarks="业务版块codePath"/>
        </addColumn>
    </changeSet>
    <changeSet id="00000000000000092" author="huangchunyang">
        <sqlFile dbms="mysql" endDelimiter=";" encoding="UTF-8" path="liquibase/sql/1018_standard_type_update.sql"/>
    </changeSet>
    <changeSet id="00000000000000093" author="huangchunyang">
        <modifyDataType tableName="project" columnName="yunshu_query_code"
                        newDataType="TEXT"/>
    </changeSet>
</databaseChangeLog>
