<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SmartProjectHookInfoMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SmartProjectHookInfo">
    <!--@mbg.generated-->
    <!--@Table smart_project_hook_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="current_project_id" jdbcType="BIGINT" property="currentProjectId" />
    <result column="current_project_key" jdbcType="VARCHAR" property="currentProjectKey" />
    <result column="target_project_yunshu_org_id" jdbcType="VARCHAR" property="targetProjectYunshuOrgId" />
    <result column="hook_time" jdbcType="BIGINT" property="hookTime" />
    <result column="hook_user_code" jdbcType="VARCHAR" property="hookUserCode" />
    <result column="hook_project_type" jdbcType="VARCHAR" property="hookProjectType" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_at" jdbcType="BIGINT" property="updateAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, current_project_id, current_project_key, target_project_yunshu_org_id, hook_time, 
    hook_user_code, hook_project_type, deleted, create_at, create_by, update_at, update_by, 
    remark
  </sql>
</mapper>