<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.BusinessSystemDataChangeApprovalConfigMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.BusSysDataChangeApprovalConfig">
        <!--@mbg.generated-->
        <!--@Table business_system_data_change_approval_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="field_id" jdbcType="VARCHAR" property="fieldId"/>
        <result column="scope_type" jdbcType="VARCHAR" property="scopeType"/>
        <result column="scope_type_name" jdbcType="VARCHAR" property="scopeTypeName"/>
        <result column="field_name" jdbcType="VARCHAR" property="fieldName"/>
        <result column="raw_field_content" jdbcType="VARCHAR" property="rawFieldContent"/>
        <result column="raw_field_enum_name" jdbcType="VARCHAR" property="rawFieldEnumName"/>
        <result column="target_field_content" jdbcType="VARCHAR" property="targetFieldContent"/>
        <result column="target_field_enum_name" jdbcType="VARCHAR" property="targetFieldEnumName"/>
        <result column="process_type" jdbcType="TINYINT" property="processType"/>
        <result column="proc_def_id" jdbcType="VARCHAR" property="procDefId"/>
        <result column="execute_unit_org_id" jdbcType="VARCHAR" property="executeUnitOrgId"/>
        <result column="execute_unit_tree_id" jdbcType="VARCHAR" property="executeUnitTreeId"/>
        <result column="execute_unit_query_code" jdbcType="VARCHAR" property="executeUnitQueryCode"/>
        <result column="create_at" jdbcType="TIMESTAMP" property="createAt"/>
        <result column="create_by" jdbcType="TIMESTAMP" property="createBy"/>
        <result column="update_at" jdbcType="TIMESTAMP" property="updateAt"/>
        <result column="update_by" jdbcType="TIMESTAMP" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, field_id, scope_type, scope_type_name, field_name, raw_field_content,
        raw_field_enum_name, target_field_content, target_field_enum_name,
        process_type, proc_def_id, execute_unit_org_id, execute_unit_query_code,deleted,
        create_at, create_by, update_at, update_by,execute_unit_tree_id
    </sql>

    <select id="pageList" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataChangeConfigResp">
        select t.*,
               yos.id                   executeUnitTreeId,
               yos.id_path_name         yunshuExecuteUnitPathName,
               yos.id_path_abbreviation yunshuExecuteUnitPathAbbreviation
        from business_system_data_change_approval_config t
                 inner join yunshu_org_sync yos on t.execute_unit_org_id = yos.dept_id
        <where>
            t.deleted = 0
            <if test="scopeType != null and scopeType != ''">
                and t.scope_type = #{scopeType}
            </if>
            <if test="scopeTypeName != null and scopeTypeName != ''">
                and t.scope_type_name like concat('%', #{scopeTypeName}, '%')
            </if>
            <if test="fieldId != null and fieldId != ''">
                and t.field_id = #{fieldId}
            </if>
            <if test="fieldName != null and fieldName != ''">
                and t.field_name like concat('%', #{fieldName}, '%')
            </if>
            <if test="rawFieldContent != null and rawFieldContent != ''">
                and t.raw_field_content = #{rawFieldContent}
            </if>
            <if test="targetFieldContent != null and targetFieldContent != ''">
                and t.target_field_content = #{targetFieldContent}
            </if>
            <if test="processType != null">
                and t.process_type = #{processType}
            </if>
            <if test="executeUnitQueryCode != null and executeUnitQueryCode != ''">
                and yos.id_path like concat(#{executeUnitQueryCode}, '%')
            </if>
        </where>
        order by t.create_at desc

    </select>
</mapper>