<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.AttachmentMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.Attachment">
        <!--@mbg.generated-->
        <!--@Table attachment-->
        <result column="business_id" jdbcType="VARCHAR" property="businessId"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="in_editor" jdbcType="TINYINT" property="inEditor"/>
        <result column="original_name" jdbcType="VARCHAR" property="originalName"/>
        <result column="file_size" jdbcType="BIGINT" property="fileSize"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="file_id" jdbcType="VARCHAR" property="fileId"/>
        <result column="file_md5" jdbcType="VARCHAR" property="fileMd5"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, business_id, business_type, in_editor, original_name, file_size, file_path, file_id, file_md5,
        create_at, create_by
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from attachment
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <delete id="batchDelete" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from attachment
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="batchInsert" resultType="Integer" parameterType="Attachment">
        insert into attachment
        (business_id, business_type, in_editor, original_name, file_size, file_path,
        file_id, file_md5,
        create_at, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.businessId,jdbcType=VARCHAR}, #{item.businessType,jdbcType=TINYINT},
            #{item.inEditor,jdbcType=TINYINT},
            #{item.originalName,jdbcType=VARCHAR}, #{item.fileSize,jdbcType=BIGINT},
            #{item.filePath,jdbcType=VARCHAR}, #{item.fileId,jdbcType=VARCHAR},
            #{item.fileMd5,jdbcType=VARCHAR},#{item.createAt,jdbcType=BIGINT},
            #{item.createBy,jdbcType=VARCHAR})
        </foreach>
    </select>

    <select id="getByBusinessIdsAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from attachment
        where
        business_type = #{businessType}
        and business_id in
        <foreach collection="businessIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getByBusinessIdAndType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from attachment
        where
        business_type = #{businessType}
        and business_id = #{businessId}
    </select>

    <select id="getFilePathByMd5" resultType="java.lang.String">
        select file_path
        from attachment
        where file_md5 = #{md5}
        order by create_at desc
    </select>

    <select id="getAll" resultMap="BaseResultMap">
        select *
        from attachment
    </select>
</mapper>