<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.BusinessSystemDataSmartsiteMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.BusinessSystemDataSmartsite">
    <!--@mbg.generated-->
    <!--@Table business_system_data_smartsite-->
    <id column="project_id" jdbcType="BIGINT" property="projectId" />
    <result column="yunshu_org_id" jdbcType="VARCHAR" property="yunshuOrgId" />
    <result column="engineer_parameter" jdbcType="VARCHAR" property="engineerParameter" />
    <result column="is_edge_small" jdbcType="TINYINT" property="isEdgeSmall" />
    <result column="is_ecology_sensitive" jdbcType="TINYINT" property="isEcologySensitive" />
    <result column="real_enter_time" jdbcType="BIGINT" property="realEnterTime" />
    <result column="real_work_begin_time" jdbcType="BIGINT" property="realWorkBeginTime" />
    <result column="worker_end_time" jdbcType="BIGINT" property="workerEndTime" />
    <result column="record_date" jdbcType="BIGINT" property="recordDate" />
    <result column="real_open_traffic_time" jdbcType="BIGINT" property="realOpenTrafficTime" />
    <result column="work_end_time" jdbcType="BIGINT" property="workEndTime" />
    <result column="project_status_eng" jdbcType="VARCHAR" property="projectStatusEng" />
    <result column="project_type" jdbcType="VARCHAR" property="projectType" />
    <result column="smart_project_address" jdbcType="VARCHAR" property="smartProjectAddress" />
    <result column="project_scale" jdbcType="VARCHAR" property="projectScale" />
    <result column="smart_contract_model" jdbcType="VARCHAR" property="smartContractModel" />
    <result column="lng" jdbcType="VARCHAR" property="lng" />
    <result column="lat" jdbcType="VARCHAR" property="lat" />
    <result column="quality_task" jdbcType="LONGVARCHAR" property="qualityTask" />
    <result column="security_task" jdbcType="LONGVARCHAR" property="securityTask" />
    <result column="contract_start_date" jdbcType="BIGINT" property="contractStartDate" />
    <result column="contract_end_date" jdbcType="BIGINT" property="contractEndDate" />
    <result column="yunshu_tree_id" jdbcType="VARCHAR" property="yunshuTreeId" />
    <result column="yunshu_query_code" jdbcType="LONGVARCHAR" property="yunshuQueryCode" />
    <result column="yunshu_parent_org_id" jdbcType="VARCHAR" property="yunshuParentOrgId" />
    <result column="yunshu_parent_org_name" jdbcType="VARCHAR" property="yunshuParentOrgName" />
    <result column="yunshu_parent_tree_id" jdbcType="VARCHAR" property="yunshuParentTreeId" />
    <result column="yzw_service_id" jdbcType="VARCHAR" property="yzwServiceId"/>
    <result column="yzw_video_id" jdbcType="VARCHAR" property="yzwServiceId"/>
    <result column="smart_site_project_id" jdbcType="BIGINT" property="smartSiteProjectId"/>
    <result column="smart_site_pinming_id" jdbcType="BIGINT" property="smartSitePinmingId"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    project_id, yunshu_org_id, engineer_parameter, is_edge_small, is_ecology_sensitive, 
    real_enter_time, real_work_begin_time, worker_end_time, record_date, real_open_traffic_time, 
    work_end_time, project_status_eng, project_type, smart_project_address, project_scale,
    smart_contract_model, lng, lat, quality_task, security_task, contract_start_date,
    contract_end_date, yunshu_tree_id, yunshu_query_code, yunshu_parent_org_id, yunshu_parent_org_name,
    yunshu_parent_tree_id, yzw_service_id, yzw_video_id,smart_site_project_id, smart_site_pinming_id
  </sql>
</mapper>