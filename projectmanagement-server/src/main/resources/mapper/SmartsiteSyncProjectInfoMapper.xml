<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteSyncProjectInfoMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteSyncProjectInfo">
    <!--@mbg.generated-->
    <!--@Table smartsite_sync_project_info-->
    <id column="project_id" property="projectId" />
    <result column="smartsite_project_agreement_info" property="smartsiteProjectAgreementInfo" />
    <result column="smartsite_project_basic_info" property="smartsiteProjectBasicInfo" />
    <result column="smartsite_project_contact_info" property="smartsiteProjectContactInfo" />
    <result column="smartsite_project_contract_info" property="smartsiteProjectContractInfo" />
    <result column="smartsite_project_info" property="smartsiteProjectInfo" />
    <result column="smartsite_project_hook_info" property="smartsiteProjectHookInfo"/>
    <result column="update_time" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    project_id, smartsite_project_agreement_info, smartsite_project_basic_info, smartsite_project_contact_info,
    smartsite_project_contract_info, smartsite_project_info, smartsite_project_rely_info, smartsite_project_hook_info,
    hook_num, hook_chile_info, update_time
  </sql>
  <insert id="insertOrUpdate" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteSyncProjectInfo">
    <!--@mbg.generated-->
    insert into smartsite_sync_project_info
    (project_id, smartsite_project_agreement_info, smartsite_project_basic_info, smartsite_project_contact_info,
    smartsite_project_contract_info, smartsite_project_info, smartsite_project_rely_info, smartsite_project_hook_info,
    hook_num, hook_child_info,update_time
      )
    values
    (#{projectId}, #{smartsiteProjectAgreementInfo}, #{smartsiteProjectBasicInfo}, #{smartsiteProjectContactInfo},
    #{smartsiteProjectContractInfo}, #{smartsiteProjectInfo}, #{smartsiteProjectRelyInfo}, #{smartsiteProjectHookInfo},
    #{hookNum}, #{hookDTOList}, #{updateTime}
      )
    on duplicate key update
    project_id = #{projectId},
    smartsite_project_agreement_info = #{smartsiteProjectAgreementInfo},
    smartsite_project_basic_info = #{smartsiteProjectBasicInfo},
    smartsite_project_contact_info = #{smartsiteProjectContactInfo},
    smartsite_project_contract_info = #{smartsiteProjectContractInfo},
    smartsite_project_info = #{smartsiteProjectInfo},
    smartsite_project_rely_info = #{smartsiteProjectRelyInfo},
    smartsite_project_hook_info = #{smartsiteProjectHookInfo},
    hook_num = #{hookNum},
    hook_child_info = #{hookDTOList},
    update_time = #{updateTime}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteSyncProjectInfo">
    <!--@mbg.generated-->
    insert into smartsite_sync_project_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        project_id,
      </if>
      <if test="smartsiteProjectAgreementInfo != null and smartsiteProjectAgreementInfo != ''">
        smartsite_project_agreement_info,
      </if>
      <if test="smartsiteProjectBasicInfo != null and smartsiteProjectBasicInfo != ''">
        smartsite_project_basic_info,
      </if>
      <if test="smartsiteProjectContactInfo != null and smartsiteProjectContactInfo != ''">
        smartsite_project_contact_info,
      </if>
      <if test="smartsiteProjectContractInfo != null and smartsiteProjectContractInfo != ''">
        smartsite_project_contract_info,
      </if>
      <if test="smartsiteProjectInfo != null and smartsiteProjectInfo != ''">
        smartsite_project_info,
      </if>
      <if test="smartsiteProjectRelyInfo != null and smartsiteProjectRelyInfo != ''">
        smartsite_project_rely_info,
      </if>
      <if test="smartsiteProjectHookInfo != null and smartsiteProjectHookInfo != ''">
        smartsite_project_hook_info,
      </if>
      <if test="hookNum != null">
        hook_num = #{hookNum}
      </if>
      <if test="hookDTOList != null and hookDTOList != ''">
        hook_child_info = #{hookDTOList},
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId},
      </if>
      <if test="smartsiteProjectAgreementInfo != null and smartsiteProjectAgreementInfo != ''">
        #{smartsiteProjectAgreementInfo},
      </if>
      <if test="smartsiteProjectBasicInfo != null and smartsiteProjectBasicInfo != ''">
        #{smartsiteProjectBasicInfo},
      </if>
      <if test="smartsiteProjectContactInfo != null and smartsiteProjectContactInfo != ''">
        #{smartsiteProjectContactInfo},
      </if>
      <if test="smartsiteProjectContractInfo != null and smartsiteProjectContractInfo != ''">
        #{smartsiteProjectContractInfo},
      </if>
      <if test="smartsiteProjectInfo != null and smartsiteProjectInfo != ''">
        #{smartsiteProjectInfo},
      </if>
      <if test="smartsiteProjectRelyInfo != null and smartsiteProjectRelyInfo != ''">
        #{smartsiteProjectRelyInfo},
      </if>
      <if test="smartsiteProjectHookInfo != null and smartsiteProjectHookInfo != ''">
        #{smartsiteProjectHookInfo},
      </if>
      <if test="hookNum != null">
        hook_num = #{hookNum},
      </if>
      <if test="hookDTOList != null and hookDTOList != ''">
        hook_child_info = #{hookDTOList},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="projectId != null">
        project_id = #{projectId},
      </if>
      <if test="smartsiteProjectAgreementInfo != null and smartsiteProjectAgreementInfo != ''">
        smartsite_project_agreement_info = #{smartsiteProjectAgreementInfo},
      </if>
      <if test="smartsiteProjectBasicInfo != null and smartsiteProjectBasicInfo != ''">
        smartsite_project_basic_info = #{smartsiteProjectBasicInfo},
      </if>
      <if test="smartsiteProjectContactInfo != null and smartsiteProjectContactInfo != ''">
        smartsite_project_contact_info = #{smartsiteProjectContactInfo},
      </if>
      <if test="smartsiteProjectContractInfo != null and smartsiteProjectContractInfo != ''">
        smartsite_project_contract_info = #{smartsiteProjectContractInfo},
      </if>
      <if test="smartsiteProjectInfo != null and smartsiteProjectInfo != ''">
        smartsite_project_info = #{smartsiteProjectInfo},
      </if>
      <if test="smartsiteProjectRelyInfo != null and smartsiteProjectRelyInfo != ''">
        smartsite_project_rely_info = #{smartsiteProjectRelyInfo},
      </if>
      <if test="smartsiteProjectHookInfo != null and smartsiteProjectHookInfo != ''">
        smartsite_project_hook_info = #{smartsiteProjectHookInfo},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
    </trim>
  </insert>

  <select id="getById" resultMap="BaseResultMap">
    select *
    from smartsite_sync_project_info
    where project_id = #{projectId}
  </select>
</mapper>
