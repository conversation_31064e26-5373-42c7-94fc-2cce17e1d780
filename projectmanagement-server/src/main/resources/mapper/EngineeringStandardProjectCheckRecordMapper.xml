<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.EngineeringStandardProjectCheckRecordMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectCheckRecord">
    <!--@mbg.generated-->
    <!--@Table engineering_standard_project_check_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="engineering_standard_mapping_id" jdbcType="BIGINT" property="engineeringStandardMappingId" />
    <result column="app_code" jdbcType="VARCHAR" property="appCode" />
    <result column="last_check_time" jdbcType="BIGINT" property="lastCheckTime" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="response_info" jdbcType="LONGVARCHAR" property="responseInfo" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
    <result column="update_at" jdbcType="BIGINT" property="updateAt" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="deleted" jdbcType="BIGINT" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, engineering_standard_mapping_id, app_code, last_check_time, `status`, response_info,
    create_by, create_at, update_at, update_by, deleted
  </sql>
</mapper>