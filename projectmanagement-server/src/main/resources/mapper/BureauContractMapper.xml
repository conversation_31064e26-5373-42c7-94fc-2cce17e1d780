<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.BureauContractMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.BureauContract">
        <!--@mbg.generated-->
        <!--@Table bureau_contract-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="approval_person" jdbcType="VARCHAR" property="approvalPerson"/>
        <result column="submit_person" jdbcType="VARCHAR" property="submitPerson"/>
        <result column="is_create_head" jdbcType="VARCHAR" property="isCreateHead"/>
        <result column="execute_unit" jdbcType="VARCHAR" property="executeUnit"/>
        <result column="is_independent" jdbcType="VARCHAR" property="isIndependent"/>
        <result column="associated_project" jdbcType="VARCHAR" property="associatedProject"/>
        <result column="contract_code" jdbcType="VARCHAR" property="contractCode"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_short_name" jdbcType="VARCHAR" property="projectShortName"/>
        <result column="project_belong" jdbcType="VARCHAR" property="projectBelong"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="country_project_type" jdbcType="VARCHAR" property="countryProjectType"/>
        <result column="market_project_type" jdbcType="VARCHAR" property="marketProjectType"/>
        <result column="market_project_type2" jdbcType="VARCHAR" property="marketProjectType2"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="project_type2" jdbcType="VARCHAR" property="projectType2"/>
        <result column="project_type3" jdbcType="VARCHAR" property="projectType3"/>
        <result column="project_type4" jdbcType="VARCHAR" property="projectType4"/>
        <result column="project_attachment" jdbcType="VARCHAR" property="projectAttachment"/>
        <result column="total_subcontracting_category" jdbcType="VARCHAR" property="totalSubcontractingCategory"/>
        <result column="structural_style" jdbcType="VARCHAR" property="structuralStyle"/>
        <result column="structural_style2" jdbcType="VARCHAR" property="structuralStyle2"/>
        <result column="including_steel" jdbcType="VARCHAR" property="includingSteel"/>
        <result column="project_max_length" jdbcType="VARCHAR" property="projectMaxLength"/>
        <result column="project_max_width" jdbcType="VARCHAR" property="projectMaxWidth"/>
        <result column="contract_type" jdbcType="VARCHAR" property="contractType"/>
        <result column="fabricated" jdbcType="VARCHAR" property="fabricated"/>
        <result column="company_assessment_indicators" jdbcType="VARCHAR" property="companyAssessmentIndicators"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="superior_company_name" jdbcType="VARCHAR" property="superiorCompanyName"/>
        <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType"/>
        <result column="contact_person" jdbcType="VARCHAR" property="contactPerson"/>
        <result column="designer" jdbcType="VARCHAR" property="designer"/>
        <result column="supervisor" jdbcType="VARCHAR" property="supervisor"/>
        <result column="signed_subject_value" jdbcType="VARCHAR" property="signedSubjectValue"/>
        <result column="do_unit" jdbcType="VARCHAR" property="doUnit"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="no_tax_included_money" jdbcType="DECIMAL" property="noTaxIncludedMoney"/>
        <result column="mid_amount_self" jdbcType="DECIMAL" property="midAmountSelf"/>
        <result column="self_civil_amount" jdbcType="DECIMAL" property="selfCivilAmount"/>
        <result column="self_install_amount" jdbcType="DECIMAL" property="selfInstallAmount"/>
        <result column="self_steel_structure_amount" jdbcType="DECIMAL" property="selfSteelStructureAmount"/>
        <result column="self_total_service_amount" jdbcType="DECIMAL" property="selfTotalServiceAmount"/>
        <result column="self_other_amount" jdbcType="DECIMAL" property="selfOtherAmount"/>
        <result column="subcontract_amount" jdbcType="DECIMAL" property="subcontractAmount"/>
        <result column="project_tax_amount" jdbcType="DECIMAL" property="projectTaxAmount"/>
        <result column="subcontract_content" jdbcType="VARCHAR" property="subcontractContent"/>
        <result column="bid_manager" jdbcType="VARCHAR" property="bidManager"/>
        <result column="bid_manager_code" jdbcType="VARCHAR" property="bidManagerCode"/>
        <result column="excute_manager" jdbcType="VARCHAR" property="excuteManager"/>
        <result column="contract_manager" jdbcType="VARCHAR" property="contractManager"/>
        <result column="contract_manager_code" jdbcType="VARCHAR" property="contractManagerCode"/>
        <result column="government_manager" jdbcType="VARCHAR" property="governmentManager"/>
        <result column="government_manager_code" jdbcType="VARCHAR" property="governmentManagerCode"/>
        <result column="contract_mode1" jdbcType="VARCHAR" property="contractMode1"/>
        <result column="contract_mode2" jdbcType="VARCHAR" property="contractMode2"/>
        <result column="contract_scope" jdbcType="LONGVARCHAR" property="contractScope"/>
        <result column="issuer_project" jdbcType="LONGVARCHAR" property="issuerProject"/>
        <result column="count_days" jdbcType="INTEGER" property="countDays"/>
        <result column="worker_date_reward_punish" jdbcType="VARCHAR" property="workerDateRewardPunish"/>
        <result column="worker_reward_punish_appoint" jdbcType="VARCHAR" property="workerRewardPunishAppoint"/>
        <result column="contract_style" jdbcType="VARCHAR" property="contractStyle"/>
        <result column="quality_guarantee" jdbcType="VARCHAR" property="qualityGuarantee"/>
        <result column="reward_punish_type" jdbcType="VARCHAR" property="rewardPunishType"/>
        <result column="reward_punish_terms" jdbcType="LONGVARCHAR" property="rewardPunishTerms"/>
        <result column="safety_requirement" jdbcType="LONGVARCHAR" property="safetyRequirement"/>
        <result column="safety_reward_punish_terms" jdbcType="LONGVARCHAR" property="safetyRewardPunishTerms"/>
        <result column="pricing_method" jdbcType="VARCHAR" property="pricingMethod"/>
        <result column="contract_form" jdbcType="VARCHAR" property="contractForm"/>
        <result column="cost_of_labor_change" jdbcType="VARCHAR" property="costOfLaborChange"/>
        <result column="change_way" jdbcType="VARCHAR" property="changeWay"/>
        <result column="terms" jdbcType="LONGVARCHAR" property="terms"/>
        <result column="cost_of_labor_change2" jdbcType="VARCHAR" property="costOfLaborChange2"/>
        <result column="change_rate2" jdbcType="VARCHAR" property="changeRate2"/>
        <result column="terms2" jdbcType="VARCHAR" property="terms2"/>
        <result column="advances_flag" jdbcType="VARCHAR" property="advancesFlag"/>
        <result column="advances_way" jdbcType="VARCHAR" property="advancesWay"/>
        <result column="completed_rate" jdbcType="VARCHAR" property="completedRate"/>
        <result column="completed_cycle" jdbcType="VARCHAR" property="completedCycle"/>
        <result column="settlement_rate" jdbcType="VARCHAR" property="settlementRate"/>
        <result column="settlement_cycle" jdbcType="VARCHAR" property="settlementCycle"/>
        <result column="warranty_premium" jdbcType="VARCHAR" property="warrantyPremium"/>
        <result column="warranty_premium_rate" jdbcType="VARCHAR" property="warrantyPremiumRate"/>
        <result column="warranty_premium_way" jdbcType="VARCHAR" property="warrantyPremiumWay"/>
        <result column="pay_type_new" jdbcType="VARCHAR" property="payTypeNew"/>
        <result column="no_cash_pay_way" jdbcType="VARCHAR" property="noCashPayWay"/>
        <result column="advances_fund_flag" jdbcType="VARCHAR" property="advancesFundFlag"/>
        <result column="guarantee_way" jdbcType="VARCHAR" property="guaranteeWay"/>
        <result column="land_legality_flag" jdbcType="VARCHAR" property="landLegalityFlag"/>
        <result column="give_up_compensate_flag" jdbcType="VARCHAR" property="giveUpCompensateFlag"/>
        <result column="pay_rate_less_eighty_flag" jdbcType="VARCHAR" property="payRateLessEightyFlag"/>
        <result column="node_more_two_month_flag" jdbcType="VARCHAR" property="nodeMoreTwoMonthFlag"/>
        <result column="commercial_ticket_proportion" jdbcType="VARCHAR" property="commercialTicketProportion"/>
        <result column="big_risk_measure" jdbcType="LONGVARCHAR" property="bigRiskMeasure"/>
        <result column="presentation_user" jdbcType="VARCHAR" property="presentationUser"/>
        <result column="recipient" jdbcType="VARCHAR" property="recipient"/>
        <result column="remark" jdbcType="LONGVARCHAR" property="remark"/>
        <result column="bureau_contract_code" jdbcType="VARCHAR" property="bureauContractCode"/>
        <result column="independent_contract_id" jdbcType="BIGINT" property="independentContractId"/>
        <result column="independent_contract_type" jdbcType="INTEGER" property="independentContractType"/>
        <result column="origin_file_id" jdbcType="BIGINT" property="originFileId"/>
        <result column="belong_id" jdbcType="BIGINT" property="belongId"/>
        <result column="successful_time" jdbcType="BIGINT" property="successfulTime"/>
        <result column="actual_signed_time" jdbcType="BIGINT" property="actualSignedTime"/>
        <result column="worker_begin_time" jdbcType="BIGINT" property="workerBeginTime"/>
        <result column="worker_end_time" jdbcType="BIGINT" property="workerEndTime"/>
        <result column="real_work_begin_time" jdbcType="BIGINT" property="realWorkBeginTime"/>
        <result column="predict_work_end_time" jdbcType="BIGINT" property="predictWorkEndTime"/>
        <result column="presentation_time" jdbcType="BIGINT" property="presentationTime"/>
        <result column="break_bottom" jdbcType="LONGVARCHAR" property="breakBottom"/>
        <result column="customer_level" jdbcType="VARCHAR" property="customerLevel"/>
        <result column="contact_person_mobile" jdbcType="VARCHAR" property="contactPersonMobile"/>
        <result column="excute_manager_code" jdbcType="VARCHAR" property="excuteManagerCode"/>
        <result column="attach" jdbcType="VARCHAR" property="attach"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="execute_unit_abbreviation" jdbcType="VARCHAR" property="executeUnitAbbreviation"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="yunshu_execute_unit" jdbcType="VARCHAR" property="yunshuExecuteUnit"/>
        <result column="yunshu_execute_unit_code" jdbcType="VARCHAR" property="yunshuExecuteUnitCode"/>
        <result column="yunshu_execute_unit_id" jdbcType="VARCHAR" property="yunshuExecuteUnitId"/>
        <result column="yunshu_execute_unit_id_path" jdbcType="VARCHAR" property="yunshuExecuteUnitIdPath"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="signed_subject_code" jdbcType="VARCHAR" property="signedSubjectCode"/>
        <result column="country_project_type_code" jdbcType="VARCHAR" property="countryProjectTypeCode"/>
        <result column="market_project_type_code" jdbcType="VARCHAR" property="marketProjectTypeCode"/>
        <result column="market_project_type2_code" jdbcType="VARCHAR" property="marketProjectType2Code"/>
        <result column="project_type_code" jdbcType="VARCHAR" property="projectTypeCode"/>
        <result column="project_type2_code" jdbcType="VARCHAR" property="projectType2Code"/>
        <result column="project_type3_code" jdbcType="VARCHAR" property="projectType3Code"/>
        <result column="project_type4_code" jdbcType="VARCHAR" property="projectType4Code"/>
        <result column="contract_mode1_code" jdbcType="VARCHAR" property="contractMode1Code"/>
        <result column="contract_mode2_code" jdbcType="VARCHAR" property="contractMode2Code"/>
        <result column="business_type_code" jdbcType="VARCHAR" property="businessTypeCode"/>
        <result column="customer_level_code" jdbcType="VARCHAR" property="customerLevelCode"/>
        <result column="enterprise_type_code" jdbcType="VARCHAR" property="enterpriseTypeCode"/>
        <result column="reward_punish_type_code" jdbcType="VARCHAR" property="rewardPunishTypeCode"/>
        <result column="advances_way_code" jdbcType="VARCHAR" property="advancesWayCode"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="superior_company_id" jdbcType="VARCHAR" property="superiorCompanyId"/>
        <result column="business_license_code" jdbcType="VARCHAR" property="businessLicenseCode"/>
        <result column="business_segment_code_path" jdbcType="VARCHAR" property="businessSegmentCodePath"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, approval_person, submit_person, is_create_head, execute_unit, is_independent,
        associated_project, contract_code, project_code, project_name, project_short_name,
        project_belong, province, city, region, country, address, country_project_type, market_project_type,
        market_project_type2, project_type, project_type2, project_type3, project_type4,
        project_attachment, total_subcontracting_category, structural_style, structural_style2,
        including_steel, project_max_length, project_max_width, contract_type, fabricated,
        company_assessment_indicators, customer_name, superior_company_name, enterprise_type,
        contact_person, designer, supervisor, signed_subject_value, do_unit, total_amount,
        no_tax_included_money, mid_amount_self, self_civil_amount, self_install_amount, self_steel_structure_amount,
        self_total_service_amount, self_other_amount, subcontract_amount, project_tax_amount,
        subcontract_content, bid_manager, bid_manager_code, excute_manager, contract_manager,
        contract_manager_code, government_manager, government_manager_code, contract_mode1,
        contract_mode2, contract_scope, issuer_project, count_days, worker_date_reward_punish,
        worker_reward_punish_appoint, contract_style, quality_guarantee, reward_punish_type,
        reward_punish_terms, safety_requirement, safety_reward_punish_terms, pricing_method,
        contract_form, cost_of_labor_change, change_way, terms, cost_of_labor_change2, change_rate2,
        terms2, advances_flag, advances_way, completed_rate, completed_cycle, settlement_rate,
        settlement_cycle, warranty_premium, warranty_premium_rate, warranty_premium_way,
        pay_type_new, no_cash_pay_way, advances_fund_flag, guarantee_way, land_legality_flag,
        give_up_compensate_flag, pay_rate_less_eighty_flag, node_more_two_month_flag, commercial_ticket_proportion,
        big_risk_measure, presentation_user, recipient, remark, bureau_contract_code, independent_contract_id,
        independent_contract_type, origin_file_id, belong_id, successful_time, actual_signed_time,
        worker_begin_time, worker_end_time, real_work_begin_time, predict_work_end_time,
        presentation_time, break_bottom, customer_level, contact_person_mobile, excute_manager_code,
        attach, business_type, execute_unit_abbreviation, `source`, yunshu_execute_unit,
        yunshu_execute_unit_code, yunshu_execute_unit_id, yunshu_execute_unit_id_path, create_at,
        update_at, signed_subject_code, country_project_type_code, market_project_type_code,
        market_project_type2_code, project_type_code, project_type2_code, project_type3_code,
        project_type4_code, contract_mode1_code, contract_mode2_code, business_type_code,
        customer_level_code, enterprise_type_code, reward_punish_type_code, advances_way_code,
        customer_id, customer_code, superior_company_id, business_license_code, business_segment_code_path
    </sql>
    <insert id="saveBureauContract" parameterType="com.cscec3b.iti.projectmanagement.server.entity.BureauContract"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO bureau_contract(`approval_person`,
                                    `submit_person`,
                                    `is_create_head`,
                                    `execute_unit`,
                                    `execute_unit_abbreviation`,
                                    `is_independent`,
                                    `associated_project`,
                                    `contract_code`,
                                    `project_code`,
                                    `project_name`,
                                    `project_short_name`,
                                    `project_belong`,
                                    `province`,
                                    `city`,
                                    `region`,
                                    `address`,
                                    `country`,
                                    `country_project_type`,
                                    `market_project_type`,
                                    `market_project_type2`,
                                    `project_type`,
                                    `project_type2`,
                                    `project_type3`,
                                    `project_type4`,
                                    `project_attachment`,
                                    `total_subcontracting_category`,
                                    `structural_style`,
                                    `structural_style2`,
                                    `including_steel`,
        `project_max_length`,
                                    `project_max_width`,
                                    `contract_type`,
                                    `fabricated`,
                                    `company_assessment_indicators`,
                                    `customer_name`,
                                    `superior_company_name`,
                                    `enterprise_type`,
                                    `contact_person`,
                                    `contact_person_mobile`,
                                    `designer`,
                                    `supervisor`,
                                    `successful_time`,
                                    `actual_signed_time`,
                                    `signed_subject_value`,
                                    `do_unit`,
                                    `total_amount`,
                                    `no_tax_included_money`,
                                    `mid_amount_self`,
                                    `self_civil_amount`,
                                    `self_install_amount`,
                                    `self_steel_structure_amount`,
                                    `self_total_service_amount`,
                                    `self_other_amount`,
                                    `subcontract_amount`,
                                    `project_tax_amount`,
                                    `subcontract_content`,
                                    `bid_manager`,
                                    `bid_manager_code`,
                                    `excute_manager`,
                                    `excute_manager_code`,
                                    `contract_manager`,
                                    `contract_manager_code`,
                                    `government_manager`,
                                    `government_manager_code`,
                                    `contract_mode1`,
                                    `contract_mode2`,
                                    `contract_scope`,
                                    `issuer_project`,
                                    `count_days`,
                                    `worker_begin_time`,
                                    `worker_end_time`,
                                    `real_work_begin_time`,
                                    `predict_work_end_time`,
                                    `worker_date_reward_punish`,
                                    `worker_reward_punish_appoint`,
                                    `contract_style`,
                                    `quality_guarantee`,
                                    `reward_punish_type`,
                                    `reward_punish_terms`,
                                    `safety_requirement`,
                                    `safety_reward_punish_terms`,
                                    `pricing_method`,
                                    `contract_form`,
        `cost_of_labor_change`,
                                    `change_way`,
                                    `terms`,
        cost_of_labor_change2,
                                    `change_rate2`,
                                    `terms2`,
                                    `advances_flag`,
                                    `advances_way`,
                                    `completed_rate`,
                                    `completed_cycle`,
                                    `settlement_rate`,
                                    `settlement_cycle`,
                                    `warranty_premium`,
                                    `warranty_premium_rate`,
                                    `warranty_premium_way`,
                                    `pay_type_new`,
                                    `no_cash_pay_way`,
                                    `advances_fund_flag`,
                                    `guarantee_way`,
        land_legality_flag,
                                    `give_up_compensate_flag`,
        pay_rate_less_eighty_flag,
                                    `node_more_two_month_flag`,
                                    `commercial_ticket_proportion`,
                                    `big_risk_measure`,
                                    `presentation_time`,
                                    `presentation_user`,
                                    `recipient`,
                                    `remark`,
                                    `bureau_contract_code`,
                                    `independent_contract_id`,
                                    `independent_contract_type`,
                                    `break_bottom`,
                                    `origin_file_id`,
                                    `belong_id`,
                                    `customer_level`,
                                    `attach`,
                                    `business_type`)
        VALUES (#{vo.approvalPerson},
                #{vo.submitPerson},
                #{vo.isCreateHead},
                #{vo.executeUnit},
                #{vo.executeUnitAbbreviation},
                #{vo.isIndependent},
                #{vo.associatedProject},
                #{vo.contractCode},
                #{vo.projectCode},
                #{vo.projectName},
                #{vo.projectShortName},
                #{vo.projectBelong},
                #{vo.province},
                #{vo.city},
                #{vo.region},
                #{vo.address},
                #{vo.country},
                #{vo.countryProjectType},
                #{vo.marketProjectType},
                #{vo.marketProjectType2},
                #{vo.projectType},
                #{vo.projectType2},
                #{vo.projectType3},
                #{vo.projectType4},
                #{vo.projectAttachment},
                #{vo.totalSubcontractingCategory},
                #{vo.structuralStyle},
                #{vo.structuralStyle2},
                #{vo.includingSteel},
                #{vo.projectMaxLength},
                #{vo.projectMaxWidth},
                #{vo.contractType},
                #{vo.fabricated},
                #{vo.companyAssessmentIndicators},
                #{vo.customerName},
                #{vo.superiorCompanyName},
                #{vo.enterpriseType},
                #{vo.contactPerson},
                #{vo.contactPersonMobile},
                #{vo.designer},
                #{vo.supervisor},
                #{vo.successfulTime},
                #{vo.actualSignedTime},
                #{vo.signedSubjectValue},
                #{vo.doUnit},
                #{vo.totalAmount},
                #{vo.noTaxIncludedMoney},
                #{vo.midAmountSelf},
                #{vo.selfCivilAmount},
                #{vo.selfInstallAmount},
                #{vo.selfSteelStructureAmount},
                #{vo.selfTotalServiceAmount},
                #{vo.selfOtherAmount},
                #{vo.subcontractAmount},
                #{vo.projectTaxAmount},
                #{vo.subcontractContent},
                #{vo.bidManager},
                #{vo.bidManagerCode},
                #{vo.excuteManager},
                #{vo.excuteManagerCode},
                #{vo.contractManager},
                #{vo.contractManagerCode},
                #{vo.governmentManager},
                #{vo.governmentManagerCode},
                #{vo.contractMode1},
                #{vo.contractMode2},
                #{vo.contractScope},
                #{vo.issuerProject},
                #{vo.countDays},
                #{vo.workerBeginTime},
                #{vo.workerEndTime},
                #{vo.realWorkBeginTime},
                #{vo.predictWorkEndTime},
                #{vo.workerDateRewardPunish},
                #{vo.workerRewardPunishAppoint},
                #{vo.contractStyle},
                #{vo.qualityGuarantee},
                #{vo.rewardPunishType},
                #{vo.rewardPunishTerms},
                #{vo.safetyRequirement},
                #{vo.safetyRewardPunishTerms},
                #{vo.pricingMethod},
                #{vo.contractForm},
                #{vo.costOfLaborChange},
                #{vo.changeWay},
                #{vo.terms},
                #{vo.costOfLaborChange2},
                #{vo.changeRate2},
                #{vo.terms2},
                #{vo.advancesFlag},
                #{vo.advancesWay},
                #{vo.completedRate},
                #{vo.completedCycle},
                #{vo.settlementRate},
                #{vo.settlementCycle},
                #{vo.warrantyPremium},
                #{vo.warrantyPremiumRate},
                #{vo.warrantyPremiumWay},
                #{vo.payTypeNew},
                #{vo.noCashPayWay},
                #{vo.advancesFundFlag},
                #{vo.guaranteeWay},
                #{vo.landLegalityFlag},
                #{vo.giveUpCompensateFlag},
                #{vo.payRateLessEightyFlag},
                #{vo.nodeMoreTwoMonthFlag},
                #{vo.commercialTicketProportion},
                #{vo.bigRiskMeasure},
                #{vo.presentationTime},
                #{vo.presentationUser},
                #{vo.recipient},
                #{vo.remark},
                #{vo.bureauContractCode},
                #{vo.independentContractId},
                #{vo.independentContractType},
                #{vo.breakBottom},
                #{vo.originFileId},
                #{vo.belongId},
                #{vo.customerLevel},
                #{vo.attach},
                #{vo.businessType})
    </insert>
    <select id="selectCountByBelongId" resultType="int">
        SELECT COUNT(*)
        FROM bureau_contract
        WHERE belong_id = #{belongId}
    </select>
    <select id="getById" resultType="com.cscec3b.iti.projectmanagement.server.entity.BureauContract">
        SELECT
        <include refid="Base_Column_List"/>
        FROM bureau_contract
        WHERE `id` = #{id}
    </select>

    <delete id="deleteByOriginFileId">
        delete from bureau_contract
        where origin_file_id = #{originFileId}
    </delete>

    <select id="pageList" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp">
        select t.id, t.belong_id ,
        #{scopeTypeEnum.dictCode} as scopeType, #{scopeTypeEnum.zhCN} as scopeTypeName,
        t.project_name as projectName,
        t.contract_code as contractFileCode,
        t1.id as cpmProjectId,
        t1.cpm_project_key,
        t1.cpm_project_name,
        t1.cpm_project_abbreviation,
        t1.source_system,
        t.create_at,
        t.update_at
        from bureau_contract t
        left join project t1 on t.independent_contract_id = t1.independent_contract_id
        <where>
            <if test="req.projectName != null and req.projectName != ''">
                and t.project_name like concat('%', #{req.projectName}, '%')
            </if>
            <if test="req.contractFileCode != null and req.contractFileCode != ''">
                and t.contract_code like concat('%', #{req.contractFileCode}, '%')
            </if>
            <if test="req.yunshueExecuteUnitQueryCode != null and req.yunshueExecuteUnitQueryCode != ''">
                and t.yunshu_execute_unit_id_path like concat('%', #{req.yunshueExecuteUnitQueryCode}, '%')
            </if>
            <if test="req.relativeProject != null and req.relativeProject == 1">
                and t1.id is not null
                <if test="req.cpmProjectKey != null and req.cpmProjectKey != ''">
                    and t1.cpm_project_key like concat('%', #{req.cpmProjectKey}, '%')
                </if>
                <if test="req.cpmProjectName != null and req.cpmProjectName != ''">
                    and (t1. project_name like concat('%', #{req.cpmProjectName}, '%')
                    or t1.project_finance_name like concat('%', #{req.cpmProjectName}, '%'))
                </if>
            </if>
            <if test="req.relativeProject != null and req.relativeProject == 0">
                and t1.id is null
            </if>
        </where>
        order by t.create_at desc
    </select>
</mapper>

