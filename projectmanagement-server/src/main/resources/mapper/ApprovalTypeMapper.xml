<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.ApprovalType">
        <!--@mbg.generated-->
        <!--@Table approval_type-->
        <id column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_last_node" jdbcType="TINYINT" property="lastNode"/>
        <result column="root_code" jdbcType="VARCHAR" property="rootCode"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="business_segment_code_path" jdbcType="VARCHAR" property="businessSegmentCodePath"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        type_id, type_name, type_code, parent_id, deleted, create_by, create_at, update_at,
        update_by, remark, is_last_node, root_code, org_id, business_segment_code_path
    </sql>

    <resultMap id="ProjectApprovalTypeRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.ProjectApprovalTypeResp">
        <result column="type_id" property="typeId"/>
        <result column="type_name" property="typeName"/>
        <result column="type_code" property="typeCode"/>
        <result column="parent_id" property="parentId"/>
        <result column="remark" property="remark"/>
        <result column="last_node" property="lastNode"/>
        <result column="root_code" property="rootCode"/>
        <result column="org_id" property="orgId"/>
        <result column="org_name" property="orgName"/>
        <result column="abbreviation" property="abbreviation"/>
        <result column="id_path" property="idPath"/>
        <result column="business_segment_code_path" property="businessSegmentCodePath"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>
    <select id="typeList" resultMap="ProjectApprovalTypeRespResultMap">
        select t.type_id,
        t.type_name,
        t.type_code,
        t.parent_id,
        t.remark,
        t.is_last_node last_node,
        t.root_code,
        t.org_id,
        t.business_segment_code_path,
        y.name org_name,
        y.abbreviation,
        y.id_path
        from approval_type t
        left join yunshu_org_sync y on t.org_id = y.dept_id
        where deleted = 0
        and root_code = #{rootCode}
    </select>

    <select id="matchBusinessSegmentAndExecuteUnit" resultMap="BaseResultMap">
        select
        t.type_id,
        t.type_name,
        t.type_code,
        t.parent_id,
        t.deleted,
        t.create_by,
        t.create_at,
        t.update_at,
        t.update_by,
        t.remark,
        t.is_last_node,
        t.root_code,
        t.org_id,
        t.business_segment_code_path
        from approval_type t
        inner join yunshu_org_sync y on t.org_id = y.dept_id
        where t.deleted = 0
        and json_contains(t.business_segment_code_path, json_array(#{businessSegmentCodePath}))
        and y.id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        order by length(y.id_path) desc , t.update_at desc
        limit 1
    </select>
    <select id="matchBusinessSegmentAndExecuteUnitV1" resultMap="BaseResultMap">
        select
        t.type_id,
        t.type_name,
        t.type_code,
        t.parent_id,
        t.deleted,
        t.create_by,
        t.create_at,
        t.update_at,
        t.update_by,
        t.remark,
        t.is_last_node,
        t.root_code,
        t.org_id,
        t.business_segment_code_path
        from approval_type t
        INNER JOIN yunshu_org_sync y ON t.org_id = y.dept_id
        WHERE t.deleted = 0
        AND y.id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND
        <foreach collection="codePaths" item="code" open="(" separator="or" close=")">
            json_contains(t.business_segment_code_path, json_array(#{code}))
        </foreach>
        ORDER BY LENGTH(y.id_path) DESC, t.update_at DESC
    </select>
</mapper>