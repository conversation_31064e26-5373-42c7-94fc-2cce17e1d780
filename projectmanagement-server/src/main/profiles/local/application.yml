server:
  port: 8010
  #  port: 8011
  servlet:
    context-path: /cpm/
  shutdown: graceful
spring:
  datasource:
    #    test
    url: *********************************************************************************************************************************************************
    username: dbadmin
    password: GBJJvFW2e3e*
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      # 连接沁名
      pool-name: DateSourceHikariCP
      # 最小空闲连接
      minimum-idle: 10
      # 最大连接数
      maximum-pool-size: 90
      # 空闲连接存活时间
      idle-timeout: 30000
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: false
  redis:
    host: ***********
    port: 6379
    password: AceGzHkR69y!
    database: 4
  rabbitmq:
    host: ***********
    port: 5672
    username: rbmqadm
    password: test-RcklP@ss123++
    virtual-host: cpm-vhost
    publisher-confirm-type: correlated
    publisher-returns: true
  quartz:
    # 持久化到数据库
    job-store-type: jdbc
    # 等待任务完成关闭
    wait-for-jobs-to-complete-on-shutdown: true
    # 启动时更新已存在的job
    #    overwrite-existing-jobs: false
    jdbc:
      #      initialize-schema: ALWAYS
      initialize-schema: embedded
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB

  lifecycle:
    # 优雅下线超时时间
    timeout-per-shutdown-phase: 1m

shenyu:
  register:
    registerType: http
    serverLists: http://svc-public-uc-shenyu-admin.dev.svc.cluster.local:8080
    props:
      username: admin
      password: 123456
  client:
    http:
      props:
        appName: cpm  #对应appName

#mybatis:
#  configuration:
#    map-underscore-to-camel-case: true
#    call-setters-on-nulls: true
#  mapper-locations: classpath*:/mapper/*Mapper.xml
#  type-aliases-package: com.cscec3b.iti.projectmanagement.server.entity

mybatis-plus:
  configuration:
    cache-enabled: false
    jdbc-type-for-null: 'null'
    lazy-loading-enabled: false
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    multiple-result-sets-enabled: true
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  type-aliases-package: com.cscec3b.iti.projectmanagement.server.entity


feign:
  client:
    config:
      default:
        connectTimeout: 300000
        readTimeout: 300000

# 通用中心配置信息
# 六统一 移除
#uc:
#  auth:
#    host: https://usercenterdev.cscec3b-iti.com
#    # 主API
#    host-api: ${uc.auth.host}/api/public-uc-usercenter
#    # 应用信息
#    client-id: 634e1b88e4b0dcd3b70bf9b1
#    client-secret: 7ef56fafccd54825913ffc37a89af854
#    # API 扫描范围
#    permission-package: com.cscec3b.iti
#    # 应用授权类型 授权码模式为 authorization_code; 简化模式为 token
#    grant-type: authorization_code
#    # 应用认证重定向url,协议,域名与端口需要与添加应用时填写的redirectUrl
#    redirect-uri: https://cpm-dev.cscec3b-iti.com/frontend/#/
#    # clientSecret 是否加密,通用中心开发环境不加密 ,其他环境均启用加密
#    password-encoder: false
#    # 权限校验方式: uc -> 所有api请求均发送到uc进行鉴权; app -> app自行鉴权(权限点与API绑定关系硬编码)
#    permission-auth-type: uc
#    # 应用通知 secret
#    notice-secret: 123


cscec:
  version: 2.1.1
  common:
    redis-config:
      key-prefix: projectManagement #redis前缀
  #公共数据标准配置
  common-data:
    key: 8bafc9ed995f4baca3439ae8f1c84dcc # 测试key
    secret: 4ceef33fc109439bab98ad92c691af6f # 测试secret
    url: https://sd.cscec3b-iti.com/standard

  api:
    smart-site:
      host: https://zhgd-openapi.cscec3b-iti.com/api/openapi/v2/
      service-name: projectApproval
      #智慧工地预生产环境
      project-url: http://**************:8008/api/openapi/v2/project
      # 立项成功url
      init-project-callback-url: /projectApproval/getProjectInfoById
      app-key: 402887de877a18810189baf46f5e0005
      secret-key: 8b0f41abd174bf8336de2df6e76bcaa3
      end-point: /project/profiles
      group: zjsj-test
      task-notice:
        web-link: http://localhost:8000/#/system-config/message-todo?billId=%s&type=%s&isNotice=%s
        app-link: http://localhost:8000/#/system-config/message-todo?billId=%s&type=%s&isNotice=%s
        msg-config-code: C-CONFIG-00000041
        retry-cycle: R-1/Pt1H



  exclude-paths:
    # 应用通知
    # 六统一 移除
    #    - http-method: post
    #      path: '/**/callback/uc/notify'
    #    - http-method: get
    #      path: '/**/callback/uc/**'
    #    - http-method: get
    #      path: '/**/auth/login-url'
    # 标准数据接口
    - http-method:
      path: '/**/common-data/**'
    - http-method: get
      path: '/**/sse/**'
    - http-method: get
      path: '/**/api/external/open/**'
    - http-method: post
      path: '/**/api/external/open/**'
    - http-method: put
      path: '/**/api/external/open/**'
  #    - http-method: post
  #      path: '/**'
  #    - http-method: put
  #      path: '/**'
  #    - http-method: get
  #      path: '/**'

  retry:
    # 重试间隔时间,默认毫秒,
    interval-time: 2000
    # 重试延迟的倍数，比如设置interval-time=3000，multiplier=2时，第一次重试为3秒后，第二次为6(3x2)秒，第三次为12(6x2)秒,但最长延迟时间不会超出 maxInterval,最大延迟次数不会超出maxAtt
    multiplier: 2
    # 最大延迟间隔时间，默认30秒，避免multiplier过大引起无限期等待
    max-interval: 600000
    # 重试次数
    max-attempts: 3
  portal-msg:
    portalUrl: https://portaltestt.cscec3b.com.cn
    systemId: SYS093
    passWord: 8f996a44d35e717913dc4fefb2c9cf08
    pcPrefix: https://cpm-test.cscec3b-iti.com/frontend/#/
    mobilePrefix: https://cpm-test.cscec3b-iti.com/frontend/h5/tip.html
    portalPath: /msg/v1/api/messages/
    portalSaveMessage: batchSaveMessage
  config:
    water-conservancy-sign: -1
    water-conservancy-project: -1
    infrastructure-sign: -1
    infrastructure-project: -1
    housing-construction-sign: -1
    housing-construction-project: -1
    # 阿里云 oss
  file-storage:
    platform: oss
    access-key-id: LTAI5t7vxN9mXg5hz8zPLC7Y #SNOSONAR
    secret-key: ******************************
    endpoint: https://oss-cn-shenzhen.aliyuncs.com
    bucket-name: projectmanager-dev-test
    # 其他环境需要修改成对应的目录
    base-path: dev/
    # 自定义访问域名
    domain: https://oss-projectmanager-dev-test.cscec3b-iti.com/
    # 可预览的文件格式，需要在浏览器支持的范围内筛选
    view-suffix: [ pdf,jpg,png ]
    # 是否启用同步任务
    enable-sync: true
  #精益建造平台根组织id,对应值为云枢各环境上的id, 前后必须带上‘/’,
  lean-build-org:
    root-org-id: '/2/'
  # MQ消息重试策略
  event-push:
    retry:
      # 重试间隔时间,默认毫秒,
      interval-time: 2000
      # 重试延迟的倍数，比如设置interval-time=3000，multiplier=2时，第一次重试为3秒后，第二次为6(3x2)秒，第三次为12(6x2)秒,但最长延迟时间不会超出 maxInterval,最大延迟次数不会超出maxAtt
      multiplier: 2
      # 最大延迟间隔时间，默认30秒，避免multiplier过大引起无限期等待
      max-interval: 60000
      # 重试次数
      max-attempts: 6
  logger:
    enableCleanScheduled: true
    cleanPeriod: 10
    filterRequestMode: [ get ]
  yun-shu:
    #    url: http://*************/api
    url: https://dop-bpm-test.cscec3b-iti.com/api
    appKey: ipa
    secret: 123456
    # 中建三局组织id
    g3DeptId: ECB9335D15BF4023ADF85FA8E5E3FF61
    smart:
      appKey: xmzx
      secret: 20230922
    #    url: http://*************/api
    #      appKey: xmzx_xz189_key
    #      secret: xmzx_xz189_secrect
    sync-enabled: false
    # 从UC同步数据线程数量：默认为2
    sync-thread-pool-size: 3
    # 同步数据处理线程数量：默认为1 建议设置为 sync-thread-pool-size的1/2 或2/3
    process-thread-pool-size: 1
    entity-org:
      - 10
      - 15
      - 20
      - 30
      - 40
      - 45
      - 47
      - 50
    headquarters:
      - 60
      - 70
  market:
    host: https://marketing-test-backend.cscec3b-iti.com
  project-approval:
    bid-exclude-org:
      # 城投
      - 29D9A522655D416DB0AD664BF191E4D6
      - zzz
    # 设计研
    sjy-org-id: 05570027DDC6435C84380CAE0AEC64CD
  operation:
    notice:
      # 秦琪
      - 15871451617
      # 覃文博
      - 13971398535
      # 林景宜
      - 134997
  mdm:
    push:
      host: http://mdm-uat.cscec.com/gateway/esb-api/GCXMZSJ/GCXM_JS_ZJSJ_JY
      pre-check-uri: false
      
# dop 认证中心
dop-auth:
  auth-url: https://k8stest.cscec3b-iti.com
  context-path: /api
  client-id: fe9d2d000aa04ded8041682bb0efb659
  un-auth-redirect-url: https://dop-auth-test.cscec3b-iti.com/authn/index.html

g3security:
  host: https://k8stest.cscec3b-iti.com
# 适配层
com:
  g3:
    org:
      url: https://k8stest.cscec3b-iti.com/g3-org-web
      clientId: fe9d2d000aa04ded8041682bb0efb659
      clientSecret: NTM0NWUwNWQ1M2VmNGM2NmI0NTg0YzJhN2E3YTcyMTA
      tenantId: 1701899363694329857
      smart-site:
        client-id: cpm-zhgd
        client-secret: MqesAYj9baNOI83FkXWG4zUyCLKo2nHS
      task-config:
        host: https://cpm-test.cscec3b-iti.com/frontend/#
        msg-config:
          # 中标未立项
          bid-approval-msg-config: C-CONFIG-00000027
          bid-approval-app-link: /project-establishment/notestablishing/detail?id=%s&operation=operation
          # 人资 标准立项完成并更新了dop主数据编码
          hr-notice: C-CONFIG-00000028
          # 工程项目
          engineer-msg-config: C-CONFIG-00000108
      cpm-label:
        - label: 经济线
          value: C-LABEL_PARAM-00000005
        - label: 城投
          value: C-LABEL_PARAM-00000006
#      uc:
#        marketing-client-id: f6d859778fb847718b79740600403070
#        marketing-client-secret: NTI0ZWIwZjUtMzM4ZC00YTRlLTg2MDctNWJhMWRjNTA5MGU4
#        smart-site-client-id: cpm-zhgd
#        smart-site-client-secret: MqesAYj9baNOI83FkXWG4zUyCLKo2nHS

## DOP配置信息
#dop-auth:
#  auth-url: https://k8spre.cscec3b-iti.com
#  context-path: /api
#  client-id: fe9d2d000aa04ded8041682bb0efb659
#  un-auth-redirect-url: https://dop-auth-pre.cscec3b-iti.com/authn/index.html
#
## 适配层
#com:
#  g3:
#    org:
#      url: https://k8spre.cscec3b-iti.com/g3-org-web
#      clientId: fe9d2d000aa04ded8041682bb0efb659
#      clientSecret: NTM0NWUwNWQ1M2VmNGM2NmI0NTg0YzJhN2E3YTcyMTA

#第三方thirdsys系统中心shenyu网关接口配置
shenyuapi:
  thirdsys:
    url: http://openplatformdev.cscec3b-iti.com
    contextPath: /thirdsys
    appKey: E558758F323E402F86A47905C215DB66
    appSecret: 9378BDB9F99C42F5B6DEC4DB016F6E8A
    version: 1.0.0

# 监控平台
monitor:
  bizGroup: cpm
  bizSystem: cpm-management
  http:
    enable: false
    apis: /**
    excludedApis:
    respJsonPath: $.status
    succFlag: 0
    uploadDomain: https://monitor-test.cscec3b-iti.com
  trace:
    enable: false






# 暴露 shutdown 接口
# 调用 curl -X POST http://127.0.0.1:18080/actuator/shutdown
management:
  server:
    ### 端口
    port: ${server.port}
    ### 允许服务地址
    address: 127.0.0.1
  endpoint:
    ### 是否开启下线 默认关闭
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        ### 暴露接口
        include: shutdown
