package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMCreateOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectInitReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OrgCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.ProjectEventFlowReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidApprovalReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidSummaryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowEventLogResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectResp;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.AddDepartmentResp;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;

/**
 * 运维服务
 *
 * <AUTHOR>
 * @date 2023/07/25 15:40
 **/

public interface IOMService {

    /**
     * 初始化项目
     *
     * <AUTHOR>
     * @date 2023/07/25 15:38
     * @param req req
     * @return java.lang.Long
     */
    Long initProject(OMInitProjectSaveReq req);

    /**
     * 查询初始化项目
     *
     * <AUTHOR>
     * @date 2023/07/25 15:39
     * @param id id
     * @return com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp
     */
    OMInitProjectResp getInitProject(Long id);

    /**
     * 更新初始化项目
     *
     * <AUTHOR>
     * @date 2023/07/25 15:39
     * @param req req
     * @return java.lang.Boolean
     */
    Boolean updateInitProject(OMInitProjectUpdateReq req);

    /**
     * 根据项目创建项目进度，如进度已存在则不操作
     *
     * @param projectId 项目id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/08/03 09:24
     */
    Boolean saveProjectProgress(Long projectId);

//
//    /**
//     * 去UC创建项目部
//     *
//     * @param projectId 项目id
//     * @return java.lang.Boolean
//     * <AUTHOR>
//     * @date 2023/08/10 14:25
//     */
//    Boolean restartUcProcess(Long projectId);

    /**
     * 临时批量添加项目部idpath
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/08/11 14:08
     */
    Boolean batchAddProjectDeptIdPath();

    /**
     * 重新推送财商立项信息
     *
     * @param subscribeId 业务系统 id
     * @param projectId   项目id
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/08/12 22:28
     */
    Boolean restartFinanceProcess(Long subscribeId,Long projectId);

    /**
     * 重新推送智慧工地
     *
     * @param projectId   项目id
     * @param subscribeId 业务系统id
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/08/12 22:30
     */
    Boolean restartSmartProcess(Long subscribeId, Long projectId);

    /**
     * 重新缓存云枢组织
     *
     * @param parentId 上级节点
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/08/21
     */
    Boolean reCacheYunshuOrg(String parentId);

    /**
     * 获取项目流程事件记录
     * @param req 请求参数
     * @return {@link Page}<{@link ProjectFlowEventLogResp}>
     */
    Page<ProjectFlowEventLogResp> getFlowEventRecord(ProjectEventFlowReq req);

    /**
     * 更新项目
     *
     * @param updateReq project
     * @return {@link Boolean}
     */
    Boolean updateProject(OMProjectUpdateReq updateReq);

    /**
     * 插入项目
     *
     * @param initReq project
     * @return {@link Boolean}
     */
    Long insertProject(OMProjectInitReq initReq);


    /**
     * 更新投标总结文件信息
     *
     * @param summaryReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateTenderSummaryFile(OMBidSummaryReq summaryReq);

    /**
     * 更新合同定案信息
     *
     * @param contractReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateContractFile(OMContractReq contractReq);

    /**
     * 更新局内分包合同信息
     *
     * @param bureauContractReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateBureauContractFile(OMBureauContractReq bureauContractReq);


    /**
     * 更新局内部补充协议合同信息
     *
     * @param agreementReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateBureauSupplementaryAgreementFile(OMBureauSupplementaryAgreementReq agreementReq);

    /**
     * 更新补充协议/无合同续签补充协议
     *
     * @param agreementReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateAgreementFile(OMSupplementaryAgreementReq agreementReq);

    /**
     * 更新中标未立项信息
     *
     * @param approvalReq 请求参数
     * @return {@link Boolean}
     */
    Boolean updateBid(OMBidApprovalReq approvalReq);

    /**
     * 查询项目所有信息
     *
     * @param response    响应
     * @param queryParams 查询参数
     */
    void exportAllProject(HttpServletResponse response, ProjectQueryParams queryParams);

    /**
     * 去组织中心创建组织
     *
     * @param initReq 请求参数
     * @return {@link Boolean }
     */
    AddDepartmentResp initOrgToSmartSiteTree(OMCreateOrgReq initReq, ProjectProgressEnum progressEnum);

    /**
     * 钻取组织树
     *
     * @param treeParentId 上级id
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     */
    List<YunshuOrgDepartmentTreeModel> getOrgTreeFromSmartSite(String treeParentId);

    /**
     * 获取组织信息
     *
     * @param orgId 组织id
     * @return {@link YunshuOrgDepartmentTreeModel }
     */
    YunshuOrgDepartmentEntity getSmartSiteOrgInfo(String orgId);

    /**
     * 通过组织id获取组织路径名称
     *
     * @param orgId 组织id
     * @return {@link YunshuOrgSyncResp }
     */
    YunshuOrgSyncResp getOrgPathName(String orgId);

    /**
     * 组织创建代理接口
     *
     * @param orgCreateReq
     * @return {@link ProjectResp }
     */
    Map<String, Object> createOrgProxy(OrgCreateReq orgCreateReq);


    /**
     * 交换项目的财商信息信息
     *
     * @param projectId 项目 ID
     * @param projectId2 项目 id2
     * @return {@link Boolean }
     */
    Boolean exchangeFinanceInfo(Long projectId, Long projectId2);
}
