package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.uc.OrgBO;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateYunshuOrgIdReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.uc.G3OrgEventCallbackReq;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.converter.mapstruct.IConverter;
import com.cscec3b.iti.projectmanagement.server.entity.PlatformEventLog;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.entity.dto.UcCallbackOperationLog;
import com.cscec3b.iti.projectmanagement.server.entity.dto.UcCallbackOperationLog.OperationRecord.OperationContent;
import com.cscec3b.iti.projectmanagement.server.enums.RevisionTypeEnum;
import com.cscec3b.iti.projectmanagement.server.feign.IUcAppOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.SyncUpdateOrgEvent;
import com.cscec3b.iti.projectmanagement.server.service.IUcCallbackService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.service.PlatformEventLogService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.util.DesCbcUtil;
import com.g3.org.api.dto.executeForQueryDepartmentList.resp.ExecuteForQueryDepartmentListResp;
import com.g3.org.api.dto.executeForQueryDepartmentList.resp.UserDepartment;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrgDepartmentResp;
import com.g3.org.api.dto.resp.QueryTreeIdsResponse;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.google.common.collect.ImmutableMap;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/07/30
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class UcCallbackServiceImpl implements IUcCallbackService {

    private final IYunshuOrgSyncService yunshuOrgSyncService;


    private final IUcAppOpenApiFeign ucAppOpenApiFeign;

    private final UcOpenApiProperties ucOpenApiProperties;

    private final ApplicationEventPublisher pushlisher;

    private final ProjectMapper projectMapper;

    private final ProjectService projectService;

    private final PlatformEventLogService platformEventLogService;

    private final IConverter<UcCallbackOperationLog, UcCallbackOperationLog, PlatformEventLog> iConverter;

    private final Executor cpmTaskExecutor;

    private final RedisTemplate<String, Object> redisTemplate;


    @Override
    public void ucOrgCallback(HttpServletRequest request, G3OrgEventCallbackReq callbackReq) throws Exception {
        final PlatformEventLog operationLog = new PlatformEventLog();
        try {
            log.info("ucOrgCallback: {}", callbackReq);
            final String method = request.getMethod();
            operationLog.setMsgId(callbackReq.getId()).setMsgType(method).setMsgBody(JsonUtils.toJsonStr(callbackReq))
                    .setReceiveTime(Instant.now().toEpochMilli());
            final String decode = DesCbcUtil.decode(callbackReq.getEncrypt(),
                    ucOpenApiProperties.getClientSecret(), callbackReq.getNotice());
            log.info("decode data : {}", decode);
            Objects.requireNonNull(decode, () -> {
                operationLog.setResponseResult("解密失败");
                return operationLog.getResponseResult();
            });
            operationLog.setDecodeContent(decode);
            final OrgBO orgBO = JsonUtils.readValue(decode, OrgBO.class);
            Objects.requireNonNull(orgBO, () -> {
                operationLog.setResponseResult("解密后转换组织信息为空");
                return operationLog.getResponseResult();
            });


            // 异步执行 updateProjectFromJYJZ
            CompletableFuture<Void> futureJYJZ = CompletableFuture.runAsync(() -> {
                try {
                    updateProjectFromJYJZ(orgBO, operationLog);
                } catch (Exception e) {
                    // 记录异常
                    log.error("uc组织事件 - 更新精益建造项目信息失败:", e);
                    // 可以在这里将异常信息添加到 operationLog 或其他日志系统中
                    // handleException(e, "业务系统处理异常(精益建造树)：", operationLog);
                }
            }, cpmTaskExecutor);
            // 异步执行 updateProjectFromZHGD
            CompletableFuture<Void> futureZHGD = CompletableFuture.runAsync(() -> {
                try {
                    updateProjectFromZHGD(orgBO, operationLog);
                } catch (Exception e) {
                    // 记录异常
                    log.error("uc组织事件 - 更新智慧工地项目信息失败:", e);
                    // 可以在这里将异常信息添加到 operationLog 或其他日志系统中
                    handleException(e, "业务系统处理异常(工地树)：", operationLog);
                }
            }, cpmTaskExecutor);

            try {
                CompletableFuture.allOf(futureJYJZ, futureZHGD).get();
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                operationLog.setResponseResult(Boolean.FALSE.toString());
                throw e;
            }
        } finally {
            operationLog.setResponseTime(Instant.now().toEpochMilli());
            platformEventLogService.save(operationLog);
        }
    }

    @Override
    public void ucOrgDeleteCallback(HttpServletRequest request, String id, Boolean mainData) {
        final String jsonStr = JSONUtil.toJsonStr(ImmutableMap.of("id", id, "mainData", mainData));
        final UcCallbackOperationLog operationLog = new UcCallbackOperationLog();
        operationLog.setMsgId(IdUtil.objectId()).setMsgType(request.getMethod()).setMsgBody(jsonStr)
                .setDecodeContent(jsonStr).setReceiveTime(Instant.now().toEpochMilli());
        final YunshuOrgSync yunshuOrgSync = yunshuOrgSyncService.selectById(id);
        if (Objects.nonNull(yunshuOrgSync)) {
            // 查询同级节点
            final List<YunshuOrgSync> list =
                    yunshuOrgSyncService.list(Wrappers.<YunshuOrgSync>lambdaQuery().eq(YunshuOrgSync::getParentId,
                            yunshuOrgSync.getParentId()).ne(YunshuOrgSync::getId, yunshuOrgSync.getId()));
            if (CollectionUtils.isEmpty(list)) {
                // 同步级节点为空，说明是移除了下一级所有，需要更新上级节点的leaf字段
                yunshuOrgSyncService.update(null, Wrappers.<YunshuOrgSync>lambdaUpdate().set(YunshuOrgSync::getLeaf,
                        Boolean.TRUE).eq(YunshuOrgSync::getId, yunshuOrgSync.getParentId()));
                final YunshuOrgSync parentOrgInfo = yunshuOrgSyncService.selectById(yunshuOrgSync.getParentId());
                final ExecuteUnitTreeDto unitTreeDto = (ExecuteUnitTreeDto)redisTemplate
                    .boundHashOps(Constants.YUNSHU_ORG_INFO).get(parentOrgInfo.getId());
                if (Objects.nonNull(unitTreeDto)) {
                    unitTreeDto.setLeaf(Boolean.TRUE);
                    updateYunshuSyncOrgCache(unitTreeDto);
                }
            }
            // 清除缓存
            deleteYunshuSyncOrgCache(id, yunshuOrgSync.getDeptId());
            yunshuOrgSyncService.removeById(id);
        }
        operationLog.setResponseResult(String.valueOf(Boolean.TRUE)).setResponseTime(Instant.now().toEpochMilli());
        final PlatformEventLog platformEventLog = iConverter.vo2Entity(operationLog);
        platformEventLogService.save(platformEventLog);

    }

    /**
     * 更新智慧工地信息
     * @param orgBO 变更的组织信息
     * @param operationLog 日志
     */
    private void updateProjectFromZHGD(OrgBO orgBO, PlatformEventLog operationLog) {
        // 更新云枢组织信息 并发送通知
        final List<Project> projects =
                projectMapper.selectList(Wrappers.<Project>lambdaQuery().eq(Project::getYunshuOrgId,
                        orgBO.getDepartmentId()));
        for (final Project project : projects) {
            final OperationContent operationContent = new OperationContent();
            // 调用修订云枢接口, 会自动触发更新通知
            try {
                final ProjectUpdateYunshuOrgIdReq updateYunshuOrgIdReq = new ProjectUpdateYunshuOrgIdReq();
                updateYunshuOrgIdReq.setId(project.getId());
                updateYunshuOrgIdReq.setYunshuOrgId(orgBO.getDepartmentId());
                projectService.updateYunshuOrgId(updateYunshuOrgIdReq, RevisionTypeEnum.UC_ORG_EVENT,
                        operationLog.getMsgId());
            } catch (Exception e) {
                log.error("uc组织事件 -更新项目部失败:", e);
                operationContent.setResult(Boolean.FALSE).setErrorMsg(e.getMessage());
            }
        }

    }


    /**
     * 更新精益建造树相关信息，
     * 可忽略异常，
     * @param orgBO 变更的组织 信息
     * @param operationLog 日志
     */
    private void updateProjectFromJYJZ(OrgBO orgBO, PlatformEventLog operationLog) {
        YunshuOrgDepartmentTreeModel treeModel = null;
        try {
            treeModel = executeGetOrgDepartment(UcOpenApiProperties.MARKETING, orgBO.getOrganizationUuid());
        } catch (Exception e) {
            handleException(e, "从UC获取工地树信息异常异常", operationLog);
        }
        if (null == treeModel || !ObjectUtils.allNotNull(treeModel.getId(), treeModel.getTreeParentId(),
                treeModel.getQueryCode(), treeModel.getDepartmentId())) {
            handleException(null, "精益建造组织信息异常：关键信息缺失", operationLog);
        }
        // 更新项目精益建造组织缓存信息
        updateYunshuSyncOrgInfo(treeModel);
        // 更新相关表
        final ExecuteUnitTreeDto yunshuDtos =
                new ExecuteUnitTreeDto().setDeptId(treeModel.getDepartmentId()).setName(treeModel.getDepartmentName())
                        .setCode(treeModel.getDepartmentCode()).setIdPath(treeModel.getQueryCode());
        pushlisher.publishEvent(new SyncUpdateOrgEvent(this, Collections.singletonList(yunshuDtos)));
    }

    /**
     * 更新云枢缓存 信息
     * @param treeModel 组织信息
     */
    private void updateYunshuSyncOrgInfo(YunshuOrgDepartmentTreeModel treeModel) {
        ExecuteUnitTreeDto treeDto = new ExecuteUnitTreeDto();
        treeDto.setId(treeModel.getId())
                .setDeptId(treeModel.getDepartmentId())
                .setName(treeModel.getDepartmentName())
                .setAbbreviation(StringUtils.isBlank(treeModel.getAbbreviation()) ? treeModel.getDepartmentName() :
                        treeModel.getAbbreviation())
                .setCode(treeModel.getDepartmentCode())
                .setIdPath(treeModel.getQueryCode())
                .setLevel(CharSequenceUtil.count(treeModel.getQueryCode(), "#"))
                .setOrgType(treeModel.getOrgCategory())
                .setParentId(treeModel.getTreeParentId())
                .setDeptSort(treeModel.getDeptSort())
                .setTreeName(treeModel.getSourceDepartmentName())
                .setOrgMarkCode(treeModel.getOrgMarkCode())
                .setOrgMarkName(treeModel.getOrgMarkName());
        // 获取是否存在下级,返回结果包含当前treeId
        final List<YunshuOrgDepartmentTreeModel> childdepartmentList
                = getExecuteGetChildDepartmentsResp(UcOpenApiProperties.MARKETING, treeDto.getId());
        treeDto.setLeaf(CollectionUtils.isEmpty(childdepartmentList));
        // 获取上级组织信息
        final YunshuOrgSync parentTreeDto = yunshuOrgSyncService.selectById(treeDto.getParentId());
        StringBuilder idPathName = new StringBuilder(treeDto.getName());
        StringBuilder idPathAbbreviation = new StringBuilder(treeDto.getAbbreviation());
        if (parentTreeDto != null) {
            idPathName.insert(0, parentTreeDto.getIdPathName()).insert(parentTreeDto.getIdPathName().length(),
                    Constants.ID_PATH_CONNECTOR);
            idPathAbbreviation.insert(0, parentTreeDto.getIdPathAbbreviation())
                    .insert(parentTreeDto.getIdPathAbbreviation().length(), Constants.ID_PATH_CONNECTOR);
            treeDto.setIdPathName(idPathName.toString());
            treeDto.setIdPathAbbreviation(idPathAbbreviation.toString());
            treeDto.setLevel(parentTreeDto.getLevel() + 1);
            // 更新上级组织 是否为叶子节点属性
            if (Boolean.TRUE.equals(parentTreeDto.getLeaf())) {
                parentTreeDto.setLeaf(false);
                yunshuOrgSyncService.updateById(parentTreeDto);
                // 更新缓存
                final ExecuteUnitTreeDto parentTreeDtoCache =
                        yunshuOrgSyncService.getUnitTreeDtoCacheById(treeDto.getParentId());
                parentTreeDtoCache.setLeaf(false);
                updateYunshuSyncOrgCache(parentTreeDtoCache);
            }
        } else {
            treeDto.setIdPathName(treeDto.getName());
            treeDto.setIdPathAbbreviation(treeDto.getAbbreviation());
        }
        treeDto.setSyncMark(IdUtil.objectId());
        yunshuOrgSyncService.insertOrUpdate(treeDto);
        updateYunshuSyncOrgCache(treeDto);
    }

    /**
     * 获取组织详情
     *
     * @param treeType     组织树类型
     * @param departmentId 组织ID
     * @return {@link YunshuOrgDepartmentEntity }
     */
    private YunshuOrgDepartmentEntity getDepartmentEntity(String treeType, String departmentId) {
        final ExecuteGetDepartmentResp smartSiteDepartment =
                ucAppOpenApiFeign.executeGetDepartment(treeType, departmentId);
        checkResponseEntity(smartSiteDepartment, "调用UC接口获取组织信息失败");
        final YunshuOrgDepartmentEntity departmentEntity = smartSiteDepartment.getData();
        Objects.requireNonNull(departmentEntity, String.format("详情为空：请检查组织ID: %s 是否正确", departmentId));
        return smartSiteDepartment.getData();
    }

    /**
     * 获取组织 树详情
     *
     * @param treeType     组织树类型
     * @param departmentId 组织ID
     * @return {@link YunshuOrgDepartmentEntity }
     */
    private UserDepartment getDepartmentInfo(String treeType, String departmentId) {
        final ExecuteForQueryDepartmentListResp smartSiteDepartment =
                ucAppOpenApiFeign.executeGetDepartmentInfo(treeType, departmentId);
        checkResponseEntity(smartSiteDepartment, "调用UC接口获取组织信息失败");
        final List<UserDepartment> departmentEntity = smartSiteDepartment.getData();
        Objects.requireNonNull(departmentEntity, String.format("详情为空：请检查组织ID: %s 是否正确", departmentId));
        return CollectionUtils.isEmpty(departmentEntity) ? null : departmentEntity.get(0);
    }

    private YunshuOrgDepartmentTreeModel executeGetOrgDepartment(String treeType, String treeId) {
        final ExecuteGetOrgDepartmentResp departmentResp = ucAppOpenApiFeign.executeGetOrgDepartment(treeType, treeId);
        checkResponseEntity(departmentResp, "调用UC接口获取组织树信息失败");
        final YunshuOrgDepartmentTreeModel data = departmentResp.getData();
        Objects.requireNonNull(data, String.format("详情为空：请检查组织ID: %s 是否正确", treeId));
        return data;
    }
    /**
     * 获取组织树下级组织列表
     *
     * @param treeType 组织树类型
     * @param treeId   组织treeID
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     */
    private List<YunshuOrgDepartmentTreeModel> getExecuteGetChildDepartmentsResp(String treeType, String treeId) {
        final ExecuteGetChildDepartmentsResp childDepartments =
                ucAppOpenApiFeign.getChildDepartments(treeType, treeId);
        checkResponseEntity(childDepartments, String.format("获取 %s 组织树中treeId: %s下级部门失败", treeType, treeId));
        return childDepartments.getData();
    }

    /**
     * 根据treeId查询当前组织及下级组织的treeId列表---本下
     * @param treeType 组织树类型
     * @param treeId 组织TreeID
     * @return {@link List }<{@link String }>
     */
    private List<String> getChildTreeIds(String treeType, String treeId) {
        final QueryTreeIdsResponse queryTreeIdsResponse = ucAppOpenApiFeign.executeForGetChildTreeIds(treeType, treeId);
        checkResponseEntity(queryTreeIdsResponse, String.format("获取 %s 组织树中treeI:  %s 及本下treeId列表失败", treeType, treeId));
        return queryTreeIdsResponse.getData();
    }

    /**
     * 根据treeId查询当前组织及下级组织的treeId列表---本下
     * @param treeType 组织树类型
     * @param treeId 组织TreeID
     * @return {@link List }<{@link String }>
     */
    private List<String> getAllTreeIds(String treeType, String treeId) {
        final QueryTreeIdsResponse queryTreeIdsResponse = ucAppOpenApiFeign.executeForGetAllTreeIds(treeType, treeId);
        checkResponseEntity(queryTreeIdsResponse, String.format("获取 %s 组织树中treeI:  %s 及本下treeId列表失败", treeType, treeId));
        return queryTreeIdsResponse.getData();
    }

    /**
     * 校验响应结果
     *
     * @param response 响应结果
     * @param errorMsg 错误信息
     */
    private void checkResponseEntity(CloudPivotResponse<?> response, String errorMsg) {
        if (ObjectUtils.allNotNull(response, response.getErrcode()) && !response.getErrcode().equals(0L)) {
            log.error("响应失败:{}", response);
            throw new FrameworkException(-1, errorMsg + ": " + JsonUtils.toJsonStr(response));
        }
    }

    private void handleException(Exception e, String msgPrefix, PlatformEventLog operationLog) {
        // 记录错误日志，包括异常信息和操作日志的相关内容
        // 可以考虑将操作日志标记为失败，并保存以供后续分析
        operationLog.setResponseResult(msgPrefix + Optional.ofNullable(e)
                .map(Throwable::getMessage).orElse(null));
        if (Objects.nonNull(e)) {
            log.error("{}：", msgPrefix, e);
        } else {
            log.error("{}：", msgPrefix);
        }
        throw new FrameworkException(-1, operationLog.getResponseResult());
    }

    private void updateYunshuSyncOrgCache(ExecuteUnitTreeDto dto) {
        redisTemplate.boundHashOps(Constants.YUNSHU_ORG_INFO).put(dto.getId(), dto);
        redisTemplate.boundHashOps(Constants.YUNSHU_DEPT_TO_TREE).put(dto.getDeptId(), dto.getId());
    }

    private void deleteYunshuSyncOrgCache(String treeId, String deptId) {
        redisTemplate.boundHashOps(Constants.YUNSHU_ORG_INFO).delete(treeId);
        redisTemplate.boundHashOps(Constants.YUNSHU_DEPT_TO_TREE).delete(deptId);
    }
}
