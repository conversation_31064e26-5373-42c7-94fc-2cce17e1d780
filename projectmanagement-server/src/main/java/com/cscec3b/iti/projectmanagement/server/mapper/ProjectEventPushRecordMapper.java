package com.cscec3b.iti.projectmanagement.server.mapper;

import com.cscec3b.iti.projectmanagement.api.dto.request.event.ProjectEventFlowReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowEventLogResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description ProjectEventPushRecordMapper
 * @date 2023/09/26 09:08
 */
@Mapper
public interface ProjectEventPushRecordMapper {

    int insert(ProjectEventPushRecord record);

//    ProjectEventPushRecord queryByPrimaryKey(Long id);

    List<ProjectEventPushRecord> queryPushRecords(@Param("req") ProjectEventPushRecord req);

    int clearPushRecords();

    /**
     * 查询事件和推送记录
     *
     * @param flowReq 查询条件
     * @return {@link List}<{@link ProjectFlowEventLogResp}>
     */
    List<ProjectFlowEventLogResp> queryEventAndPushRecords(@Param("flowReq") ProjectEventFlowReq flowReq);

    int updateById(ProjectEventPushRecord pushRecord);
}
