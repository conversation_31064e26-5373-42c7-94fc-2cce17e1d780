package com.cscec3b.iti.projectmanagement.server.feign;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.fasterxml.jackson.databind.JsonNode;
import feign.*;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * MDM feign config
 *
 * <AUTHOR>
 * @date 2023/10/23
 */


@Slf4j
public class MDMOpenApiFeignConfig {


    /**
     * feign 日志级别
     *
     * @return {@link Logger.Level }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public Logger.Level mdmLogger() {
        return Logger.Level.FULL;
    }

    /**
     * 重试策略
     *
     * @return {@link Retryer }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public Retryer mdmRetryer() {
        return new Retryer.Default(2 * 60 * 1000L, 2 * 60 * 1000L, 3);
    }

    // 透传请求头
    @Bean
    @Primary
    public RequestInterceptor mdmRequestInterceptor() {
        return requestTemplate -> {
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }
            HttpServletRequest request = attributes.getRequest();

            // MDM特定请求头
            requestTemplate.header("Accept", "application/json;charset=utf-8");
            requestTemplate.header("Content-Type", "application/json;charset=utf-8");
            requestTemplate.header("dataType", "Engproject");
            
            // 国际化请求参数获取参数值
            final String header = request.getHeader("Accept-Language");
            if (StringUtils.isNotBlank(header)) {
                requestTemplate.header("Accept-Language", header);
            }
            // 获取请求 ip
            final String ip = request.getHeader("x-real-ip");
            if (StringUtils.isNotBlank(ip)) {
                requestTemplate.header("x-real-ip", ip);
            }
        };
    }


    /**
     * 返回异常处理
     *
     * @return {@link ErrorDecoder }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public ErrorDecoder mdmFeignErrorDecoder() {
        return new MDMOpenApiFeignConfig.FeignErrorDecoder();
    }

    /**
     * <AUTHOR>
     * @date 2023/10/23
     *///处理返回的异常
    public static class FeignErrorDecoder extends ErrorDecoder.Default {
        @Override
        public Exception decode(String methodKey, Response response) {

            log.error("【错误】 has exception");
            Exception exception = super.decode(methodKey, response);
            // RetryableException 异常不作处理
            if (exception instanceof RetryableException) {
                return exception;
            }
            try {
                // 如果是FeignException，则对其进行处理，并抛出BusinessException
                if (exception instanceof FeignException && ((FeignException) exception).responseBody().isPresent()) {
                    ByteBuffer responseBody = ((FeignException) exception).responseBody().get();
                    String bodyText =
                            StandardCharsets.UTF_8.newDecoder().decode(responseBody.asReadOnlyBuffer()).toString();
                    final JsonNode jsonNode = JsonUtils.parseStringToJson(bodyText);
                    if (jsonNode != null && jsonNode.has("code") && jsonNode.has("message")) {
                        return new FrameworkException(jsonNode.get("code").asInt(), jsonNode.get("message").asText());
                    } else {
                        return new FrameworkException(-1, bodyText);
                    }
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            return exception;
        }

    }

}
