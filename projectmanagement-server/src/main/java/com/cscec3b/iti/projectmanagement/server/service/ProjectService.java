package com.cscec3b.iti.projectmanagement.server.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.YunshuExecuteInfoDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.FinanceReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.NonIndependentProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectEffectPictureUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenProjectArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenProjectPageByYunshuIdsReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.ProjectQueryByExecuteReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.UpdateYzwProjectIdReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenProjectArchiveResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenProjectFinanceResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.*;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.cscec3b.iti.projectmanagement.server.enums.RevisionTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.scheduled.SyncFinanceMDMScheduled;

public interface ProjectService {

    /**
     * 获取cpmProjectKey
     *
     * @return {@link String}
     */
    String getCpmProjectKey();

    /**
     * 合同id查项目信息
     *
     * @param contractType 合同类型
     * @param contractId   合同id
     * @return {@link List}<{@link Project}>
     */
    List<Project> qryProjectByConId(Integer contractType, Long contractId);

    /**
     * 通过
     *
     * @param originFileId (市场营销的associatedId, 项目挂接时为 中标未立项的主键id)
     * @param type 类型
     * @return com.cscec3b.iti.projectmanagement.server.entity.Project
     * <AUTHOR>
     * @date 2023/05/31 10:52
     */
    Project qryProjectByOriginFileId(Long originFileId, String type);

    /**
     * 创建项目
     *
     * @param project 项目
     * @return int
     */
    int createProject(Project project);

    /**
     * 更新项目
     *
     * @param project 项目
     * @return int
     */
    int updateProject(Project project);

    /**
     * 设置项目金额
     *
     * @param projectOld 旧项目
     * @return {@link Project}
     */
    Project setProjectMoney(Project projectOld);

    /**
     * 重新计算项目金额
     *
     * @param projectOld projectOld
     */
    void recalculationProjectAmounts(Project projectOld);

    /**
     * 重新计算项目金额 V3
     *
     * @param project projectOld
     */
    void recalculationProjectAmountsV3(Project project);

    /**
     * 获取详情
     *
     * @param id id
     * @return {@link ProjectResp}
     */
    ProjectResp detail(Long id);

    /**
     * 合同/补充协议关系查询
     *
     * @param independentContractId   独立合同id
     * @param independentContractType 独立合同类型
     * @return {@link ContractualRelationshipResp}
     * <AUTHOR>
     * @Description
     * @Date 2022/11/2 14:52
     */
    ContractualRelationshipResp contractRelationList(Long independentContractId, Integer independentContractType);

    /**
     * 投标总结关系查询
     *
     * @param independentContractId   独立合同id
     * @param independentContractType 独立合同类型
     * @return {@link ContractualRelationshipResp}
     * <AUTHOR>
     * @Description
     * @Date 2022/11/2 14:53
     */
    ContractualRelationshipResp tenderRelationList(Long independentContractId, Integer independentContractType);

    /**
     * 合同定案详情查询
     *
     * @param contractId 合同标识
     * @return {@link ContractDetailResp}
     * <AUTHOR>
     * @Description
     * @Date 2022/11/2 14:53
     */
    ContractDetailResp contractDetail(Long contractId);

    /**
     * 投标总结详情查询
     *
     * @param contractId 合同标识
     * @return {@link BidSummaryResp}
     * <AUTHOR>
     * @Description
     * @Date 2022/11/2 14:53
     */
    BidSummaryResp tenderDetail(Long contractId);

    /**
     * 更新项目状态
     *
     * @param id            id
     * @param projectStatus 项目状态
     * @return int
     */
    int updateProjectStatus(Long id, Integer projectStatus);

    /**
     * 通过id查询
     *
     * @param id 项目id
     * @return 项目信息
     * @description 根据id查询项目信息
     * <AUTHOR>
     * @date 2022/10/28
     */
    Project selectById(Long id);

    /**
     * 推动数据到财商
     *
     * @param id 项目id
     * @return true/false
     * @description 推送立项数据到财商系统
     * <AUTHOR>
     * @date 2022/10/28
     */
    GenericityResponse<Object> pushDataToFinance(Long id);

    /**
     * 财商获取数据
     *
     * @param id   推送数据id
     * @param type 推送数据类型
     * @return 立项项目数据
     * @description 财商系统获取项目数据
     * <AUTHOR>
     * @date 2022/10/28
     */
    GenericityResponse<FinanceResp> financeGetData(String id, String type);

    /**
     * 财务更新数据
     *
     * @param financeReq 财商回调数据
     * @return true/false
     * @description 财商回调更新项目数据
     * <AUTHOR>
     * @date 2022/10/28
     */
    GenericityResponse<Boolean> financeUpdateData(FinanceReq financeReq);

    /**
     * 保存项目任务
     *
     * @param projectReq 立项任务数据
     * @return true/false
     * @description 保存立项任务数据
     * <AUTHOR>
     * @date 2022/11/01
     */
    Boolean saveProjectTask(ProjectReq projectReq);

    /**
     * 提交项目任务
     *
     * @param projectReq 立项任务数据
     * @return true/false
     * @description 提交立项任务数据
     * <AUTHOR>
     * @date 2022/11/01
     */
    Boolean submitProjectTask(ProjectReq projectReq);

    Boolean financeInfoToProjectProgress(ProjectProgress progress);

    /**
     * 页面列表条件
     *
     * @param queryParams 查询参数
     * @return {@link Page}<{@link ProjectResp}>
     */
    Page<ProjectResp> pageListByCondition(ProjectQueryParams queryParams);

    /**
     * 更新项目
     *
     * @param projectUpdateReq 项目更新要求
     * @return {@link Boolean}
     */
    Boolean updateProjectInner(ProjectUpdateReq projectUpdateReq);

    /**
     * 获取项目信息
     *
     * @param id id
     * @return {@link ProjectDetailResp}
     */
    ProjectDetailResp getProjectInfo(Long id);



    /**
     * @param projectOpenReq 项目对外请求数据
     * @return 项目对外信息
     * @description 项目对外信息获取
     * <AUTHOR>
     * @date 2022/11/03
     */
    List<ProjectOpenResp> externalOpenProject(ProjectOpenReq projectOpenReq);

    /**
     * 获取所有项目信息
     *
     * @return {@link List}<{@link Project}>
     */
    List<Project> getAllProject();



    /**
     * 标准组织与云枢组织映射
     *
     * @param project project
     */
    // void mappingStandOrgToYunshuExecute(Project project);


    /**
     * 更新云枢id,更新云枢ID 会触发云枢更新事件
     *
     * @param projectUpdateReq projectUpdateReq
     * @param remark 备注
     * @return Boolean
     */
    Boolean updateYunshuOrgId(ProjectUpdateYunshuOrgIdReq projectUpdateReq,  RevisionTypeEnum revisionType,
            String remark);

    /**
     * 补录云枢组织id,只能补录一次，并且触发工地更新后置事件
     *
     * @param projectAdditionsReq projectAdditionsReq
     * @param revisionTypeEnum revisionTypeEnum
     * @return {@link Boolean}
     */
    Boolean additionYunshuOrgId(ProjectUpdateYunshuOrgIdReq projectAdditionsReq, RevisionTypeEnum revisionTypeEnum);


    // /**
    //  * 获取组织树
    //  * @return TreeNodeBO[]
    //  */
    // TreeNodeBO[] getAllTree();
    //
    // /**
    //  * @param departmentId 部门id
    //  * @return string
    //  */
    // String getNameByDepartmentId(String departmentId);

    /**
     * 通过文件信息查询项目信息
     *
     * @param files files
     * @return com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenByFileResp
     * <AUTHOR>
     * @date 2023/03/31 15:03
     */
    List<ProjectOpenByFileResp> externalOpenProjectByFile(List<ProjectOpenByFileReq> files);
    /**
     * 批量更新项目中的执行单位及项目部/指挥部信息
     *
     * @param projectsMap projectMap
     * @param smartSiteDataMap smartSiteDataMap
     * @return int
     */
    int updateProjectStatusFromSmt(Map<String,List<Project>> projectsMap,Map<String,SmartSiteData> smartSiteDataMap);

    /**
     * 市场营销项目挂接
     *
     * <AUTHOR>
     * @date 2023/05/21 21:04
     * @param hookReqReq hookReqReq
     * @return com.cscec3b.iti.common.base.page.Page<com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenHookQueryReq>
     */
    Page<ProjectOpenHookQueryResp> externalOpenHookProject(ProjectOpenHookQueryReq hookReqReq);

    /**
     * 中标未立项挂接
     *
     * @param hookReq hookReqReq
     * @return {@link Page}<{@link ProjectOpenHookQueryResp}>
     */
    Page<ProjectOpenHookQueryResp> hookProject(ProjectOpenHookQueryReq hookReq);

    /**
     * 市场营销项目挂接时更新项目金额
     *
     * @param project project
     * @return booean booean
     */
    Boolean updateProjectAmountById(Project project);

    /**
     * 更新项目金额
     *
     * @param projectId      projectId
     * @param contractAmount contractAmount
     * @return boolean
     */
    Boolean updateProjectAmount(Long projectId, BigDecimal contractAmount);




    /**
     * 查询所有云枢组织id为空的项目信息
     *
     * <AUTHOR>
     * @date 2023/05/22 18:48
     * @param executeUnitIds eexecuteUnitIds
     * @return java.util.List<com.cscec3b.iti.projectmanagement.server.entity.Project>
     */
    List<Project> getProjectOfYunshuUnitIdNull(Set<String> executeUnitIds);

    /**
     * 通过标准组织code 批量更新云枢单位信息
     *
     * <AUTHOR>
     * @date 2023/05/22 19:09
     * @param dtos dtos
     * @return java.lang.Boolean
     */
    Boolean updateYunshuUnitByExecuteUnitCode(List<YunshuExecuteInfoDto> dtos);

    /**
     * 通过云枢组织id更新云枢信息
     *
     * @param dtos dtos
     * @return boolean
     */
    Boolean updateYunshuUnitByExecuteUnitId(List<ExecuteUnitTreeDto> dtos);

//    /**
//     * 更新云枢单位信息
//     *
//     * <AUTHOR>
//
//    /**
//     * 执行单位与标准组织的相互映射
//     * <AUTHOR>
//     * @date 2023/06/19 09:41
//     * @param project project
//     * @return boolean
//     */
//    boolean executeUnitMapping(Project project);

    // /**
    //  * 查询无项目部且无立项进度的项目数据
    //  *
    //  * <AUTHOR>
    //  * @date 2023/07/03 15:10
    //  * @return java.util.List<com.cscec3b.iti.projectmanagement.server.entity.Project>
    //  */
    // List<Project> getHasNoDeptAndProcessList();

    /**
     * 财商信息修订
     *
     * <AUTHOR>
     * @date 2023/07/04 10:12
     * @param financeReq financeReq
     * @return java.lang.Boolean
     */
    Boolean updateFinance(ProjectUpdateFinanceReq financeReq);

    void fillFinancialBusinessSegment(Project project);

    /**
     *
     * <AUTHOR>
     * @date 2023/07/21 18:55
     * @param resetReq resetReq
     * @return java.lang.Boolean
     */
    Boolean resetNonIndependentProject(NonIndependentProjectReq resetReq);

    /**
     * 判断项目是否已完成(如财商与工地立项已完成则项目立项已完成)
     *
     * @param projectId projectId
     * <AUTHOR>
     * @date 2023/08/09 16:30
     */
    void setWhetherProjectHasBeenCompleted(Long projectId);


    /**
     * 修订执行单位
     *
     * @param executeReq 项目及执行单位信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/08/16 16:03
     */
    Boolean updateExecuteUnit(ProjectUpdateUnitExecuteReq executeReq);

    /**
     * 项目管理列表-切换云枢组织
     *
     * @param queryParams 项目管理列表查询参数
     * @return 项目管理列表
     * <AUTHOR>
     * @Date 2023/8/23
     */
    Page<ProjectResp> pageListByConditionCloudPivot(ProjectQueryParams queryParams);

    /**
     * @param req 云筑网编码
     * @return
     * <AUTHOR>
     * @date 2023/10/7
     */
    Boolean updateYZW(UpdateYzwProjectIdReq req);

    /**
     * 分页查询项目列表
     *
     * @param appKey 业务系统 appkey
     * @param projectPageReq 分頁查询参数
     * @return {@link OpenProjectPageResp}
     */
    Page<OpenProjectPageResp> getOpenProjectPage(String appKey, OpenProjectPageReq projectPageReq);

    /**
     * 更新项目经纬度信息，并触发工地更新后置事件
     *
     * @param req 请求参数
     * @return {@link Boolean}
     */
    Boolean updateLatitudeAndLongitude(ProjectLatAndLngUpdateReq req);
    /**
     * 获取项目状态枚举数据(工程、财务、商务)
     * @author: fangsixiang
     * @date: 2023/12/4 10:14
     * @param: []
     * @return: java.lang.Object
    **/
    ProjectStatusEnumDataResp getProjectStatusEnumData();

    /**
     * 更新项目效果图
     *
     * @param req 请求参数
     * @return {@link Boolean}
     */
    Boolean updateEffectPicture(ProjectEffectPictureUpdateReq req);

    /**
     * 根据map更新项目
     *
     * @param id       id
     * @param fieldMap fieldMap 要更新的字段及值
     * @return {@link Boolean}
     */
    Boolean updateProjectByMap(Long id, HashMap<String, Object> fieldMap);

    /**
     * 项目合同关系列表
     *
     * @param independentContractType 独立合同类型
     * @param independentContractId   独立合同id
     * @return {@link List}<{@link ContractFileRelationResp}>
     */
    List<ContractFileRelationResp> projectContractRelationList(Integer independentContractType,
            Long independentContractId);

    /**
     * 项目金额分类
     *
     * @param projectId 项目id
     * @return {@link ProjectAmountCategoryResp}
     */
    ProjectAmountCategoryResp getAmountCategory(Long projectId);

    /**
     * 项目导出
     *
     * @param queryParams 查询参数
     * @param response    响应
     */
    void exportProjectPage(HttpServletResponse response, ProjectQueryParams queryParams);

    /**
     * 删除项目
     *
     * @param id id
     * @return {@link Boolean}
     */
    Boolean deleteProjectById(Long id);

    /**
     * 项目信息-云筑网映射信息
     *
     * @param mappingReqs 云筑网映射信息查询参数
     * @return {@link List }<{@link ProjectOpenMappingResp }>
     */
    List<ProjectOpenMappingResp> getMappingInfo(List<ProjectYzwMappingReq> mappingReqs);

    /**
     * 根据云枢组织id列表获取项目列表
     *
     * @param appKey 业务系统 appkey
     * @param req    云枢组织id列表
     * @return {@link Page }<{@link ProjectOpenResp }>
     */
    Page<ProjectOpenResp> getProjectPageByYunshuIds(String appKey, OpenProjectPageByYunshuIdsReq req);

    /**
     * 根据云枢执行单位获取项目列表
     *
     * @param appKey      业务系统 appkey
     * @param queryParams 查询参数
     * @return {@link Page }<{@link ProjectResp }>
     */
    Page<ProjectResp> getProjectPageByExecuteUnit(String appKey, ProjectQueryByExecuteReq queryParams);

    /**
     * 分页查询项目基础档案信息
     *
     * @param openApiKey APPKEY
     * @param pageReq    请求参数
     * @return {@link Page }<{@link OpenProjectArchiveResp }>
     */
    Page<OpenProjectArchiveResp> getOpenProjectArchivedPage(String openApiKey, OpenProjectArchiveReq pageReq);

    /**
     * @param projectUpdateReq    项目更新请求参数
     * @param revisionType        修订类型
     * @param remark              修订备注
     * @param projectProgressEnum 项目进度
     * @return {@link Boolean }
     */
    Boolean updateYunshuOrgId(ProjectUpdateYunshuOrgIdReq projectUpdateReq, RevisionTypeEnum revisionType,
            String remark, ProjectProgressEnum projectProgressEnum);

    FlowNodeDataTypeEnum updateSmartSiteOfProjectProgress(@NotNull(message = "项目id不能为空") Long projectId,
        ProjectProgressEnum projectProgressEnum);

    /**
     * 获取项目错误列表
     * 
     * @param req 请求参数
     * @return {@link List }<{@link ErrorProjectResp }>
     */
    Page<ErrorProjectResp> getProjectErrorPageList(ErrorProjectPageReq req);

    /**
     * 删除项目错误列表
     *
     * @param ids ids
     * @return {@link Boolean }
     */
    Boolean deleteErrorProject(List<Long> ids);

    /**
     * 按组织 ID 获取项目财务信息
     *
     * @param yunshuOrgId 云书 org id
     * @return {@link OpenProjectFinanceResp }
     */
    OpenProjectFinanceResp getFinanceInfoByOrgId(String yunshuOrgId);

    /**
     * 投标总结列表
     *
     * @param appKey     业务系统ID
     * @param pageParams 分页参数
     * @return {@link BidSummaryResp }
     */
    //    Page<BidSummaryResp> tenderPageDetail(String appKey, ContractFileDetailReq pageParams);

    void publishSheJiYuanEpcEvent(Project project);

    /**
     * 同步财商系统MDM
     *
     * @param projectId 项目 ID
     * @return {@link Boolean }
     */
    Boolean syncFinanceMdm(Long projectId);

    String getFinanceReqApi();

    /**
     * 回调财商获取MDM信息
     *
     * @param api 应用程序接口
     * @param req 要求
     * @return {@link SyncFinanceMDMScheduled.FinanceResult }
     */
    SyncFinanceMDMScheduled.FinanceResult callFinanceMDMApi(String api, SyncFinanceMDMScheduled.FinanceMDMReq req);
}
