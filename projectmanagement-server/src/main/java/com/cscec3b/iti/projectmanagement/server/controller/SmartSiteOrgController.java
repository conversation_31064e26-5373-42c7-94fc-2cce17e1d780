package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.projectmanagement.api.ISmartOrgController;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 云枢智慧工地组织类控制器
 *
 * <AUTHOR>
 * @date 2023/08/12 23:07
 **/

@RestController
@RequestMapping(ISmartOrgController.PATH)
@Api(tags = "智慧工地组织信息")
public class SmartSiteOrgController implements ISmartOrgController {


    private final IYunshuOrgService yunshuOrgService;

    public SmartSiteOrgController(IYunshuOrgService yunshuOrgService) {
        this.yunshuOrgService = yunshuOrgService;
    }


    /**
     * 钻取方式获取组织树
     *
     * @param parentId   根节点id 为空则取当前用户所在组织
     * @param isEntities 是否实体
     * @return {@link GenericityResponse }<{@link List }<{@link ExecuteUnitTreeResp }>>
     * <AUTHOR>
     * @date 2023/08/25
     */
    @Override
    public GenericityResponse<List<ExecuteUnitTreeResp>> getTree(String parentId, boolean isEntities) {
        return ResponseBuilder.fromData(yunshuOrgService.getOrgTree(parentId, Boolean.FALSE));
    }

    /**
     * 修订时钻取方式获取组织树
     *
     * @param parentId   父节点id
     * @param isEntities 是否实体组织
     * @return {@link GenericityResponse }<{@link List }<{@link ExecuteUnitTreeResp }>>
     * <AUTHOR>
     * @date 2023/08/25
     */
    @Override
    public GenericityResponse<List<ExecuteUnitTreeResp>> getAllTree(String parentId, boolean isEntities) {
        return ResponseBuilder.fromData(yunshuOrgService.getOrgTreeForRevision(parentId, isEntities));
    }

    /**
     * 分域模糊搜索组织
     *
     * @param parentIdPath 父idPath
     * @param name         名字
     * @param abbreviation 缩写
     * @param isEntities   是否实体
     * @return
     */
    @Override
    public GenericityResponse<List<ExecuteUnitTreeResp>> fuzzySearchOrg(
            @NotBlank(message = "组织idPath不能为空") String parentIdPath, String name, String abbreviation,
            boolean isEntities) {
        return ResponseBuilder.fromData(yunshuOrgService.fuzzySearchOrg(parentIdPath, name, abbreviation, isEntities));
    }

    @Override
    public GenericityResponse<String> getTreeIdByOrgId(String orgId) {
        return ResponseBuilder.fromData(yunshuOrgService.getSmartOrgTreeId(orgId));
    }

    @Override
    public GenericityResponse<List<ExecuteUnitTreeResp>> getTreeRevisionRealTime(String parentTreeId,
            boolean isEntities) {
        return ResponseBuilder.fromData(yunshuOrgService.getOrgTreeRevisionForRealTime(parentTreeId, isEntities));
    }


}
