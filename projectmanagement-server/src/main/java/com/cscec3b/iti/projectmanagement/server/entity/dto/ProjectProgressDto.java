package com.cscec3b.iti.projectmanagement.server.entity.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 项目监控进度dto
 *
 * <AUTHOR>
 * @date 2023/08/02 09:45
 **/

@Getter
@Setter
public class ProjectProgressDto {

    /**
     * 主键ID 与项目表主键保持一致
     */
    private Long projectId;

    /**
     * 签约时间
     */
    private Long signTime;

    /**
     * 签约状态 0：未签约 2：已签约
     */
    private Integer signStatus;


    /**
     * 项目预警状态 0：未触发 1：已触发 2：已解除
     */
    private Integer warnStatus;


    /**
     * 财商传参时间(财商立项时间)
     */
    private Long toFinanceTime;

    /**
     * 财商立项完成时间
     */
    private Long approveFinishTime;

    /**
     * 财商立项状态 0：未立项 1:立项中 2：已立项 (财商)
     */
    private Integer approveStatus;


    /**
     * 财商系统备注
     */
    private String financeRemarks;


    /**
     * 智慧工地查询时间
     */
    private Long smartQueryTime;

    /**
     * 智慧工地立项状态
     */
    private Integer smartApproveStatus;

    /**
     * UC传参时间(智慧工地立项完成时间，uc项目部创建时间)
     */
    private Long toUcTime;

    /**
     * 智慧工地系统备注
     */
    private String smartRemarks;


    /**
     * UC创建项目部状态
     */
    private Integer ucDepartStatus;

    /**
     * UC系统备注
     */
    private String ucRemarks;


    /**
     * 项目中心系统备注
     */
    private String cpmRemarks;


    /**
     * 创建指挥部状态
     */
    private Integer createCommandStatus;

    /**
     * 创建指挥部发起时间
     */
    private Long createCommandStartTime;

    /**
     * 创建指挥部完成时间
     */
    private Long createCommandEndTime;


}
