package com.cscec3b.iti.projectmanagement.server.service.impl;

import static com.cscec3b.iti.projectmanagement.server.constant.Constants.YUNSHU_DEPT_INFO;
import static com.cscec3b.iti.projectmanagement.server.constant.Constants.YUNSHU_TREE_CURR_INFO;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.cache.annotation.CacheResult;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryUsersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.org.UserAndOrgDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.OrgLabelResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.UserOrgListResp;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.config.YunShuConfig;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.SyncUpdateOrgEvent;
import com.cscec3b.iti.projectmanagement.server.service.IOrgService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.g3.G3OrgService;
import com.g3.org.api.dto.executeForQueryDepartmentList.resp.ExecuteForQueryDepartmentListResp;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrgDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrganizationResp;
import com.g3.org.api.dto.resp.QueryUsersResponse;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.g3.org.api.dto.resp.org.YunshuOrgTreeEntity;
import com.g3.org.api.dto.resp.user.QueryUsersDTO;
import com.g3.org.api.dto.resp.user.UserAndOrgDTO;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.odin.freyr.common.orika.BeanMapUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 组织相关服务
 *
 * <AUTHOR>
 * @date 2023/08/12 23:10
 **/

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OrgServiceImpl implements IOrgService {

    /**
     * 线程池定义
     */
    private static final ForkJoinPool FORK_JOIN_POOL = new ForkJoinPool(6);
    /**
     * 批量数据量
     */
    private static final int BATCH_SIZE = 2000;
    private final List<ExecuteUnitTreeDto> batchList = new ArrayList<>();
    private final G3OrgService g3OrgService;

    private final YunShuConfig yunShuConfig;

    private final Executor cpmTaskExecutor;

    private final IYunshuOrgSyncService yunshuOrgSyncService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final ApplicationEventPublisher pushlisher;

    private final UcOpenApiProperties ucOpenApiProperties;


    /**
     * 钻取组织树
     *
     * @param parentId   父id
     * @param isEntities 是否实体
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    public List<ExecuteUnitTreeResp> getOrgTree(String parentId,  boolean isEntities) {
        // 为空则从当前用户节点获取组织树
        if (StringUtils.isBlank(parentId)) {
            parentId = LoginUserUtil.getTreeId();
            if (StringUtils.isBlank(parentId)) {
                throw new BusinessException(8010102);
            }
            // 根节点直接获取当前节点信息
            ExecuteUnitTreeDto resp = yunshuOrgSyncService.getUnitTreeDtoCacheById(parentId);
            if (Objects.isNull(resp)) {
                final YunshuOrgSync orgSync = yunshuOrgSyncService.getById(parentId);
                resp = BeanMapUtils.map(orgSync, ExecuteUnitTreeDto.class);
            }
            return Lists.newArrayList(BeanMapUtils.map(resp, ExecuteUnitTreeResp.class));
        }
        // 获取子节点
        return yunshuOrgSyncService.getChildAndFilter(parentId, null, null, isEntities);
    }

    /**
     * 钻取组织树（修订执行单位） parentId为空时，则取精益建造根节点
     *
     * @param parentId 根节点id
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    @CacheResult
    public List<ExecuteUnitTreeResp> getOrgTreeForRevision(String parentId, boolean isEntities) {
        if (StringUtils.isBlank(parentId)) {
            parentId = getRootTreeIdFromYunshu();
        }
        return yunshuOrgSyncService.getChildAndFilter(parentId, null, null, isEntities);
    }

    /**
     * 模糊搜索组织
     *
     * @param parentTreeId 父id
     * @param name         名字
     * @param abbreviation 缩写
     * @param isEntities   是否实体
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    @CacheResult
    public List<ExecuteUnitTreeResp> fuzzySearchOrg(String parentTreeId, String name, String abbreviation,
        boolean isEntities) {
        final List<ExecuteUnitTreeResp> dtoList = yunshuOrgSyncService.getAllChildAndFilter(parentTreeId, name,
                abbreviation, isEntities);

        return listToTree(dtoList);
    }

    /**
     * 列表转树，非根节点不能丢失与根节点进行平铺
     *
     * @param list 列表
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/23
     */
    private List<ExecuteUnitTreeResp> listToTree(List<ExecuteUnitTreeResp> list) {
        final Map<String, ExecuteUnitTreeResp> nodeMap =
            list.stream().collect(Collectors.toMap(ExecuteUnitTreeDto::getId, o -> o, (o1, o2) -> o2));

        List<ExecuteUnitTreeResp> rootNodes = new ArrayList<>();
        for (ExecuteUnitTreeResp node : list) {
            ExecuteUnitTreeResp parent = nodeMap.get(node.getParentId());
            if (parent == null) {
                rootNodes.add(node);
            } else {
                parent.getChildren().add(node);
            }
        }
        return rootNodes;
    }

    /**
     * 从云枢获取组织树
     *
     * @param orgId       org id
     * @param isEntityOrg 是实体机构
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public List<YunshuOrgDepartmentTreeModel> getOrgTreeFromYunshu(String orgId, boolean isEntityOrg) {
        log.debug("获取云枢组织信息，id:{}", orgId);
        // orgId 如果为空，则从根组织下取值
        if (StringUtils.isBlank(orgId)) {
            orgId = getRootTreeIdFromYunshu();
        }
        // orgId不为空则取下级部门
        final ExecuteGetChildDepartmentsResp childDepartmentResp = g3OrgService.executeGetChildDepartments(orgId);
        checkResponseEntity(childDepartmentResp);
        final List<YunshuOrgDepartmentTreeModel> departments = childDepartmentResp.getData();
        if (isEntityOrg) {
            return departments.stream().filter(o -> yunShuConfig.getEntityOrg().contains(o.getOrgCategory()))
                .collect(Collectors.toList());
        }
        return departments;
    }

    /**
     * 从云枢获取根节点树id
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/08/22
     */
    @PmReTry
    private String getRootTreeIdFromYunshu() {
        String orgId;
        final ExecuteGetOrganizationResp organizationResponse = g3OrgService.executeGetOrganization();
        checkResponseEntity(organizationResponse);
        // 查询详情
        final YunshuOrgTreeEntity entity = organizationResponse.getData();
        if (ObjectUtil.isNull(entity)) {
            throw new BusinessException(-1);
        }
        orgId = entity.getId();
        return orgId;
    }

    /**
     * 从云枢获取部门信息
     *
     * @param departmentId 部门id
     * @return {@link YunshuOrgDepartmentEntity }
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public YunshuOrgDepartmentEntity getDepartmentFromYunshu(String departmentId) {
        if (StringUtils.isBlank(departmentId)) {
            return null;
        }
        final ExecuteGetDepartmentResp orgDepartmentResponse = g3OrgService.executeGetDepartment(departmentId);
        checkResponseEntity(orgDepartmentResponse);
        return orgDepartmentResponse.getData();

    }

    /**
     * 检查响应
     *
     * @param response 响应
     * <AUTHOR>
     * @date 2023/10/30
     */
    private void checkResponseEntity(CloudPivotResponse<?> response) {
        if (ObjectUtils.allNotNull(response, response.getErrcode()) && !response.getErrcode().equals(0L)) {
            log.error("响应失败:{}", response);
            throw new FrameworkException(80107000, JsonUtils.toJsonStr(response));
        }
    }

    public YunshuOrgDepartmentTreeModel getCurrentTreemodel(String parentTreeId) {
        if (StringUtils.isBlank(parentTreeId)) {
            // 精益建造树根节点
            final String rootTreeId = getRootTreeIdFromYunshu();
            // 三局集团
            final List<YunshuOrgDepartmentTreeModel> orgTree = getOrgTreeFromYunshu(rootTreeId, false);
            if (CollectionUtils.isEmpty(orgTree)) {
                log.error("通过G3Service获取组织信息失败, treeId: {}", parentTreeId);
                throw new BusinessException(8010105);
            }
            parentTreeId = orgTree.get(0).getId();
        }

        // 通过G3Service获取组织信息
        final ExecuteGetOrgDepartmentResp orgDepartmentResp = g3OrgService.executeGetOrgDepartment(parentTreeId);
        checkResponseEntity(orgDepartmentResp);
        final YunshuOrgDepartmentTreeModel treeModel = orgDepartmentResp.getData();
        if (ObjectUtils.isEmpty(treeModel)) {
            log.error("通过G3Service获取组织信息失败, treeId: {}", parentTreeId);
            throw new BusinessException(8010105);
        }
        return treeModel;
    }

    private ExecuteUnitTreeDto getParentIdPathNameInfo(YunshuOrgDepartmentTreeModel treeModel) {
        final YunshuOrgSync yunshuOrgSync = yunshuOrgSyncService.getById(treeModel.getTreeParentId());
        if (Objects.isNull(yunshuOrgSync)) {
            final String queryCode = treeModel.getQueryCode();
            // 三局集团
            if (CharSequenceUtil.count(queryCode, "#") <= 1) {
                return null;
            }
            final ExecuteUnitTreeDto unitTreeDto =
                    new ExecuteUnitTreeDto().setId(treeModel.getId()).setDeptId(treeModel.getDepartmentId());
            final String[] splitQueryCode = queryCode.split("#");
            for (int i = splitQueryCode.length - 2; i > 0; i--) {
                final YunshuOrgDepartmentTreeModel model = getCurrentTreemodel(splitQueryCode[i]);
                final YunshuOrgDepartmentEntity department = getDepartmentFromYunshu(model.getDepartmentId());
                if (Objects.nonNull(department)) {
                    final String orgFullName = department.getOrgFullName();
                    final String orgShortName = department.getOrgShortName();
                    unitTreeDto.setIdPathName(Joiner.on("/").skipNulls().join(orgFullName,
                            unitTreeDto.getIdPathName()));
                    unitTreeDto.setIdPathAbbreviation(Joiner.on("/").skipNulls().join(orgShortName, unitTreeDto.getIdPathAbbreviation()));
                }
            }
            return unitTreeDto;
        }
        return  BeanUtil.copyProperties(yunshuOrgSync, ExecuteUnitTreeDto.class);
    }


    @Override
    @Lock(lockKey = "syncYunshuOrg", leaseTime = 30 * 60 * 1000L)
    public void syncYunshuOrg(String parentTreeId) {
        // 初始化缓存operation
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> treeForKeyCache =
                redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO);
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> deptForKeyCache =
                redisTemplate.boundHashOps(YUNSHU_DEPT_INFO);

        // 获取根节点
        // get parentTreeId of departmentInfo
        final YunshuOrgDepartmentTreeModel treeModel = getCurrentTreemodel(parentTreeId);
        //parentIdPath
        final String parentIdPath = treeModel.getQueryCode();
        // sync mark
        final String syncMark = IdUtil.objectId();
        // getParentInfo 构造全路径名称
        ExecuteUnitTreeDto parentDto = getParentIdPathNameInfo(treeModel);




        if (Objects.nonNull(parentDto)) {
            treeForKeyCache.put(parentDto.getId(), parentDto);
            deptForKeyCache.put(parentDto.getDeptId(), parentDto);
        }
        syncRecursive(treeModel, parentDto, syncMark, treeForKeyCache, deptForKeyCache);

        flushToDatabase();
        // 移除未同步数据
        yunshuOrgSyncService.deleteUnSynchronizedData(parentIdPath, syncMark);
        // 设置过期时间 23小时
        treeForKeyCache.expire(23 * 60L,TimeUnit.HOURS);
        deptForKeyCache.expire(23 * 60L,TimeUnit.HOURS);
    }

    private synchronized void flushToDatabase() {
        yunshuOrgSyncService.batchInsertOrUpdate(batchList);
        batchList.clear();
    }

    private void syncRecursive(YunshuOrgDepartmentTreeModel node, ExecuteUnitTreeDto parent, String syncMark,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeForKeyCache,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptForKeyCache) {
        final CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            try {
                // RATE_LIMITER.acquire();
                final ExecuteUnitTreeDto unitDto = getEntityAndGenUnitDto(node, parent, syncMark);
                List<YunshuOrgDepartmentTreeModel> orgTreeFromYunshu = getOrgTreeFromYunshu(node.getId(), false);
                // 如果没有子节点，则设置为叶子节点
                if (CollectionUtils.isEmpty(orgTreeFromYunshu)) {
                    unitDto.setLeaf(Boolean.TRUE);
                }
                batchList.add(unitDto);
                if (batchList.size() >= 1000) {
                    flushToDatabase();
                }
                treeForKeyCache.put(unitDto.getId(), unitDto);
                deptForKeyCache.put(unitDto.getDeptId(), unitDto);
                for (final YunshuOrgDepartmentTreeModel model : orgTreeFromYunshu) {
                    syncRecursive(model, unitDto, syncMark, treeForKeyCache, deptForKeyCache);
                }

            } catch (Exception e) {
                log.error("从Yunshu获取部门信息失败, deptId: {}", node.getDepartmentId(), e);
                // 发生错误，跳过此部门
            }
        }, FORK_JOIN_POOL);
        future.join();
    }

    @NotNull
    private ExecuteUnitTreeDto getEntityAndGenUnitDto(YunshuOrgDepartmentTreeModel node, ExecuteUnitTreeDto parent,
            String syncMark) {
        // 尝试从Yunshu获取部门信息
        YunshuOrgDepartmentEntity department = getDepartmentFromYunshu(node.getDepartmentId());
        final String fullName =
                department != null ? department.getOrgFullName() : node.getDepartmentName();
        final String abbreviation = department != null ? department.getOrgShortName() : node.getDepartmentName();
        final ExecuteUnitTreeDto unitDto = new ExecuteUnitTreeDto();
        final String queryCode = node.getQueryCode();
        final int level = CharSequenceUtil.count(queryCode, "#");
        unitDto.setId(node.getId()).setName(fullName).setDeptId(node.getDepartmentId())
                .setAbbreviation(abbreviation).setIdPath(queryCode).setLevel(level).setSyncMark(syncMark)
                .setCode(node.getDepartmentCode()).setOrgType(node.getOrgCategory())
                .setParentId(node.getTreeParentId()).setDeptSort(node.getDeptSort())
                .setTreeName(node.getSourceDepartmentName());
        // 获取
        StringBuilder idPathName = new StringBuilder(unitDto.getName());
        StringBuilder idPathAbbreviation = new StringBuilder(unitDto.getAbbreviation());
        if (Objects.nonNull(parent)) {
            idPathName.insert(0, parent.getIdPathName()).insert(parent.getIdPathName().length(),
                    Constants.ID_PATH_CONNECTOR);
            idPathAbbreviation.insert(0, parent.getIdPathAbbreviation())
                    .insert(parent.getIdPathAbbreviation().length(), Constants.ID_PATH_CONNECTOR);
        }
        unitDto.setIdPathName(idPathName.toString()).setIdPathAbbreviation(idPathAbbreviation.toString());
        return unitDto;
    }

    /**
     * 缓存yunshuorg
     *
     * @param parentTreeId 父id
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Lock(lockKey = "cacheYunshuOrgTree", leaseTime = 30 * 60 * 1000L)
    public void cacheYunshuOrgTree(String parentTreeId) {

        // 初始化缓存operation
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations =
            redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO);
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations =
            redisTemplate.boundHashOps(YUNSHU_DEPT_INFO);

        final String parentId = StringUtils.isBlank(parentTreeId) ? getRootTreeIdFromYunshu() : parentTreeId;

        final String syncMark = IdUtil.objectId();
        log.info("云枢同步标识：{}, time: {}", parentId, Instant.now().toEpochMilli());
        // 获取根节点 并从根节点缓存所有组织
        CompletableFuture.runAsync(() -> {
            log.debug("begin to sync yunshu org");
                    ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap = new ConcurrentHashMap<>(64);
            cacheYunshuOrg(parentId, tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark);
            log.debug("sync yunshu org end");
        }, cpmTaskExecutor)
            // 移除未同步数据
            .thenRun(() -> {
                log.debug("remove unSync data");
                // 先查询是同步数据是否写入成功
                if (yunshuOrgSyncService.getCountBySyncMark(syncMark) > 0) {
                    // 支持按层级删除
                    final ExecuteUnitTreeDto unitTreeDto = treeBoundHashOperations.get(parentId);
                    String deleteIdPath = ObjectUtils.isEmpty(unitTreeDto) ? null : unitTreeDto.getIdPath();
                    yunshuOrgSyncService.deleteUnSynchronizedData(deleteIdPath, syncMark);
                }
            });
        // 设置缓存23小时过期(同步组织大概在30分钟左右)
        treeBoundHashOperations.expire(23, TimeUnit.HOURS);
        deptBoundHashOperations.expire(23, TimeUnit.HOURS);
    }


    /**
     * 缓存yunshu组织
     *
     * @param treeId                  树id
     * @param tempMap                 缓存Map
     * @param treeBoundHashOperations treeBoundHashOperations
     * @param syncMark                同步标志
     * <AUTHOR>
     * @date 2023/08/24
     */
    private void cacheYunshuOrg(String treeId, ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations,
            String syncMark) {
        // 获取子节点
        final List<YunshuOrgDepartmentTreeModel> orgTree = getOrgTreeFromYunshu(treeId, false);
        if (CollectionUtils.isNotEmpty(orgTree)) {
            log.info("开始缓存{}的子节点:{}条", treeId, orgTree.size());
            // 递归缓存
            cacheAsync(orgTree, tempMap, treeBoundHashOperations, deptBoundHashOperations,syncMark);
        }

        List<ExecuteUnitTreeDto> arrays = new ArrayList<>(tempMap.values());
        // 重新缓存

        // 分批保存到数据表
        while (!arrays.isEmpty()) {
            int endIndex = Math.min(BATCH_SIZE, arrays.size());
            List<ExecuteUnitTreeDto> needInsert = new ArrayList<>(arrays.subList(0, endIndex));
            yunshuOrgSyncService.batchInsertOrUpdate(needInsert);
            arrays.removeAll(needInsert);
        }
    }

    /**
     * 异步缓存云枢组织
     *
     * @param orgTree                 云枢部门树
     * @param tempMap                 临时Map缓存
     * @param treeBoundHashOperations redisOperations
     * @param deptBoundHashOperations
     * @param syncMark                同步标志
     * <AUTHOR>
     * @date 2023/10/30
     */
    private void cacheAsync(List<YunshuOrgDepartmentTreeModel> orgTree,
                            ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
        BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
        BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations, String syncMark) {
        ConcurrentLinkedQueue<YunshuOrgDepartmentTreeModel> treeModels = new ConcurrentLinkedQueue<>();
        ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap = new ConcurrentHashMap<>(8);
        ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap = new ConcurrentHashMap<>(8);
        // 异步任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (YunshuOrgDepartmentTreeModel org : orgTree) {
            // 开启异步线程
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                YunshuOrgDepartmentEntity department = null;
                try {
                    // 尝试从Yunshu获取部门信息
                    department = getDepartmentFromYunshu(org.getDepartmentId());
                } catch (Exception e) {
                    log.error("从Yunshu获取部门信息失败, deptId: {}", org.getDepartmentId(), e);
                    // 发生错误，跳过此部门
                }
                final String fullName =
                        department != null ? department.getOrgFullName() : org.getDepartmentName();
                final String abbreviation = department != null ? department.getOrgShortName() : org.getDepartmentName();
                final ExecuteUnitTreeDto unitDto = new ExecuteUnitTreeDto();
                final String queryCode = org.getQueryCode();
                final int level = CharSequenceUtil.count(queryCode, "#");
                unitDto.setId(org.getId()).setName(fullName).setDeptId(org.getDepartmentId())
                    .setAbbreviation(abbreviation).setIdPath(queryCode).setLevel(level).setSyncMark(syncMark)
                    .setCode(org.getDepartmentCode()).setOrgType(org.getOrgCategory())
                        .setParentId(org.getTreeParentId()).setDeptSort(org.getDeptSort())
                        .setTreeName(org.getSourceDepartmentName());

                final ExecuteUnitTreeDto parentTreeDto = tempMap.get(org.getTreeParentId());
                StringBuilder idPathName = new StringBuilder(unitDto.getName());
                StringBuilder idPathAbbreviation = new StringBuilder(unitDto.getAbbreviation());
                if (parentTreeDto != null) {
                    idPathName.insert(0, parentTreeDto.getIdPathName()).insert(parentTreeDto.getIdPathName().length(),
                        Constants.ID_PATH_CONNECTOR);
                    idPathAbbreviation.insert(0, parentTreeDto.getIdPathAbbreviation())
                        .insert(parentTreeDto.getIdPathAbbreviation().length(), Constants.ID_PATH_CONNECTOR);
                }
                unitDto.setIdPathName(idPathName.toString()).setIdPathAbbreviation(idPathAbbreviation.toString());

                List<YunshuOrgDepartmentTreeModel> orgTreeFromYunshu = new ArrayList<>();
                try {
                    // 尝试从Yunshu获取组织树信息
                    orgTreeFromYunshu = getOrgTreeFromYunshu(org.getId(), false);

                } catch (Exception e) {
                    log.error("从Yunshu获取组织树信息失败, orgId: {}", org.getId(), e);
                    // 发生错误，跳过此组织树
                }
                // 如果没有子节点，则设置为叶子节点
                if (CollectionUtils.isEmpty(orgTreeFromYunshu)) {
                    unitDto.setLeaf(Boolean.TRUE);
                }
                // 暂存
                tempMap.put(org.getId(), unitDto);
                // 以treeId为Key 缓存
                treeCacheMap.put(org.getId(), unitDto);
                // 以deptId 为Key 缓存
                deptCacheMap.put(org.getDepartmentId(), unitDto);
                treeModels.addAll(orgTreeFromYunshu);
                // 注意此处线程数量，太多会导致外部系统异常，数据丢失
            }, FORK_JOIN_POOL);
            // 加入异步任务列表
            futures.add(future);
        }
        // 等待所有线程完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 每处理完一层级的数据，就缓存一次，保证下级组织能获取上级组织信息
        // 同时去更新
        if (!treeCacheMap.isEmpty()) {
            treeBoundHashOperations.putAll(treeCacheMap);
            deptBoundHashOperations.putAll(deptCacheMap);
            // 更新项目表数据
            final Collection<ExecuteUnitTreeDto> yunshuDtos = treeCacheMap.values();
            // 更新业务表中的数据
            pushlisher.publishEvent(new SyncUpdateOrgEvent(this, new ArrayList<>(yunshuDtos)));
        }

        if (!treeModels.isEmpty()) {
            cacheAsync(new ArrayList<>(treeModels), tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark);
        }
    }

    @Override
	public void getYunshuOrgNamePath(String orgId, List<String> orgFullName, List<String> orgAbbrName) {
		if (StringUtils.isBlank(orgId)) {
			return;
		}
        final ExecuteUnitTreeDto dto = yunshuOrgSyncService.getUnitTreeDtoCacheByDeptId(orgId);
        // ExecuteUnitTreeDto dto = (ExecuteUnitTreeDto)redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO).get(orgId);
		if (Objects.isNull(dto)) {
			return;
		}
		orgFullName.add(dto.getName());
		orgAbbrName.add(dto.getAbbreviation());
        getYunshuOrgNamePath(dto.getParentId(), orgFullName, orgAbbrName);
	}


    /**
     * 获取实体组织
     *
     * @param deptId
     * @return {@link ExecuteUnitTreeDto }
     * <AUTHOR>
     * @date 2023/10/29
     */
    @Override
    public ExecuteUnitTreeDto getEntityOrgByDeptId(String deptId) {
        String treeId = null;
        String newDeptId = null;
        final ExecuteUnitTreeDto executeUnitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheByDeptId(deptId);
        if (Objects.isNull(executeUnitTreeDto)) {
            final YunshuOrgSync yunshuOrgSync = yunshuOrgSyncService.getYunshuOrgByDeptId(deptId);
            if (Objects.isNull(yunshuOrgSync)) {
                final ExecuteGetDepartmentResp executeGetDepartmentResp = g3OrgService.executeGetDepartment(deptId);
                checkResponseEntity(executeGetDepartmentResp);
                final YunshuOrgDepartmentEntity deptInfo = executeGetDepartmentResp.getData();
                if (Objects.isNull(deptInfo)) {
                    throw new BusinessException(8010105);
                }
                treeId = deptInfo.getTreeId();
            } else {
                treeId = yunshuOrgSync.getId();
            }
        } else {
            treeId = executeUnitTreeDto.getId();
        }

        return getEntityOrgByTreeId(treeId);
    }

    @Override
    public ExecuteUnitTreeDto getEntityOrgByTreeId(String treeId) {
        ExecuteUnitTreeDto executeUnitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheById(treeId);
        if (Objects.isNull(executeUnitTreeDto)) {
            YunshuOrgSync orgSync = yunshuOrgSyncService.selectById(treeId);
            if (ObjectUtils.isEmpty(orgSync)) {
                // 通过G3Service获取组织信息
                final ExecuteGetOrgDepartmentResp orgDepartmentResp = g3OrgService.executeGetOrgDepartment(treeId);
                checkResponseEntity(orgDepartmentResp);
                final YunshuOrgDepartmentTreeModel treeModel = orgDepartmentResp.getData();
                if (ObjectUtils.isEmpty(treeModel)) {
                    log.error("通过G3Service获取组织信息失败, treeId: {}", treeId);
                    throw new BusinessException(8010105);
                }
                executeUnitTreeDto =
                        new ExecuteUnitTreeDto().setId(treeModel.getId()).setName(treeModel.getDepartmentName())
                                .setDeptId(treeModel.getDepartmentId()).setAbbreviation(treeModel.getSourceDepartmentName())
                                .setIdPath(treeModel.getQueryCode()).setCode(treeModel.getDepartmentCode())
                                .setOrgType(treeModel.getOrgCategory()).setParentId(treeModel.getTreeParentId())
                                .setDeptSort(treeModel.getDeptSort());
            } else {
                executeUnitTreeDto = BeanUtil.copyProperties(orgSync, ExecuteUnitTreeDto.class);
            }
        }
        final Integer orgType = executeUnitTreeDto.getOrgType();

        // 实体则返回
        if (yunShuConfig.getEntityOrg().contains(orgType)) {
            return executeUnitTreeDto;
        }
        return getEntityOrgByTreeId(executeUnitTreeDto.getParentId());
    }


    /**
     * 通过中标未立项的组织id查询智慧工地的组织树Id
     *
     * @param parentOrgId 上级组织id
     * @return {@link List}<{@link ExecuteUnitTreeResp}>
     */
    @Override
    public String getSmartOrgTreeId(String parentOrgId) {
        final ExecuteGetDepartmentResp orgDepartmentResponse = g3OrgService.executeGetDepartment(parentOrgId);
        checkResponseEntity(orgDepartmentResponse);
        final YunshuOrgDepartmentEntity entity = orgDepartmentResponse.getData();
        return Optional.ofNullable(entity).map(YunshuOrgDepartmentEntity::getTreeId)
                .orElseThrow(() -> new BusinessException(8010105));
    }
    @Override
    public List<ExecuteUnitTreeResp> getOrgTreeRevisionForRealTime(String parentId, boolean isEntities) {
        final List<YunshuOrgDepartmentTreeModel> orgTreeFromYunshu = getOrgTreeFromYunshu(parentId, isEntities);
        return orgTreeFromYunshu.stream().parallel().map(org -> {
            final ExecuteUnitTreeResp unitDto = new ExecuteUnitTreeResp();
            final String queryCode = org.getQueryCode();
            final int level = CharSequenceUtil.count(queryCode, "#");
            unitDto.setId(org.getId()).setName(org.getDepartmentName()).setDeptId(org.getDepartmentId())
                    .setAbbreviation(org.getSourceDepartmentName()).setIdPath(queryCode).setLevel(level)
                    .setCode(org.getDepartmentCode()).setOrgType(org.getOrgCategory())
                    .setParentId(org.getTreeParentId()).setDeptSort(org.getDeptSort()).setOrgType(org.getOrgCategory());
            return unitDto;

        }).collect(Collectors.toList());
    }


    // @Override
    @Override
    @Lock(lockKey = "cacheYunshuOrgTree", leaseTime = 2 * 60 * 60 * 1000L)
    public void cacheYunshuOrgTreeV1(String parentTreeId) {
        // 创建固定大小为8 的线程池
        final ThreadFactory factory =
                new ThreadFactoryBuilder().setNameFormat("sync-yunshu-org-task-%d").setPriority(6).build();
        final ExecutorService executorService = Executors.newFixedThreadPool(8, factory);

        String parentId = determineParentId(parentTreeId);
        String syncMark = IdUtil.objectId();
        log.info("云枢同步标识：{}, time: {}", parentId, Instant.now().toEpochMilli());

        CompletableFuture.runAsync(() -> syncOrganizations(parentId, syncMark))
//        CompletableFuture.runAsync(() -> syncOrganizations(parentId, syncMark, executorService), executorService)
                .thenRun(() -> removeUnsynchronizedData(parentId, syncMark))
                .thenRun(this::setCacheExpiration)
                .thenRun(executorService::shutdown);
    }

    @Override
    public List<UserOrgListResp> getCurrentEntityOrgList() {
        final ExecuteForQueryDepartmentListResp departmentListResp =
                g3OrgService.executeForQueryDepartmentList(LoginUserUtil.userId(), Boolean.TRUE);
        checkResponseEntity(departmentListResp);
        return departmentListResp.getData().stream().map(UserOrgListResp::new).collect(Collectors.toList());

        // return departmentListResp.getData();
    }

    private String determineParentId(String parentTreeId) {
        return StringUtils.isBlank(parentTreeId) ? getRootTreeIdFromYunshu() : parentTreeId;
    }

    private void syncOrganizations(String parentId, String syncMark) {
//    private void syncOrganizations(String parentId, String syncMark, ExecutorService executors) {
        log.debug("开始同步云枢组织");
        ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap = new ConcurrentHashMap<>(8);
        BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations =
                redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO);
        BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations =
                redisTemplate.boundHashOps(YUNSHU_DEPT_INFO);

        syncOrgTree(parentId, tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark);
//        syncOrgTree(parentId, tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark, executors);

        batchInsertOrUpdate(new ArrayList<>(tempMap.values()));
        log.debug("云枢组织同步结束");
    }

    private void syncOrgTree(String treeId, ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations,
            String syncMark) {
//            String syncMark, ExecutorService executors) {
        List<YunshuOrgDepartmentTreeModel> orgTree = getOrgTreeFromYunshu(treeId, false);
        if (CollectionUtils.isEmpty(orgTree)) {
            return;
        }

        log.info("开始缓存{}的子节点:{}条", treeId, orgTree.size());

        ConcurrentLinkedQueue<YunshuOrgDepartmentTreeModel> treeModels = new ConcurrentLinkedQueue<>(orgTree);
        ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap = new ConcurrentHashMap<>(8);
        ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap = new ConcurrentHashMap<>(8256);
        orgTree.forEach(org -> {
            processOrg(org, tempMap, treeCacheMap, deptCacheMap, treeModels, syncMark);
        });
//        CompletableFuture.allOf(orgTree.stream()
//                .map(org -> CompletableFuture.runAsync(() ->
//                        processOrg(org, tempMap, treeCacheMap, deptCacheMap, treeModels, syncMark)))
////                        processOrg(org, tempMap, treeCacheMap, deptCacheMap, treeModels, syncMark), executors))
//                .toArray(CompletableFuture[]::new)).join();

        updateCache(treeCacheMap, deptCacheMap, treeBoundHashOperations, deptBoundHashOperations);

//        while (!treeModels.isEmpty()) {
//            syncOrgTree(treeModels.poll().getId(), tempMap, treeBoundHashOperations, deptBoundHashOperations,
//                    syncMark);
////                    syncMark, executors);
//        }
    }

    private void processOrg(YunshuOrgDepartmentTreeModel org,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap,
            ConcurrentLinkedQueue<YunshuOrgDepartmentTreeModel> treeModels,
            String syncMark) {
//        try {
//            YunshuOrgDepartmentEntity department = getDepartmentFromYunshu(org.getDepartmentId());
//
//            ExecuteUnitTreeDto unitDto = createExecuteUnitTreeDto(org, department, tempMap, syncMark);
//
//            List<YunshuOrgDepartmentTreeModel> childOrgTree = getOrgTreeFromYunshu(org.getId(), false);
//
//            // 如果没有子节点，则设置为叶子节点
//            unitDto.setLeaf(CollectionUtils.isEmpty(childOrgTree));
//
//            tempMap.put(org.getId(), unitDto);
//            treeCacheMap.put(org.getId(), unitDto);
//            deptCacheMap.put(org.getDepartmentId(), unitDto);
//            treeModels.addAll(childOrgTree);
//            childOrgTree.forEach(e -> {
//                YunshuOrgDepartmentEntity dept = getDepartmentFromYunshu(e.getDepartmentId());
//
//                ExecuteUnitTreeDto dto = createExecuteUnitTreeDto(e, dept, tempMap, syncMark);
//
//                List<YunshuOrgDepartmentTreeModel> childrenTree = getOrgTreeFromYunshu(e.getId(), false);
//
//                // 如果没有子节点，则设置为叶子节点
//                dto.setLeaf(CollectionUtils.isEmpty(childrenTree));
//
//                tempMap.put(e.getId(), dto);
//                treeCacheMap.put(e.getId(), dto);
//                deptCacheMap.put(e.getDepartmentId(), dto);
//            });
//        } catch (Exception e) {
//            log.error("处理组织信息失败, orgId: {}", org.getId(), e);
//        }
        try {
            YunshuOrgDepartmentEntity department = getDepartmentFromYunshu(org.getDepartmentId());
            if (department == null) {
                log.warn("部门信息为空, orgId: {}", org.getId());
                return;
            }

            ExecuteUnitTreeDto unitDto = createExecuteUnitTreeDto(org, department, tempMap, syncMark);
            List<YunshuOrgDepartmentTreeModel> childOrgTree = getOrgTreeFromYunshu(org.getId(), false);

            // 如果没有子节点，则设置为叶子节点
            unitDto.setLeaf(CollectionUtils.isEmpty(childOrgTree));

            tempMap.put(org.getId(), unitDto);
            treeCacheMap.put(org.getId(), unitDto);
            deptCacheMap.put(org.getDepartmentId(), unitDto);
            treeModels.addAll(childOrgTree);

            processChildOrgs(childOrgTree, treeModels, syncMark, tempMap, treeCacheMap, deptCacheMap);
        } catch (Exception e) {
            log.error("处理组织信息失败, orgId: {}", org.getId(), e);
        }
    }

    private void processChildOrgs(List<YunshuOrgDepartmentTreeModel> childOrgTree,
                                  ConcurrentLinkedQueue<YunshuOrgDepartmentTreeModel> treeModels,
                                  String syncMark,
                                  ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
                                  ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap,
                                  ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap) {
        childOrgTree.forEach(e -> {
            try {
                YunshuOrgDepartmentEntity dept = getDepartmentFromYunshu(e.getDepartmentId());
                if (dept == null) {
                    log.warn("子部门信息为空, orgId: {}", e.getId());
                    return;
                }

                ExecuteUnitTreeDto dto = createExecuteUnitTreeDto(e, dept, tempMap, syncMark);
                List<YunshuOrgDepartmentTreeModel> childrenTree = getOrgTreeFromYunshu(e.getId(), false);

                // 如果没有子节点，则设置为叶子节点
                dto.setLeaf(CollectionUtils.isEmpty(childrenTree));

                tempMap.put(e.getId(), dto);
                treeCacheMap.put(e.getId(), dto);
                deptCacheMap.put(e.getDepartmentId(), dto);
                treeModels.addAll(childrenTree);
                processChildOrgs(childrenTree, treeModels, syncMark, tempMap, treeCacheMap, deptCacheMap);
            } catch (Exception ex) {
                log.error("处理子组织信息失败, orgId: {}", e.getId(), ex);
            }
        });
    }

    private ExecuteUnitTreeDto createExecuteUnitTreeDto(YunshuOrgDepartmentTreeModel org,
            YunshuOrgDepartmentEntity department,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            String syncMark) {
        String fullName = department != null ? department.getOrgFullName() : org.getDepartmentName();
        String abbreviation = department != null ? department.getOrgShortName() : org.getDepartmentName();
        ExecuteUnitTreeDto unitDto = new ExecuteUnitTreeDto();
        String queryCode = org.getQueryCode();
        int level = CharSequenceUtil.count(queryCode, "#");

        unitDto.setId(org.getId())
                .setName(fullName)
                .setDeptId(org.getDepartmentId())
                .setAbbreviation(abbreviation)
                .setIdPath(queryCode)
                .setLevel(level)
                .setSyncMark(syncMark)
                .setCode(org.getDepartmentCode())
                .setOrgType(org.getOrgCategory())
                .setParentId(org.getTreeParentId())
                .setDeptSort(org.getDeptSort())
                .setTreeName(org.getSourceDepartmentName());

        ExecuteUnitTreeDto parentTreeDto = tempMap.get(org.getTreeParentId());
        if (parentTreeDto != null) {
            unitDto.setIdPathName(parentTreeDto.getIdPathName() + Constants.ID_PATH_CONNECTOR + unitDto.getName())
                    .setIdPathAbbreviation(parentTreeDto.getIdPathAbbreviation() + Constants.ID_PATH_CONNECTOR + unitDto.getAbbreviation());
        } else {
            unitDto.setIdPathName(unitDto.getName())
                    .setIdPathAbbreviation(unitDto.getAbbreviation());
        }

        return unitDto;
    }

    private void updateCache(ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations) {
        if (!treeCacheMap.isEmpty()) {
            treeBoundHashOperations.putAll(treeCacheMap);
            deptBoundHashOperations.putAll(deptCacheMap);
            pushlisher.publishEvent(new SyncUpdateOrgEvent(this, new ArrayList<>(treeCacheMap.values())));
        }
    }

    private void removeUnsynchronizedData(String parentId, String syncMark) {
        if (yunshuOrgSyncService.getCountBySyncMark(syncMark) > 0) {
            ExecuteUnitTreeDto unitTreeDto =
                    (ExecuteUnitTreeDto) redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO).get(parentId);
            String deleteIdPath = ObjectUtils.isEmpty(unitTreeDto) ? null : unitTreeDto.getIdPath();
            yunshuOrgSyncService.deleteUnSynchronizedData(deleteIdPath, syncMark);
        }
    }

    private void setCacheExpiration() {
        redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO).expire(23, TimeUnit.HOURS);
        redisTemplate.boundHashOps(YUNSHU_DEPT_INFO).expire(23, TimeUnit.HOURS);
    }

    private void batchInsertOrUpdate(List<ExecuteUnitTreeDto> dtos) {
        for (int i = 0; i < dtos.size(); i += BATCH_SIZE) {
            List<ExecuteUnitTreeDto> batch = dtos.subList(i, Math.min(i + BATCH_SIZE, dtos.size()));
            yunshuOrgSyncService.batchInsertOrUpdate(batch);
        }
    }


    @Override
    public Page<UserAndOrgDto> getUserListByDepartment(QueryUsersReq req) {
        QueryUsersResponse response = g3OrgService.executeForQueryUsersByDepartmentId(req.getDepartmentId(),
                req.getKeywords(), req.getCurrent(), req.getSize());
        if (!response.isSuccess()) {
            throw new FrameworkException(-1, response.getErrmsg());
        }
        QueryUsersDTO data = response.getData();
        Page<UserAndOrgDto> page = new Page<>(data.getTotalElements(), req.getCurrent(), req.getSize());
        final List<UserAndOrgDTO> content = data.getContent();
        final List<UserAndOrgDto> userList = content.stream().map(u -> {
            final UserAndOrgDto userDto = BeanMapUtils.map(u, UserAndOrgDto.class);
            userDto.setUserAccount(u.getUsername()).setUserName(u.getName());
            return userDto;
        }).collect(Collectors.toList());
        page.setRecords(userList);
        return page;
    }

    @Override
    public List<OrgLabelResp> getLabels() {
        return ucOpenApiProperties.getCpmLabel();
    }
}
