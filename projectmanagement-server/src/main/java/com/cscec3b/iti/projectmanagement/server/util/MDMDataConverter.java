package com.cscec3b.iti.projectmanagement.server.util;

import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.feign.entity.MdmPushEntity;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * MDM数据转换工具类
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
public class MDMDataConverter {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    private MDMDataConverter() {
        // 工具类私有构造函数
    }

    /**
     * 将Project实体转换为MdmPushEntity
     *
     * @param project 项目实体
     * @return MDM推送实体
     */
    public static MdmPushEntity convertFromProject(Project project) {
        if (project == null) {
            return null;
        }

        MdmPushEntity entity = new MdmPushEntity();

        // 基本信息
        entity.setCode(project.getCpmProjectKey());
        entity.setName(project.getCpmProjectName());
        entity.setStname(project.getCpmProjectAbbreviation());
        entity.setProjType("01");

        // 地理信息
        entity.setContinent("CN");
        entity.setOffRegion(project.getRegionCode());
        entity.setIsInternal("1");

        // 项目属性
        entity.setOwnerType(project.getProjectContractorType());
        entity.setType(project.getProjectCategory());
        entity.setAInsParentCode(project.getProjectDeptId());
        entity.setIsAssType(project.getFabricated());
        entity.setConType(project.getContractMode());

        // 日期信息
        entity.setPlanStartDate(formatTimestamp(project.getWorkerBeginTime()));
        entity.setPlanEndDate(formatTimestamp(project.getWorkerEndTime()));
        entity.setContStartDate(formatTimestamp(project.getContractStartDate()));
        entity.setActualConstrCompltDate(formatTimestamp(project.getContractEndDate()));

        // 项目状态和其他信息
        entity.setBuiProStatus(convertProjectStatus(project.getProjectStatus()));
        entity.setCustomCode(project.getCustomCode());
        entity.setProjectAddress(project.getSmartProjectAddress());

        // 经纬度
        entity.setLongitude(parseBigDecimal(project.getLng()));
        entity.setLatitude(parseBigDecimal(project.getLat()));

        entity.setProjectDepartment(project.getProjectDeptId());
        entity.setCompanyLvl2(extractCompanyLvl2(project.getProjectDeptIdPath()));

        // 项目规模和面积
        entity.setProjScale(project.getProjectScale());
        entity.setProjBuildArea(project.getBuildingArea());

        // 工程信息
        entity.setEngineImportanceCate(project.getProjectImportanceClass());
        entity.setSignPrime(project.getSignedSubjectValue());
        entity.setBuildUnit(project.getBuildUnit());
        entity.setDesignComp(project.getDesignUnit());
        entity.setRecceComp(project.getSurveyUnit());
        entity.setSupevsComp(project.getSupervisionUnit());

        // 特殊字段
        entity.setFSftybd(project.getSameSectionAsGc());
        entity.setIsBeltRoad("2");
        entity.setIsFreeze("2");

        // 系统字段
        String currentTime = TIMESTAMP_FORMAT.format(new Date());
        entity.setCreateUser(project.getCreateBy());
        entity.setCreateTime(currentTime);
        entity.setUpdateUser(project.getUpdateBy());
        entity.setUpdateTime(currentTime);
        entity.setSendTime(currentTime);
        entity.setSubmittedByOrgId(project.getProjectDeptId());
        entity.setVersion("1.0");
        entity.setOperType("add");

        return entity;
    }

    /**
     * 将BidApproval实体转换为MdmPushEntity
     *
     * @param bidApproval 中标未立项实体
     * @return MDM推送实体
     */
    public static MdmPushEntity convertFromBidApproval(BidApproval bidApproval) {
        if (bidApproval == null) {
            return null;
        }

        MdmPushEntity entity = new MdmPushEntity();

        // 基本信息
        entity.setCode(bidApproval.getProjectCode());
        entity.setName(bidApproval.getProjectName());
        entity.setProjType("01");

        // 地理信息
        entity.setContinent("CN");
        entity.setIsInternal("Y".equals(bidApproval.getProjectBelong()) ? "1" : "2");

        // 项目属性
        entity.setOwnerType(bidApproval.getProjectContractorType());
        entity.setType(bidApproval.getProjectCategory());
        entity.setIsAssType(bidApproval.getFabricated());
        entity.setConType(bidApproval.getContractMode());

        // 日期信息
        entity.setPlanStartDate(formatTimestamp(bidApproval.getWorkerBeginTime()));
        entity.setPlanEndDate(formatTimestamp(bidApproval.getWorkerEndTime()));

        // 项目信息
        entity.setCustomCode(bidApproval.getCustomCode());
        entity.setProjectAddress(bidApproval.getAddress());

        // 工程信息
        entity.setEngineImportanceCate(bidApproval.getProjectImportanceClass());
        entity.setSignPrime(bidApproval.getSignedSubjectValue());
        entity.setBuildUnit(bidApproval.getBuildUnit());
        entity.setDesignComp(bidApproval.getDesignUnit());
        entity.setRecceComp(bidApproval.getSurveyUnit());
        entity.setSupevsComp(bidApproval.getSupervisionUnit());

        // 特殊字段
        entity.setFSftybd(bidApproval.getSameSectionAsGc());
        entity.setIsBeltRoad("2");
        entity.setIsFreeze("2");

        // 系统字段
        String currentTime = TIMESTAMP_FORMAT.format(new Date());
        entity.setCreateUser(bidApproval.getCreateBy());
        entity.setCreateTime(currentTime);
        entity.setUpdateUser(bidApproval.getUpdateBy());
        entity.setUpdateTime(currentTime);
        entity.setSendTime(currentTime);
        entity.setVersion("1.0");
        entity.setOperType("add");

        return entity;
    }

    /**
     * 格式化时间戳为日期字符串
     */
    private static String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return DATE_FORMAT.format(new Date(timestamp));
    }

    /**
     * 转换项目状态
     */
    private static String convertProjectStatus(Integer projectStatus) {
        if (projectStatus == null) {
            return "1.1";
        }
        switch (projectStatus) {
            case 0:
                return "1.1"; // 立项中
            case 1:
                return "1.2"; // 完成立项
            default:
                return "1.1";
        }
    }

    /**
     * 解析BigDecimal
     */
    private static BigDecimal parseBigDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 提取二级单位
     */
    private static String extractCompanyLvl2(String deptIdPath) {
        if (StringUtils.isBlank(deptIdPath)) {
            return null;
        }
        String[] parts = deptIdPath.split("/");
        if (parts.length >= 3) {
            return parts[2];
        }
        return null;
    }
}
