package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.projectmanagement.api.IPageLockApi;
import com.cscec3b.iti.projectmanagement.api.dto.response.pagelockinfo.PageLockResp;
import com.cscec3b.iti.projectmanagement.server.service.IPageLockService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(IPageLockApi.PATH)
@Api(tags = "页面锁定控制器")
public class PageLockController implements IPageLockApi {

    private final IPageLockService pageLockService;

    @Override
    public GenericityResponse<PageLockResp> check(String pageId) {
        return ResponseBuilder.fromData(pageLockService.checkAndTryLock(pageId));
    }

    @Override
    public GenericityResponse<Boolean> release(String pageId) {
        return ResponseBuilder.fromData(pageLockService.release(pageId));
    }
}
