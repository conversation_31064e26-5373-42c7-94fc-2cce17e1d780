package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.server.config.YunShuConfig;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.service.IOrgCacheService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.g3.G3OrgService;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrganizationResp;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.g3.org.api.dto.resp.org.YunshuOrgTreeEntity;
import com.google.common.base.Joiner;
import com.google.common.util.concurrent.RateLimiter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 组织相关服务
 *
 * <AUTHOR>
 * @date 2023/08/12 23:10
 **/

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OrgCacheServiceImpl implements IOrgCacheService {

    private final G3OrgService g3OrgService;

    private final YunShuConfig yunShuConfig;


    private final IYunshuOrgSyncService yunshuOrgSyncService;

    private final RedisTemplate<String, Object> redisTemplate;

    private RateLimiter apiRateLimiter;

    /**
     * 从云枢获取根节点树id
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/08/22
     */
    @PmReTry
    private String getRootTreeIdFromYunshu() {
        String orgId;
        final ExecuteGetOrganizationResp organizationResponse = g3OrgService.executeGetOrganization();
        checkResponseEntity(organizationResponse);
        // 查询详情
        final YunshuOrgTreeEntity entity = organizationResponse.getData();
        if (ObjectUtil.isNull(entity)) {
            throw new BusinessException(-1);
        }
        orgId = entity.getId();
        return orgId;
    }

    /**
     * 从云枢获取部门信息
     *
     * @param departmentId 部门id
     * @return {@link YunshuOrgDepartmentEntity }
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public YunshuOrgDepartmentEntity getDepartmentFromYunshu(String departmentId) {
        if (StringUtils.isBlank(departmentId)) {
            return null;
        }
        final ExecuteGetDepartmentResp orgDepartmentResponse = g3OrgService.executeGetDepartment(departmentId);
        checkResponseEntity(orgDepartmentResponse);
        return orgDepartmentResponse.getData();
    }

    /**
     * 从云枢获取组织树
     *
     * @param orgId       org id
     * @param isEntityOrg 是实体机构
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public List<YunshuOrgDepartmentTreeModel> getOrgTreeFromYunshu(String orgId, boolean isEntityOrg) {
        log.debug("获取云枢组织信息，id:{}", orgId);
        // orgId 如果为空，则从根组织下取值
        if (StringUtils.isBlank(orgId)) {
            orgId = getRootTreeIdFromYunshu();
        }
        // orgId不为空则取下级部门
        final ExecuteGetChildDepartmentsResp childDepartmentResp = g3OrgService.executeGetChildDepartments(orgId);
        checkResponseEntity(childDepartmentResp);
        final List<YunshuOrgDepartmentTreeModel> departments = childDepartmentResp.getData();
        if (isEntityOrg) {
            return departments.stream().filter(o -> yunShuConfig.getEntityOrg().contains(o.getOrgCategory()))
                    .collect(Collectors.toList());
        }
        return departments;
    }

    /**
     * 检查响应
     *
     * @param response 响应
     * <AUTHOR>
     * @date 2023/10/30
     */
    private void checkResponseEntity(CloudPivotResponse<?> response) {
        if (ObjectUtils.allNotNull(response, response.getErrcode()) && !response.getErrcode().equals(0L)) {
            log.error("响应失败:{}", response);
            throw new FrameworkException(80107000, JsonUtils.toJsonStr(response));
        }
    }

    private String determineParentId(String parentTreeId) {
        return StringUtils.isBlank(parentTreeId) ? getRootTreeIdFromYunshu() : parentTreeId;
    }


    /**
     *
     */// -------------------- 并发控制与内存优化 --------------------
    private ExecutorService fetchExecutor;      // 数据获取线程池
    /**
     *
     */
    private ExecutorService processExecutor;    // 数据处理线程池

    // 对象池减少GC压力
    private GenericObjectPool<ExecuteUnitTreeDto> dtoPool;
    // 流水线任务队列
    // private final BlockingQueue<YunshuOrgDepartmentTreeModel> fetchQueue = new LinkedBlockingQueue<>(15_000);
    private final BlockingQueue<YunshuOrgDepartmentTreeModel> fetchQueue = new ArrayBlockingQueue<>(5_000);


    // @PostConstruct
    public void init() {
        // 初始化线程池（三阶段流水线）
        ThreadFactory fetchFactory = new ThreadFactoryBuilder().setNameFormat("fetch-pool-%d").build();
        ThreadFactory processFactory = new ThreadFactoryBuilder().setNameFormat("process-pool-%d").build();

        // 从API数据获取线程池
        this.fetchExecutor = Executors.newFixedThreadPool(
                yunShuConfig.getSyncThreadPoolSize(), fetchFactory
        );
        // 数据处理
        this.processExecutor = Executors.newFixedThreadPool(
                yunShuConfig.getProcessThreadPoolSize(), processFactory
        );


        // 初始化对象池
        this.dtoPool = new GenericObjectPool<>(new BasePooledObjectFactory<ExecuteUnitTreeDto>() {
            @Override
            public ExecuteUnitTreeDto create() {
                return new ExecuteUnitTreeDto();
            }

            @Override
            public PooledObject<ExecuteUnitTreeDto> wrap(ExecuteUnitTreeDto obj) {
                return new DefaultPooledObject<>(obj);
            }
        });
        dtoPool.setMaxTotal(8000);

        // this.apiRateLimiter = RateLimiter.create(yunShuConfig.getApiRateLimit());
    }

    // -------------------- 核心方法 --------------------
    @Lock(leaseTime = 1000 * 60 * 60)
    @Override
    public void cacheYunshuOrgTreeV3(String parentTreeId) {
        String rootId = determineParentId(parentTreeId);
        String syncMark = IdUtil.objectId();
        log.info("云枢同步启动 | 根节点ID: {}, 同步标记: {}", rootId, syncMark);
        // 初始化线程池（流水线）
        this.init();

        // 初始化Redis操作
        BoundHashOperations<String, String, ExecuteUnitTreeDto> orgCache =
                redisTemplate.boundHashOps(Constants.YUNSHU_ORG_INFO);
        BoundHashOperations<String, String, String> deptToTreeCache =
                redisTemplate.boundHashOps(Constants.YUNSHU_DEPT_TO_TREE);


        // 新增同步控制工具
        AtomicInteger fetchTaskCounter = new AtomicInteger(0);  // 阶段1任务计数器
        AtomicInteger processTaskCounter = new AtomicInteger(0);  // 阶段1任务计数器
        CountDownLatch saveLatch = new CountDownLatch(yunShuConfig.getProcessThreadPoolSize());

        // 阶段1：启动数据获取
        log.info("启动数据获取 | 根节点ID: {}", rootId);
        submitFetchTask(rootId, fetchTaskCounter);

        // 阶段2：启动数据处理
        log.info("启动数据处理及保存");
        startProcessPipeline(rootId, syncMark, orgCache, deptToTreeCache, fetchTaskCounter, processTaskCounter,
                saveLatch);
    }

    // -------------------- 阶段1：数据获取 --------------------
    private void submitFetchTask(String treeId, AtomicInteger taskCounter) {
        if (fetchExecutor.isShutdown() || fetchExecutor.isTerminated()) {
            log.warn("线程池已关闭，无法提交新任务 | treeId: {}", treeId);
            return;
        }
        fetchExecutor.submit(() -> {
            log.warn("提交新任务 | treeId: {}, 当前任务数: {}", treeId, taskCounter.get());
            try {
                // 获取API调用许可（阻塞直到获得许可）
                // apiRateLimiter.acquire();
                taskCounter.incrementAndGet();
                List<YunshuOrgDepartmentTreeModel> children = getOrgTreeFromYunshu(treeId, false);
                for (YunshuOrgDepartmentTreeModel child : children) {
                    if (StringUtils.isNotBlank(child.getId())) {
                        fetchQueue.put(child);  // 将子节点放入队列
                        submitFetchTask(child.getId(), taskCounter);  // 递归提交子任务
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("数据获取失败 | treeId: {}", treeId, e);
            } finally {
                taskCounter.decrementAndGet();
            }
        });
    }

    // -------------------- 阶段2：数据处理 --------------------
    private void startProcessPipeline(String parentId, String syncMark, BoundHashOperations<String, String,
                    ExecuteUnitTreeDto> orgCache, BoundHashOperations<String, String, String> deptToTreeCache,
            AtomicInteger taskCounter, AtomicInteger processTasks, CountDownLatch saveLatch) {
        for (int i = 0; i < yunShuConfig.getProcessThreadPoolSize(); i++) {
            processExecutor.submit(() -> {
                List<ExecuteUnitTreeDto> batch = new ArrayList<>(yunShuConfig.getSyncBatchSize());
                while (!(fetchQueue.isEmpty() && taskCounter.get() == 0)) {
                    try {
                        log.info("数据处理队列长度: {}", fetchQueue.size());
                        YunshuOrgDepartmentTreeModel org = fetchQueue.poll(10_000, TimeUnit.MILLISECONDS);
                        if (org != null) {
                            processTasks.incrementAndGet();
                            // 数据处理
                            ExecuteUnitTreeDto dto = processOrgNode(org, syncMark, orgCache, deptToTreeCache);
                            // 数据保存
                            batch.add(dto);
                            if (batch.size() >= yunShuConfig.getSyncBatchSize()) {
                                saveBatch(batch, syncMark);
                                batch.clear();
                            }
                            processTasks.decrementAndGet();
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("数据处理线程中断", e);
                        break;
                    } catch (Exception e) {
                        log.error("数据处理失败 ", e);
                    }
                }
                if (CollectionUtils.isNotEmpty(batch)) {
                    saveBatch(batch, syncMark); // 保存剩余数据
                    log.info("数据处理线程退出 | 保存剩余数据");
                }
                saveLatch.countDown(); // 标记当前线程任务完成
                log.info("数据处理完成");
            });
        }
        // 启动一个单独的线程来执行清理和关闭操作
        new Thread(() -> {
            try {
                saveLatch.await(); // 主线程挂起，直到所有处理线程完成
                log.info("所有处理线程完成，统一清理和关闭线程池");
                // 统一清理和关闭（由主线程执行）
                final ExecuteUnitTreeDto executeUnitTreeDto = orgCache.get(parentId);
                final String idPath =
                        Optional.ofNullable(executeUnitTreeDto).map(ExecuteUnitTreeDto::getIdPath).orElse(null);
                yunshuOrgSyncService.deleteUnSynchronizedData(idPath, syncMark);
                yunshuOrgSyncService.updateIsLeaf();
                log.info("统一清理和关闭线程池完成");
                shutdownPools();
                log.info("同步过程完成");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("资源清理被中断", e);
            }
        }).start();
    }


    private ExecuteUnitTreeDto processOrgNode(YunshuOrgDepartmentTreeModel org, String syncMark,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> orgCache,
            BoundHashOperations<String, String, String> deptToTreeCache) {
        try {
            // 从对象池获取DTO
            ExecuteUnitTreeDto dto = dtoPool.borrowObject();
            buildExecuteUnitTreeDto(dto, org, syncMark);

            // 缓存数据
            cacheYunshuDto(orgCache, deptToTreeCache, dto);

            return dto;
        } catch (Exception e) {
            log.error("数据处理失败 | orgId: {}", org.getId(), e);
            return null;
        }
    }


    private void buildExecuteUnitTreeDto(ExecuteUnitTreeDto dto, YunshuOrgDepartmentTreeModel org, String syncMark) {
        dto.setId(org.getId())
                .setDeptId(org.getDepartmentId())
                .setName(org.getDepartmentName())
                .setAbbreviation(StringUtils.isBlank(org.getAbbreviation()) ? org.getDepartmentName() :
                        org.getAbbreviation())
                .setSyncMark(syncMark)
                .setCode(org.getDepartmentCode())
                .setIdPath(org.getQueryCode())
                .setLevel(CharSequenceUtil.count(org.getQueryCode(), "#"))
                .setOrgType(org.getOrgCategory())
                .setParentId(org.getTreeParentId())
                .setDeptSort(org.getDeptSort())
                .setTreeName(org.getSourceDepartmentName())
                .setOrgMarkCode(org.getOrgMarkCode())
                .setOrgMarkName(org.getOrgMarkName());

        String idPathName = dto.getName();
        String idPathAbbreviation = dto.getAbbreviation();

        // 从缓存获取父路径
        if (StringUtils.isNotBlank(org.getTreeParentId())) {
            final ExecuteUnitTreeDto unitTreeDto = this.getUnitTreeDtoById(org.getTreeParentId());
            if (Objects.nonNull(unitTreeDto) && ObjectUtil.isAllNotEmpty(unitTreeDto.getIdPathName(),
                    unitTreeDto.getIdPathAbbreviation())) {
                idPathName = Joiner.on("/").skipNulls().join(unitTreeDto.getIdPathName(), dto.getName());
                idPathAbbreviation = Joiner.on("/").skipNulls().join(unitTreeDto.getIdPathAbbreviation(),
                        dto.getAbbreviation());
            }
        }

        dto.setIdPathName(idPathName).setIdPathAbbreviation(idPathAbbreviation);

    }


    private synchronized void saveBatch(List<ExecuteUnitTreeDto> batch, String syncMark) {
        try {
            // 1. 批量写入数据库
            yunshuOrgSyncService.batchInsertOrUpdate(batch);

            // 2. 清理对象池
            batch.forEach(dto -> dtoPool.returnObject(dto));
        } catch (Exception e) {
            log.error("批量保存失败 | 同步标记: {}", syncMark, e);
            batch.forEach(dto -> {
                try {
                    dtoPool.invalidateObject(dto);
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            });  // 废弃异常对象
        }
    }

    private void shutdownPools() {
        try {
            fetchExecutor.shutdown();
            processExecutor.shutdown();
            if (!fetchExecutor.awaitTermination(30, TimeUnit.SECONDS)) fetchExecutor.shutdownNow();
            if (!processExecutor.awaitTermination(30, TimeUnit.SECONDS)) processExecutor.shutdownNow();
        } catch (InterruptedException e) {
            log.error("线程池关闭异常", e);
        }
    }

    private void cacheYunshuDto(BoundHashOperations<String, String, ExecuteUnitTreeDto> orgCache,
            BoundHashOperations<String, String, String> deptToTreeCache, ExecuteUnitTreeDto dto) {
        orgCache.put(dto.getId(), dto);
        deptToTreeCache.put(dto.getDeptId(), dto.getId());
    }

    private ExecuteUnitTreeDto getUnitTreeDtoById(String id) {
        return (ExecuteUnitTreeDto) redisTemplate.opsForHash().get(Constants.YUNSHU_ORG_INFO, id);
    }

    private ExecuteUnitTreeDto getUnitTreeIdByDeptId(String deptId) {
        return Optional.ofNullable(deptId).map(orgId -> redisTemplate.opsForHash().get(Constants.YUNSHU_ORG_INFO,
                orgId)).map(String.class::cast).map(this::getUnitTreeDtoById).orElse(null);
    }


}
