package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @Date 2022/10/21 14:39
 */
@Mapper
public interface BureauSupplementaryAgreementMapper extends BaseMapper<BureauSupplementaryAgreement> {
    /**
     * 保存
     *
     * @param bureauSupplementaryAgreement bureauSupplementaryAgreement
     * @return int
     */
    int saveBureauSupplementaryAgreement(@Param("vo") BureauSupplementaryAgreement bureauSupplementaryAgreement);
    
    /**
     * 通过belongId 查询总数
     *
     * @param belongId beelongId
     * @return int 总数
     */
    int selectCountByBelongId(Long belongId);
    
    /**
     * 通过Id 查询详情
     * @param id  id
     * @return BureauContract
     */
    BureauSupplementaryAgreement getById(Long id);
    
    /**
     * 删除局补充协议
     * @param belongIds belongIds
     * @return int
     */
    int deleteBureauSupplementaryAgreementByBelongIds(@Param("list") List<Long> belongIds);

    /**
     * 分页查询文件列表
     *
     * @param pageReq       分页查询参数
     * @param scopeTypeEnum 合同文件类型
     * @return {@link List}<{@link ContractFilePageResp}>
     */
    List<ContractFilePageResp> pageList(@Param("req") ContractFilePageReq pageReq,
            @Param("scopeTypeEnum") IndContractsTypeEnum scopeTypeEnum);

}
