package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.OSSProcessEvent;
import com.cscec3b.iti.projectmanagement.api.dto.response.OSSProcessResp;
import com.cscec3b.iti.projectmanagement.server.constant.OSSConstants;
import com.cscec3b.iti.projectmanagement.server.service.IOSSProcessService;
import com.cscec3b.iti.projectmanagement.server.service.ISseEmitterService;
import com.odin.freyr.common.orika.BeanMapUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/1/10 16:49
 */
@Service
@Slf4j
public class OSSProcessServiceImpl implements IOSSProcessService {

    private final StringRedisTemplate stringRedisTemplate;

    private final ISseEmitterService sseEmitterService;

    private final Executor cpmSingleTaskExecutor;

    @Resource
    private Executor cpmTaskExecutor;

    public OSSProcessServiceImpl(StringRedisTemplate stringRedisTemplate, ISseEmitterService sseEmitterService, Executor cpmSingleTaskExecutor) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.sseEmitterService = sseEmitterService;
        this.cpmSingleTaskExecutor = cpmSingleTaskExecutor;
    }

    @Override
    public void cacheProcess(OSSProcessEvent event) {
        log.info("cache oss Process -> {}", event.toString());
        stringRedisTemplate.opsForValue().set(OSSConstants.OSS_PROCESS_KEY + event.getFileId(),
                JsonUtils.toJsonStr(event), 8 * 60 * 60, TimeUnit.SECONDS);
    }

    @Override
    public OSSProcessResp getProcess(String key) {
        String processStr = stringRedisTemplate.opsForValue().get(OSSConstants.OSS_PROCESS_KEY + key);
        if (StringUtils.isBlank(processStr)) {
            // 任务不存在或已完成
            throw new BusinessException(8010304);
        }
        return JsonUtils.readValue(processStr, OSSProcessResp.class);
    }

    @Async("cpmTaskExecutor")
    @EventListener
    @Override
    public void parseProcessListener(OSSProcessEvent event) {
        log.info("接收到文件上进度通知:{} -> {}", event.getFileId(), event.getPercent());
        // 缓存文件上传下载进度
        this.cacheProcess(event);
        // 通知前端进度
        sseEmitterService.sendMessage(event.getFileId(), BeanMapUtils.map(event, OSSProcessResp.class));
        // 关闭通道
        if (event.getStatus() == 1 && event.isSucceed()) {
            //  关闭连接
            sseEmitterService.close(event.getFileId());
        }
    }
}
