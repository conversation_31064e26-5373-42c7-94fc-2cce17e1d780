package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryUsersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.org.UserAndOrgDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.OrgLabelResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.UserOrgListResp;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;

/**
 * 云枢组织相关服务层
 *
 * <AUTHOR>
 * @date 2023/08/12 23:09
 **/

public interface IOrgService {

	/**
	 * 获取组织树
	 * parentId为空时，则取用户当前所在组织为根节点，向下获取所有树节点
	 *
	 * @param parentId   根节点id
	 * @param isEntities 是否实体
	 * @return {@link List }<{@link ExecuteUnitTreeResp }>
	 * <AUTHOR>
	 * @date 2023/08/24
	 */
	List<ExecuteUnitTreeResp> getOrgTree(String parentId, boolean isEntities);

	/**
	 * 获取组织树
	 * parentId为空时，则取精益建造根节点
	 *
	 * @param parentId   根节点id
	 * @param isEntities 是否实体
	 * @return {@link List }<{@link ExecuteUnitTreeResp }>
	 * <AUTHOR>
	 * @date 2023/08/24
	 */
	List<ExecuteUnitTreeResp> getOrgTreeForRevision(String parentId, boolean isEntities);

	/**
	 * 分域查询组织信息
	 *
	 * @param parentTreeId 父节点id
	 * @param name         全称
	 * @param abbreviation 简称
	 * @param isEntities   是否实体
	 * @return {@link List }<{@link ExecuteUnitTreeResp }>
	 * <AUTHOR>
	 * @date 2023/08/24
	 */
	List<ExecuteUnitTreeResp> fuzzySearchOrg(String parentTreeId, String name, String abbreviation, boolean isEntities);

	/**
	 * 获取云枢组织树
	 *
	 * @param orgId       orgId
	 * @param isEntityOrg 是否仅返回实体机构
	 * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
	 * <AUTHOR>
	 * @date 2023/10/30
	 */
	List<YunshuOrgDepartmentTreeModel> getOrgTreeFromYunshu(String orgId, boolean isEntityOrg);

	/**
	 * 查询部门详情
	 *
	 * @param departmentId deptId
	 * @return {@link YunshuOrgDepartmentEntity }
	 * <AUTHOR>
	 * @date 2023/10/30
	 */
	YunshuOrgDepartmentEntity getDepartmentFromYunshu(String departmentId);


    void syncYunshuOrg(String parentTreeId);

    /**
	 * 缓存云枢组织树
	 *
	 * @param parentTreeId 父树id
	 * <AUTHOR>
	 * @date 2023/08/21
	 */
	void cacheYunshuOrgTree(String parentTreeId);


	/**
	 * @param orgId orgId
	 * @param orgFullName orgId
	 * @param orgAbbrName orgId
	 * <AUTHOR>
	 * @Date 2023/8/22
	 */
	void getYunshuOrgNamePath(String orgId,List<String> orgFullName,List<String> orgAbbrName);

	/**
	 * 通过deptId获取实体组织
	 *
	 * @param deptId
	 * @return {@link ExecuteUnitTreeDto }
	 * <AUTHOR>
	 * @date 2023/10/29
	 */
	ExecuteUnitTreeDto getEntityOrgByDeptId(String deptId);

	/**
	 * 通过treeId获取实体组织
	 *
	 * @param treeId
	 * @return {@link ExecuteUnitTreeDto }
	 * <AUTHOR>
	 * @date 2023/10/29
	 */
	ExecuteUnitTreeDto getEntityOrgByTreeId(String treeId);


	/**
	 * 通过中标未立项的组织id查询智慧工地的组织树Id
	 *
	 * @param parentOrgId 上级组织id
	 * @return {@link List}<{@link ExecuteUnitTreeResp}>
	 */
	String getSmartOrgTreeId(String parentOrgId);


	/**
	 * 实时从云枢获取组织树
	 *
	 * @param parentId   上级组织id
	 * @param isEntities 是否实体组织
	 * @return {@link List}<{@link ExecuteUnitTreeResp}>
	 */
	List<ExecuteUnitTreeResp> getOrgTreeRevisionForRealTime(String parentId, boolean isEntities);

	// @Override
	@Lock(lockKey = "cacheYunshuOrgTree", leaseTime = 30 * 60 * 1000L)
	void cacheYunshuOrgTreeV1(String parentTreeId);

	/**
	 * 获取当前用户的实体组织列表
	 *
	 * @return {@link UserOrgListResp }
	 */
	List<UserOrgListResp> getCurrentEntityOrgList();

    Page<UserAndOrgDto> getUserListByDepartment(QueryUsersReq req);

    /**
     * 获取组织标签
     * 
     * @return {@link List }<{@link OrgLabelResp }>
     */
    List<OrgLabelResp> getLabels();
}
