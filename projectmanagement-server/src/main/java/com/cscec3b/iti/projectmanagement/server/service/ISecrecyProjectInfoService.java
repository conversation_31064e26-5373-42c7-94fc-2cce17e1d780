package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.SecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.QuerySecrecyProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.UpdateSecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectResp;
import com.cscec3b.iti.projectmanagement.server.entity.SecrecyProjectInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 保密项目信息
 * <AUTHOR>
 * @date 2024/04/11
 */
public interface ISecrecyProjectInfoService extends IService<SecrecyProjectInfo>{


    /**
     * 新建
     *
     * @param createSpecialProjectReq 创建特殊项目请求
     * @return {@link Long}
     */
    Long create(SecrecyProjectReq createSpecialProjectReq);

    /**
     * 更新
     *
     * @param updateSecrecyProjectReq 更新保密项目要求
     * @return Boolean boolean
     * @description 编辑更新保密项目
     * @date 2023/02/14 15:29
     * <AUTHOR>
     */
    Boolean updateSecrecyProject(UpdateSecrecyProjectReq updateSecrecyProjectReq);

    /**
     * 获取详细信息
     *
     * @param id 项目id
     * @return {@link SecrecyProjectDetailResp}
     * @description 查询保密项目详情
     * @date 2023/02/14 15:36
     * <AUTHOR>
     */
    SecrecyProjectDetailResp getDetail(Long id);

    /**
     * 保密项目列表
     *
     * @param params 查询参数
     * @return Page<SpecialProjectResp>
     */
    Page<SecrecyProjectResp> pageList(QuerySecrecyProjectParams params);

    boolean deleteSecrecyProject(List<Long> ids);

}
