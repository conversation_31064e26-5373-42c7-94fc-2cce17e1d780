package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.response.changehistory.ProjectChangeHistoryResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectChangeHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  项目变更记录
 *  <AUTHOR>
 *  @date 2024/06/21
 */
@Mapper
public interface ProjectChangeHistoryMapper extends BaseMapper<ProjectChangeHistory> {
    /**
     * 查询项目变更记录
     *
     * @param id 项目id
     * @return {@link List}<{@link ProjectChangeHistoryResp}>
     */
    ProjectChangeHistoryResp queryProjectChangeHistory(@Param("id") Long id,
            @Param("businessType") Integer businessType);

    /**
     * 查询项目变更记录列表
     *
     * @param projectId   项目id
     * @param businessType 变更类型
     * @return {@link List}<{@link ProjectChangeHistoryResp}>
     */
    List<ProjectChangeHistoryResp> queryChangeHistoryListByProjectId(@Param("projectId") Long projectId,
            @Param("businessType") Integer businessType);
}