package com.cscec3b.iti.projectmanagement.server.enums;

import com.cscec3b.iti.common.base.dictionary.IDataDictionary;

/**
 * <AUTHOR>
 * @description MonitorProjectTypeEnum 项目监控类型
 * @date 2023/05/23 18:54
 */
public enum MonitorProjectTypeEnum implements IDataDictionary {
    /**
     * 项目监控类型：全部、重大基础设施类项目立项、非重大基础设施类项目立项、特殊立项
     */
    ALL(0,"全部","all"),

    /**
     * 重大基础设施类项目立项
     */
    MAJOR_INFRASTRUCTURE(1, "重大基础设施类项目立项", "major_infrastructure"),

    /**
     * 非重大基础设施类项目立项
     */
    NON_MAJOR_INFRASTRUCTURE(2, "非重大基础设施类项目立项", "non_major_infrastructure"),

    /**
     * 特殊立项
     */
    SPECIAL_PROJECT(3, "特殊立项", "special_project"),

    /**
     * 其他
     */
    OTHER(99, "其它", "other");

    MonitorProjectTypeEnum(Integer dictCode, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    final Integer dictCode;

    final String zhCN;

    final String enUS;

    @Override
    public Integer getDictCode() {
        return dictCode;
    }

    @Override
    public String getZhCN() {
        return zhCN;
    }

    @Override
    public String getEnUS() {
        return enUS;
    }

    /**
     * 增加说明
     */
    @Override
    public String getDesc() {
        return "项目分类枚举类";
    }
}
