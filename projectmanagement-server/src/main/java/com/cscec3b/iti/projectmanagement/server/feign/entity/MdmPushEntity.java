package com.cscec3b.iti.projectmanagement.server.feign.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * MDM工程项目推送实体
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "MDM工程项目推送实体")
public class MdmPushEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工程项目编码
     */
    @ApiModelProperty(value = "工程项目编码")
    private String code;

    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称")
    private String name;

    /**
     * 项目简称（中文）
     */
    @ApiModelProperty(value = "项目简称（中文）")
    private String stname;

    /**
     * 工程项目类型
     */
    @ApiModelProperty(value = "工程项目类型")
    private String projType = "01";

    /**
     * 洲别
     */
    @ApiModelProperty(value = "洲别")
    private String continent;

    /**
     * 行政区域
     */
    @ApiModelProperty(value = "行政区域")
    private String offRegion;

    /**
     * 境内/境外
     */
    @ApiModelProperty(value = "境内/境外")
    private String isInternal;

    /**
     * 是否列入"一带一路"项目库
     */
    @ApiModelProperty(value = "是否列入一带一路项目库")
    private String isBeltRoad;

    /**
     * 是否封存
     */
    @ApiModelProperty(value = "是否封存")
    private String isFreeze;

    /**
     * 项目承接主体类型
     */
    @ApiModelProperty(value = "项目承接主体类型")
    private String ownerType;

    /**
     * 项目分类
     */
    @ApiModelProperty(value = "项目分类")
    private String type;

    /**
     * 所属行政架构主体
     */
    @ApiModelProperty(value = "所属行政架构主体")
    private String aInsParentCode;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式")
    private String isAssType;

    /**
     * 承建模式
     */
    @ApiModelProperty(value = "承建模式")
    private String conType;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期")
    private String staDate;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期")
    private String endDate;

    /**
     * 计划竣工日期
     */
    @ApiModelProperty(value = "计划竣工日期")
    private String planEndDate;

    /**
     * 计划开工日期
     */
    @ApiModelProperty(value = "计划开工日期")
    private String planStartDate;

    /**
     * 工程项目状态
     */
    @ApiModelProperty(value = "工程项目状态")
    private String buiProStatus;

    /**
     * 业主单位
     */
    @ApiModelProperty(value = "业主单位")
    private String customCode;

    /**
     * 项目经理（执行）
     */
    @ApiModelProperty(value = "项目经理（执行）")
    private String projectManagerNo;

    /**
     * 项目经理联系(执行)电话
     */
    @ApiModelProperty(value = "项目经理联系(执行)电话")
    private String projectManagerTel;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    /**
     * 项目经度
     */
    @ApiModelProperty(value = "项目经度")
    private BigDecimal longitude;

    /**
     * 项目纬度
     */
    @ApiModelProperty(value = "项目纬度")
    private BigDecimal latitude;

    /**
     * 所属项目部
     */
    @ApiModelProperty(value = "所属项目部")
    private String projectDepartment;

    /**
     * 二级单位
     */
    @ApiModelProperty(value = "二级单位")
    private String companyLvl2;

    /**
     * 项目建筑面积
     */
    @ApiModelProperty(value = "项目建筑面积")
    private BigDecimal projBuildArea;
    /**
     * 工程类别
     */
    @ApiModelProperty(value = "工程类别")
    private String engineCate;

    /**
     * 建设单位
     */
    @ApiModelProperty(value = "建设单位")
    private String buildUnit;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位")
    private String designComp;

    /**
     * 勘察单位
     */
    @ApiModelProperty(value = "勘察单位")
    private String recceComp;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位")
    private String supevsComp;

    /**
     * 项目里程
     */
    @ApiModelProperty(value = "项目里程")
    private BigDecimal projMilestone;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String projManage;

    /**
     * 项目规模
     */
    @ApiModelProperty(value = "项目规模")
    private String projScale;

    /**
     * 工程重要性类别
     */
    @ApiModelProperty(value = "工程重要性类别")
    private String engineImportanceCate;

    /**
     * 签约主体名称
     */
    @ApiModelProperty(value = "签约主体名称")
    private String signPrime;

    /**
     * 内部联合体牵头项目名称
     */
    @JsonProperty("F_LHTQTXM")
    @ApiModelProperty(value = "内部联合体牵头项目名称")
    private String fLhtqtxm;

    /**
     * 内部拆分牵头项目名称
     */
    @JsonProperty("F_NBCFQTXM")
    @ApiModelProperty(value = "内部拆分牵头项目名称")
    private String fNbcfqtxm;

    /**
     * 是否为内部联合体牵头项目
     */
    @JsonProperty("F_SFLHTQTXM")
    @ApiModelProperty(value = "是否为内部联合体牵头项目")
    private String fSflhtqtxm;

    /**
     * 是否为联合体项目（牵头方为中建内部）
     */
    @JsonProperty("F_SFLHTXM")
    @ApiModelProperty(value = "是否为联合体项目（牵头方为中建内部）")
    private String fSflhtxm;

    /**
     * 是否为内部拆分的上级项目
     */
    @JsonProperty("F_SFNBCFQTXM")
    @ApiModelProperty(value = "是否为内部拆分的上级项目")
    private String fSfnbcfqtxm;

    /**
     * 是否为内部拆分项目
     */
    @JsonProperty("F_SFNBCFXM")
    @ApiModelProperty(value = "是否为内部拆分项目")
    private String fSfnbcfxm;

    /**
     * 总承包单位是否为内部
     */
    @JsonProperty("F_SFNBZCBDW")
    @ApiModelProperty(value = "总承包单位是否为内部")
    private String fSfnbzcbdw;

    /**
     * 是否与总承包同一标段
     */
    @JsonProperty("F_SFTYBD")
    @ApiModelProperty(value = "是否与总承包同一标段")
    private String fSftybd;

    /**
     * 上级管理总承包项目名称
     */
    @JsonProperty("F_SJGLZCBXM")
    @ApiModelProperty(value = "上级管理总承包项目名称")
    private String fSjglzcbxm;

    /**
     * 提交单位编码
     */
    @JsonProperty("SUBMITTEDBYORGID")
    @ApiModelProperty(value = "提交单位编码")
    private String submittedByOrgId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 制单人编码
     */
    @ApiModelProperty(value = "制单人编码")
    private String createUser;

    /**
     * 制单时间
     */
    @ApiModelProperty(value = "制单时间")
    private String createTime;

    /**
     * 最后修改人编码
     */
    @ApiModelProperty(value = "最后修改人编码")
    private String updateUser;

    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间")
    private String updateTime;

    /**
     * 发送时间
     */
    @ApiModelProperty(value = "发送时间")
    private String sendTime;

    /**
     * 合同工期（天）
     */
    @ApiModelProperty(value = "合同工期（天）")
    private Integer contProjSpan;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期")
    private String contStartDate;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期")
    private String actualConstrCompltDate;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operType;

}
