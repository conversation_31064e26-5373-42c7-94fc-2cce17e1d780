package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.projectmanagement.api.dto.dto.FinancialBusinessSegmentDto;
import com.cscec3b.iti.projectmanagement.server.entity.DictMainData;
import com.cscec3b.iti.projectmanagement.server.mapper.DictMainDataMapper;
import com.cscec3b.iti.projectmanagement.server.service.DictMainDataService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DictMainDataServiceImpl extends ServiceImpl<DictMainDataMapper, DictMainData> implements DictMainDataService {

    @Override
    @Cacheable(value = "@30m," + "dictMainData:fullPathName", key = "#fullPathName")
    public DictMainData getByFullPathName(String fullPathName) {
        return this.getOne(Wrappers.<DictMainData>lambdaQuery().eq(DictMainData::getFullPathName, fullPathName));
    }

    @Override
    @Cacheable(value = "@30m," + "dictMainData:oldFullPath", key = "#oldFullPath")
    public DictMainData getByOldFullPath(String oldFullPath) {
        return this.getOne(Wrappers.<DictMainData>lambdaQuery().eq(DictMainData::getOldFullPathCode, oldFullPath));
    }

    @Override
    @Cacheable(value = "@30m," + "dictMainData:oldCode", key = "#oldCode")
    public DictMainData getByOldCode(String oldCode) {
        return this.getOne(Wrappers.<DictMainData>lambdaQuery().eq(DictMainData::getOldCode, oldCode));
    }

    @Override
    @Cacheable(value = "@300m," + "dictMainData:tree:list")
    public List<FinancialBusinessSegmentDto> getTreeList() {
        List<DictMainData> list = this.list(
                Wrappers.<DictMainData>lambdaQuery().orderByAsc(DictMainData::getMainDataId));
        // list 转 tree 取 oldParentCode == 0 为根节点
        return this.genTree(list, "0");
    }

    @Override
    @Cacheable(value = "@300m," + "DictMainData:list:all", key = "T(String).valueOf(#root.methodName)")
    public List<FinancialBusinessSegmentDto> getAllList() {
        List<DictMainData> list = this.list(
                Wrappers.<DictMainData>lambdaQuery().orderByAsc(DictMainData::getMainDataId));
        return list.stream().map(mainData -> new FinancialBusinessSegmentDto().setId(String.valueOf(mainData.getId()))
                .setNamePath(mainData.getFullPathName()).setParentCode(mainData.getOldParentCode())
            .setCodePath(mainData.getOldFullPathCode()).setName(mainData.getName()).setCode(mainData.getOldCode()))
            .collect(Collectors.toList());
    }

    /**
     * list 转 树
     *
     * @param list       要转树的list
     * @param parentCode 根节点值
     * @return List<OrganizationTree>
     */
    public List<FinancialBusinessSegmentDto> genTree(List<DictMainData> list, String parentCode) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 递归获取树形结构
        // 获取父节点，说明：父节点的parentOrgUuid 为参数 parentOrgUuid
        return list.stream().filter(m -> m.getOldParentCode() != null && m.getOldParentCode().equals(parentCode))
            .map(mainData -> new FinancialBusinessSegmentDto().setId(String.valueOf(mainData.getId()))
                .setNamePath(mainData.getFullPathName()).setParentCode(mainData.getOldParentCode())
                .setCodePath(mainData.getOldFullPathCode()).setName(mainData.getName()).setCode(mainData.getOldCode()))
            .peek(m -> m.setChildren(getChildren(m, list)))
            // .sorted(Comparator.comparingInt(OrganizationTree::getOrderNumber))
            .sorted(Comparator.comparing(FinancialBusinessSegmentDto::getCode)).collect(Collectors.toList());
    }

    /**
     * 组装 Tree 子节点
     *
     * @param parent   节点对象
     * @param dataList list列表
     * @return List<OrganizationTree>
     */
    private List<FinancialBusinessSegmentDto> getChildren(FinancialBusinessSegmentDto parent,
                                                          List<DictMainData> dataList) {
        // 子节点parentOrgUuid = 父节点uuid
        return dataList.stream().filter(m1 -> m1.getOldParentCode().equals(parent.getCode()))
                .map(mainData -> new FinancialBusinessSegmentDto().setId(String.valueOf(mainData.getId()))
                        .setNamePath(mainData.getFullPathName()).setParentCode(mainData.getOldParentCode())
                        .setName(mainData.getName()).setParentName(parent.getName()).setCode(mainData.getOldCode()))
                .peek(m1 -> m1.setChildren(getChildren(m1, dataList)))
                .sorted(Comparator.comparing(FinancialBusinessSegmentDto::getId))
                .collect(Collectors.toList());
    }
}
