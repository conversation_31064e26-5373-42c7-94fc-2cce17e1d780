package com.cscec3b.iti.projectmanagement.server.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IdTableMapper {


    /**
     * 获取自增id
     * @param prefix 前缀
     * @param curDate 当前日期
     * @return {@link Long}
     */
    Long getIncrementId(@Param("prefix") String prefix, @Param("curDate") String curDate);

    /**
     * 更新自增id
     * @param prefix 前缀
     * @param curDate 当前日期
     */
    int updateIncrementId(@Param("prefix") String prefix, @Param("curDate") String curDate);

    /**
     * 插入或更新自增id
     *
     * @param prefix 前缀
     * @return int
     */
    int insertOrUpdateIncrementId(@Param("prefix") String prefix);
}