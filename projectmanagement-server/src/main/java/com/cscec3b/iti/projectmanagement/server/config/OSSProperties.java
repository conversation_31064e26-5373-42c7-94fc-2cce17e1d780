package com.cscec3b.iti.projectmanagement.server.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * oss存储平台配置类
 *
 * <AUTHOR>
 * @date 2022-12-2022/12/30 9:23
 */

@Data
@NoArgsConstructor
@ToString
@RefreshScope
@Component
@ConfigurationProperties(prefix = "cscec.file-storage")
public class OSSProperties {

    /**
     * 存储平台,默认阿里云 oss
     */
    private String platform = "oss";

    /**
     * 缩略图后缀: .png; .jpg;
     */
    private String thumbnailSuffix = ".min.jpg";


    /**
     * 鉴权 accessKey
     */
    private String accessKeyId;

    /**
     * 鉴权 secretKey
     */
    private String secretKey;

    /**
     * 区域端点
     */
    private String endpoint;

    /**
     * 桶名称
     */
    private String bucketName;

    /**
     * 访问域名（建议使用自定义域名）
     */
    private String domain;

    /**
     * 基础路径（统一前缀）
     */
    private String basePath = "";

    /**
     * 可预览的后缀名，如果浏览器不支持则无法支持
     */
    private List<String> viewSuffix;

    /**
     * 是否启用定期同步任务
     */
    private boolean enableSync = false;


}
