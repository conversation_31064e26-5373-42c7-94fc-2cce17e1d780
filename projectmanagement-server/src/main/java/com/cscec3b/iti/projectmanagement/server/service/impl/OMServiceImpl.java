package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMCreateOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMInitProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectInitReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OMProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.OrgCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.ProjectEventFlowReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidApprovalReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBidSummaryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMBureauSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.file.OMSupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateYunshuOrgIdReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowEventLogResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidBureauContractService;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidBureauSupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidContractService;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidSummaryService;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidSupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.BidSummary;
import com.cscec3b.iti.projectmanagement.server.entity.BureauContract;
import com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.entity.Contract;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.SupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.cscec3b.iti.projectmanagement.server.enums.RevisionTypeEnum;
import com.cscec3b.iti.projectmanagement.server.feign.IUcAppOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.mapper.OMMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.ProjectEventListener;
import com.cscec3b.iti.projectmanagement.server.service.IExcelExportService;
import com.cscec3b.iti.projectmanagement.server.service.IOMService;
import com.cscec3b.iti.projectmanagement.server.service.IOrgCacheService;
import com.cscec3b.iti.projectmanagement.server.service.IOrgService;
import com.cscec3b.iti.projectmanagement.server.service.IProjectEventPushRecordService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.YunShuSmartConstructionOrgService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.g3.G3OrgService;
import com.g3.org.api.dto.AddOrgReq;
import com.g3.org.api.dto.resp.ExecuteForAddDepartment;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.AddDepartmentResp;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.github.pagehelper.page.PageMethod;
import com.odin.freyr.common.orika.BeanMapUtils;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 运维服务
 *
 * <AUTHOR>
 * @date 2023/07/25 15:40
 **/

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class OMServiceImpl implements IOMService, IExcelExportService {

    private final OMMapper ommapper;


    private final ProjectService projectService;

    private final IOrgService orgService;

    private final IOrgCacheService orgCacheService;

    private final ProjectEventListener projectEventListener;

    private final ProjectMapper projectMapper;

    /**
     * 项目流程事件推送记录
     */
    private final IProjectEventPushRecordService projectEventPushRecordService;

    private final IBidContractService bidContractService;

    private final IBidSummaryService bidSummaryService;

    private final IBidBureauContractService bidBureauContractService;

    private final IBidBureauSupplementaryAgreementService bidBureauSupplementaryAgreementService;

    private final IBidSupplementaryAgreementService bidSupplementaryAgreementService;

    private final IBidApprovalService bidApprovalService;

    private final Executor cpmTaskExecutor;

    private final IUcAppOpenApiFeign ucAppOpenApiFeign;

    private final IYunshuOrgSyncService yunshuOrgSyncService;

    private final ScheduledExecutorService cpmScheduledExecutor;

    private final G3OrgService g3OrgService;

    private final YunShuSmartConstructionOrgService yunShuSmartConstructionOrgService;

    /**
     * 生成项目名称及简称, 转换业务板块信息
     *
     * @param project 项目信息
     */
    private static void genProjectOtherInfo(Project project) {
        final String cpmProjectName = StringUtils.isBlank(project.getProjectFinanceName()) ? project.getProjectName() :
                project.getProjectFinanceName();
        final String projectAbbreviation = StringUtils.isBlank(project.getProjectAbbreviation()) ?
                project.getProjectName() : project.getProjectAbbreviation();
        final String projectFinanceAbbreviation = StringUtils.isBlank(project.getProjectFinanceAbbreviation()) ?
                project.getProjectFinanceName() : project.getProjectFinanceAbbreviation();
        String cpmProjectAbbreviation = StringUtils.isBlank(project.getProjectFinanceAbbreviation()) ?
                projectAbbreviation : projectFinanceAbbreviation;
        // 设置项目中心名称 ，简称
        project.setProjectAbbreviation(projectAbbreviation).setProjectFinanceAbbreviation(projectFinanceAbbreviation)
                .setCpmProjectAbbreviation(cpmProjectAbbreviation).setCpmProjectName(cpmProjectName)
                .setFinanceBusinessSegment(project.getFinanceBusinessSegment())
                .setFinanceBusinessSegmentCodePath(project.getFinanceBusinessSegmentCodePath());
        // 业务板块
        project.setCpmBusinessSegment(project.getFinanceBusinessSegment()).setCpmBusinessSegmentCodePath(project.getFinanceBusinessSegmentCodePath());
    }

    private static LambdaQueryWrapper<Project> getProjectLambdaQueryWrapper(ProjectQueryParams queryParams) {
        final LambdaQueryWrapper<Project> queryWrapper = Wrappers.<Project>lambdaQuery()
            .likeRight(Project::getYunshuExecuteUnitIdPath, queryParams.getYunshuExecuteUnitIdPath())
            .like(StringUtils.isNoneBlank(queryParams.getProjectFinanceName()), Project::getProjectFinanceName,
                queryParams.getProjectFinanceName())
            .like(StringUtils.isNoneBlank(queryParams.getProjectFinanceCode()), Project::getProjectFinanceCode,
                queryParams.getProjectFinanceCode())
            .eq(StringUtils.isNotBlank(queryParams.getBusinessType()), Project::getBusinessType,
                queryParams.getBusinessType())
            .eq(StringUtils.isNotBlank(queryParams.getYunshuOrgId()), Project::getYunshuOrgId,
                queryParams.getYunshuOrgId())
            .ge(Objects.nonNull(queryParams.getContractAmountMin()), Project::getContractAmount,
                queryParams.getContractAmountMin())
            .le(Objects.nonNull(queryParams.getContractAmountMax()), Project::getContractAmount,
                queryParams.getContractAmountMax())
            .like(StringUtils.isNotBlank(queryParams.getCpmProjectKey()), Project::getCpmProjectKey,
                queryParams.getCpmProjectKey())
            .eq(StringUtils.isNotBlank(queryParams.getProjectStatusEng()), Project::getProjectStatusEng,
                queryParams.getProjectStatusEng())
            .eq(StringUtils.isNotBlank(queryParams.getProjectStatusFin()), Project::getProjectStatusFin,
                queryParams.getProjectStatusFin())
            .eq(StringUtils.isNotBlank(queryParams.getProjectStatusBiz()), Project::getProjectStatusBiz,
                queryParams.getProjectStatusBiz())
            .orderByDesc(Project::getCreateAt);
        return queryWrapper;
    }

    /**
     * 初始化项目
     *
     * @param req req
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/07/25 15:38
     */
    @Override
    public Long initProject(OMInitProjectSaveReq req) {
        final Project project = BeanMapUtils.map(req, Project.class);
        project.setSourceSystem(1).setProjectStatus(1);
        genProjectOtherInfo(project);
        if (StringUtils.isBlank(project.getCpmProjectKey())) {
            project.setCpmProjectKey(projectService.getCpmProjectKey());
        }
        if (StringUtils.isNoneBlank(req.getYunshuOrgId())) {
            // 更新云枢信息
            upadteYunshuOrgInfo(project, req.getYunshuOrgId());
        }
        projectMapper.insert(project);
        return project.getId();
    }

    private void upadteYunshuOrgInfo(Project project, String yunshuOrgId) {
        // 同时修改云枢相关的其他字段
        try {
            final YunShuSmartConstructionOrgResp info =
                    yunShuSmartConstructionOrgService.findDeptAndParentAndTreeInfo(yunshuOrgId);
            project.setYunshuParentOrgId(info.getOrgID()).setYunshuParentOrgName(info.getOrgFullName())
                    .setYunshuParentTreeId(info.getTreeId()).setYunshuQueryCode(info.getChild().getQueryCode())
                    .setYunshuTreeId(info.getChild().getTreeId());
        } catch (Exception e) {
            log.error("修订云枢时获取云枢上级组织信息失败", e);
            throw e;
        }
    }

    /**
     * 查询初始化项目
     *
     * @param id id
     * @return com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp
     * <AUTHOR>
     * @date 2023/07/25 15:39
     */
    @Override
    public OMInitProjectResp getInitProject(Long id) {
        return ommapper.getInitProjectById(id);
    }

    /**
     * 更新初始化项目
     *
     * @param req req
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/07/25 15:39
     */
    @Override
    public Boolean updateInitProject(OMInitProjectUpdateReq req) {
        Project project = BeanMapUtils.map(req, Project.class);
        genProjectOtherInfo(project);
        return projectMapper.updateById(project) == 1;
    }

    /**
     * 临时批量添加项目部idpath
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2023/08/11 14:08
     */
    @Override
    public Boolean batchAddProjectDeptIdPath() {
        ommapper.batchAddProjectDeptIdPath();
        return Boolean.TRUE;
    }

    /**
     * 重启财商立项流程
     *
     * @param subscribeId
     * @param projectId   项目id
     * @return {@link Boolean}
     */
    @Override
    public Boolean restartFinanceProcess(Long subscribeId, Long projectId) {
        // final ProjectProgress projectProgress = new ProjectProgress();
        // final long currentTime = Instant.now().toEpochMilli();
        // projectProgress.setProjectId(projectId);
        // eventService.asyncPushToFinance(projectProgress, currentTime, subscribeId);
        // return Boolean.TRUE;

        projectEventListener.restartPushEvent(projectId, subscribeId, FlowNodeEnum.MARKETING_SEGMENT,
                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE);

        return Boolean.TRUE;
    }

    /**
     * 重新智慧工地立项流程
     *
     * @param projectId   项目id
     * @param subscribeId 业务系统id
     * @return {@link Boolean}
     */
    @Override
    public Boolean restartSmartProcess(Long subscribeId, Long projectId) {

        projectEventListener.restartPushEvent(projectId, subscribeId, FlowNodeEnum.MARKETING_SEGMENT,
                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE);
        return Boolean.TRUE;
    }

    /**
     * 重新缓存云枢组织
     *
     * @param parentId 上级节点
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public Boolean reCacheYunshuOrg(String parentId) {
        orgCacheService.cacheYunshuOrgTreeV3(parentId);
        return Boolean.TRUE;
    }

    /**
     * 根据项目创建项目进度，如进度已存在则不操作
     *
     * @param projectId 项目id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023/08/03 09:24
     */
    @Override
    public Boolean saveProjectProgress(Long projectId) {
        ommapper.insertIgnoreProjectProgress(projectId);
        return true;
    }

    @Override
    public Page<ProjectFlowEventLogResp> getFlowEventRecord(ProjectEventFlowReq req) {
        final Integer size = req.getSize();
        final Integer current = req.getCurrent();
        com.github.pagehelper.Page<ProjectFlowEventLogResp> pageInfo = PageMethod.startPage(current, size).doSelectPage(
                () -> projectEventPushRecordService.queryEventAndPushRecords(req));

        List<ProjectFlowEventLogResp> resultList = pageInfo.getResult();

        return new Page<ProjectFlowEventLogResp>(pageInfo.getTotal(), current, size).setRecords(resultList);
    }

    @Override
    public Boolean updateProject(OMProjectUpdateReq updateReq) {
        final Project project = BeanUtil.copyProperties(updateReq, Project.class);
        return projectMapper.updateProjectById(project) == 1;
    }

    @Override
    public Long insertProject(OMProjectInitReq initReq) {
        final Project project = BeanUtil.copyProperties(initReq, Project.class);
        projectMapper.insert(project);
        return project.getId();
    }

    @Override
    public Boolean updateTenderSummaryFile(OMBidSummaryReq summaryReq) {
        final BidSummary bidSummary = BeanUtil.copyProperties(summaryReq, BidSummary.class);
        return bidSummaryService.updateById(bidSummary);
    }

    @Override
    public Boolean updateContractFile(OMContractReq contractReq) {
        final Contract contract = BeanUtil.copyProperties(contractReq, Contract.class);
        return bidContractService.updateById(contract);
    }

    @Override
    public Boolean updateBureauContractFile(OMBureauContractReq bureauContractReq) {
        final BureauContract bureauContract = BeanUtil.copyProperties(bureauContractReq, BureauContract.class);
        return bidBureauContractService.updateById(bureauContract);
    }

    @Override
    public Boolean updateBureauSupplementaryAgreementFile(OMBureauSupplementaryAgreementReq agreementReq) {
        final BureauSupplementaryAgreement bureauSupplementaryAgreement = BeanUtil.copyProperties(agreementReq,
                BureauSupplementaryAgreement.class);
        return bidBureauSupplementaryAgreementService.updateById(bureauSupplementaryAgreement);
    }

    @Override
    public Boolean updateAgreementFile(OMSupplementaryAgreementReq agreementReq) {
        final SupplementaryAgreement supplementaryAgreement = BeanUtil.copyProperties(agreementReq,
                SupplementaryAgreement.class);
        return bidSupplementaryAgreementService.updateById(supplementaryAgreement);
    }

    @Override
    public Boolean updateBid(OMBidApprovalReq approvalReq) {
        final BidApproval bidApproval = BeanUtil.copyProperties(approvalReq, BidApproval.class);
        return bidApprovalService.updateById(bidApproval);
    }

    @Override
    public void exportAllProject(HttpServletResponse response, ProjectQueryParams queryParams) {
        String fileName =
                "项目列表信息(运维)-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Supplier<List<Project>> supplier = () -> getListCloudPivotExPortData(queryParams);
        try {
            exportData(response, fileName, Project.class, supplier, null, cpmTaskExecutor);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private List<Project> getListCloudPivotExPortData(ProjectQueryParams queryParams) {
        final Integer total = projectMapper.selectCount(getProjectLambdaQueryWrapper(queryParams));
        final int limit = 1500;
        List<CompletableFuture<List<Project>>> futures = new ArrayList<>();
        if (total != 0) {
            long totalPage = (total + limit - 1) / limit;
            for (long i = 0; i < totalPage; i++) {
                long offset = i * limit;
                log.info("offset:{}", offset);
                CompletableFuture<List<Project>> future = CompletableFuture.supplyAsync(() -> {
                    final LambdaQueryWrapper<Project> queryWrapper = getProjectLambdaQueryWrapper(queryParams);
                    queryWrapper.last("limit " + offset + "," + limit);
                    return projectMapper.selectList(queryWrapper);
                }, cpmTaskExecutor);
                futures.add(future);
            }
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            // 等待所有任务执行完成，并获取结果
            CompletableFuture<List<Project>> combinedFuture = allFutures.thenApply(v ->
                    futures.stream()
                            .map(CompletableFuture::join)
                            .flatMap(List::stream)
                            .collect(Collectors.toList()));
            return combinedFuture.join();

        } else {
            return Collections.emptyList();
        }
    }

    @Override
    public AddDepartmentResp initOrgToSmartSiteTree(OMCreateOrgReq initReq, ProjectProgressEnum progressEnum) {
        final Project project =
                Optional.of(initReq.getProjectId()).map(projectMapper::selectById).orElseThrow(() ->
                        new FrameworkException(-1, "项目不存在"));
        // 获取工地树组织信息
        final YunshuOrgDepartmentEntity siteOrgInfo = getSmartSiteOrgInfo(initReq.getParentOrgId());
        final String parentTreeId =
                Optional.ofNullable(siteOrgInfo).map(YunshuOrgDepartmentEntity::getTreeId).orElseThrow(() -> new FrameworkException(-1, "获取上级组织信息失败"));
        String orgName = Optional.of(initReq).map(OMCreateOrgReq::getOrgName).orElse(project.getCpmProjectName());
        final String orgAbbr =
            Optional.of(initReq).map(OMCreateOrgReq::getOrgAbbr).filter(StringUtils::isNoneBlank).orElse(orgName);
        String projectFinanceCode = initReq.getFinanceProjectCode();
        if (StringUtils.isBlank(initReq.getFinanceProjectCode())) {
            projectFinanceCode = project.getProjectFinanceCode();
        }
        final String cpmProjectKey = project.getCpmProjectKey();
        final List<String> labels = initReq.getLabels();
        log.info(
            "调用组织中心创建组织，orgName：{}，orgAbbr: {}, parentTreeId: {},departmentCode：{},projectFinanceCode：{},"
                + "createUserId：{}",
            orgName, orgAbbr, parentTreeId, cpmProjectKey, projectFinanceCode, LoginUserUtil.userIdOrNull());
        ExecuteForAddDepartment executeForAddDepartment = g3OrgService.executeForAddDepartment(orgName, parentTreeId,
            cpmProjectKey, projectFinanceCode, LoginUserUtil.userIdOrNull(), null, orgAbbr, labels);
        if (!executeForAddDepartment.isSuccess()) {
            throw new FrameworkException(-1, "组织中心创建组织失败：" + executeForAddDepartment.getErrmsg());
        }
        AddDepartmentResp newDepartment = executeForAddDepartment.getData();

        final ProjectUpdateYunshuOrgIdReq yunshuOrgIdReq =
                new ProjectUpdateYunshuOrgIdReq().setYunshuOrgId(newDepartment.getDepartmentId()).setId(project.getId());
        // 更新项目组织id， 并触发工地更新/立项事件 并视立项状态触发标准立项事件
        cpmScheduledExecutor.schedule(() -> projectService.updateYunshuOrgId(yunshuOrgIdReq,
                RevisionTypeEnum.CREATE_ORG_IN_UC, "组织创建", progressEnum), 3 * 1000L, TimeUnit.MILLISECONDS);
        return newDepartment;

    }

    @Override
    public List<YunshuOrgDepartmentTreeModel> getOrgTreeFromSmartSite(String treeParentId) {
        final ExecuteGetChildDepartmentsResp childDepartments =
                ucAppOpenApiFeign.getChildDepartments(UcOpenApiProperties.SMART_SITE, treeParentId);
        if (!childDepartments.isSuccess()) {
            throw new FrameworkException(-1, "获取子组织信息失败：" + childDepartments.getErrmsg());
        }
        return childDepartments.getData();
    }

    @Override
    public YunshuOrgDepartmentEntity getSmartSiteOrgInfo(String orgId) {
        final ExecuteGetDepartmentResp executeGetDepartmentResp =
                g3OrgService.executeGetDepartment(orgId);
//                ucAppOpenApiFeign.executeGetDepartment(UcOpenApiProperties.SMART_SITE, orgId);
        if (!executeGetDepartmentResp.isSuccess()) {
            throw new FrameworkException(-1, "获取组织信息失败：" + executeGetDepartmentResp.getErrmsg());
        }
        return executeGetDepartmentResp.getData();
    }

    @Override
    public YunshuOrgSyncResp getOrgPathName(String orgId) {
        final YunshuOrgSync yunshuOrgSync = yunshuOrgSyncService.getYunshuOrgByDeptId(orgId);
        if (Objects.isNull(yunshuOrgSync)) {
            return new YunshuOrgSyncResp();
        }
        return BeanMapUtils.map(yunshuOrgSync, YunshuOrgSyncResp.class);
    }

    @Override
    public Map<String, Object> createOrgProxy(OrgCreateReq orgCreateReq) {
        AddOrgReq addOrgReq = BeanMapUtils.map(orgCreateReq, AddOrgReq.class);
        final String orgDeptAbbr = Optional.of(orgCreateReq).map(OrgCreateReq::getDepartmentAbbreviation)
            .filter(StringUtils::isNoneBlank).orElse(orgCreateReq.getDepartmentName());
        addOrgReq.setAbbreviation(orgDeptAbbr);
        final ExecuteForAddDepartment executeForAddDepartment =
                ucAppOpenApiFeign.executeForAddDepartment(UcOpenApiProperties.SMART_SITE, addOrgReq);
        if (!executeForAddDepartment.isSuccess()) {
            throw new FrameworkException(-1, "组织中心创建组织失败：" + executeForAddDepartment.getErrmsg());
        }
        AddDepartmentResp data = executeForAddDepartment.getData();
        return BeanUtil.beanToMap(data);
    }

    @Override
    public Boolean exchangeFinanceInfo(Long projectId, Long projectId2) {
        if (projectId.equals(projectId2)) {
            throw new FrameworkException(-1, "不能交换同一个项目");
        }
        final Project project = projectMapper.selectById(projectId);
        final Project project2 = projectMapper.selectById(projectId2);
        if (Objects.isNull(project) || Objects.isNull(project2)) {
            throw new FrameworkException(-1, "项目不存在");
        }
        if (Objects.equals(project.getYunshuOrgId(), project2.getYunshuOrgId())) {
            throw new FrameworkException(-1, "项目组织id一致，无需交换");
        }

        log.info("交换项目财商信息成功，项目1ID：{}，项目2ID：{}", projectId, projectId2);

        // 保存项目1的财商信息
        String tempProjectFinanceCode = project.getProjectFinanceCode();
        String tempProjectFinanceName = project.getProjectFinanceName();
        String tempProjectFinanceAbbreviation = project.getProjectFinanceAbbreviation();

        // 将项目2的财商信息赋值给项目1
        project.setProjectFinanceCode(project2.getProjectFinanceCode())
            .setProjectFinanceName(project2.getProjectFinanceName())
            .setProjectFinanceAbbreviation(project2.getProjectFinanceAbbreviation());

        // 将项目1的财商信息赋值给项目2
        project2.setProjectFinanceCode(tempProjectFinanceCode).setProjectFinanceName(tempProjectFinanceName)
            .setProjectFinanceAbbreviation(tempProjectFinanceAbbreviation);

        // 更新项目1和项目2的其他相关信息
        genProjectOtherInfo(project);
        genProjectOtherInfo(project2);

        // 保存更新后的项目信息
        int updateCount = projectMapper.exchangeFinanceInfo(project);
        updateCount += projectMapper.exchangeFinanceInfo(project2);

        log.info("交换项目财商信息成功，项目1ID：{}，项目2ID：{}", projectId, projectId2);

        return updateCount == 2;

    }
}
