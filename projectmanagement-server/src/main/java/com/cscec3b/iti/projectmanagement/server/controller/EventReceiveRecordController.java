package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IProjectEventReceiveRecordApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommEnumDict;
import com.cscec3b.iti.projectmanagement.api.dto.request.EventReceiveRecordReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.EventReceiveRecordResp;
import com.cscec3b.iti.projectmanagement.server.service.IEventReceiveRecordService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 智慧工地事件通知日志记录
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@RestController
@RequestMapping(IProjectEventReceiveRecordApi.PATH)
@Api(tags = "智慧工地事件通知日志记录")
@AllArgsConstructor
public class EventReceiveRecordController implements IProjectEventReceiveRecordApi {

    private final IEventReceiveRecordService eventReceiveRecordService;

    @Override
    public GenericityResponse<List<CommEnumDict>> getSmartSiteEnum() {
        return ResponseBuilder.fromData(eventReceiveRecordService.getSmartSiteNoticeEnum());
    }

    @Override
    public GenericityResponse<Page<EventReceiveRecordResp>> getRecordPages(final EventReceiveRecordReq receiveRecordReq) {
        return ResponseBuilder.fromData(eventReceiveRecordService.getRecordPages(receiveRecordReq));
    }
}
