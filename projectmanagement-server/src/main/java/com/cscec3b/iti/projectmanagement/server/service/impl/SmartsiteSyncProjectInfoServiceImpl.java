package com.cscec3b.iti.projectmanagement.server.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.cscec3b.iti.projectmanagement.server.entity.SmartsiteSyncProjectInfo;
import com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteSyncProjectInfoMapper;
import com.cscec3b.iti.projectmanagement.server.service.SmartsiteSyncProjectInfoService;

/**
 * 智慧工地同步的数据信息
 *
 * <AUTHOR>
 */
@Service
public class SmartsiteSyncProjectInfoServiceImpl implements SmartsiteSyncProjectInfoService {

    @Resource
    private SmartsiteSyncProjectInfoMapper smartsiteSyncProjectInfoMapper;

    @Override
    public int insertOrUpdate(SmartsiteSyncProjectInfo record) {
        return smartsiteSyncProjectInfoMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(SmartsiteSyncProjectInfo record) {
        return smartsiteSyncProjectInfoMapper.insertOrUpdateSelective(record);
    }

}
