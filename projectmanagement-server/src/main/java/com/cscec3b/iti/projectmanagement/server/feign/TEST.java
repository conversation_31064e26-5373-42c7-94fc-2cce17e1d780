package com.cscec3b.iti.projectmanagement.server.feign;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

import static cn.hutool.crypto.SecureUtil.md5;

public class TEST {

    public static void main(String[] args) {
        String timeStamp = java.lang.String.valueOf(System.currentTimeMillis());

        Map<String, Object> param = new HashMap<>();
        param.put("appId", "844d2f7e70fb46ec97570c205d25f493");
        param.put("timeStamp", "1722514412993");
        String upperCase = generateSign(param, "NzJlMmViNDQtMDQ0Yi00OTIxLWE0NDYtNDdhMWIxOGQ1OTNi");
        System.out.println(timeStamp);
        System.out.println(upperCase);
    }

    public static String generateSign(Map<String, Object> params, String secretKey) {
        // 生成签名前先去除sign
        params.remove("sign");
        String stringA = packageSign(params);
        String stringSignTemp = stringA + "&key=" + secretKey;
        return md5(stringSignTemp).toUpperCase();
    }

    private static String packageSign(Map<String, Object> params) {
        // 先将参数以其参数名的字典序升序进行排序
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        // 遍历排序后的字典，将所有参数按"key=value"格式拼接在一起
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, Object> param : sortedParams.entrySet()) {
            String value = param.getValue().toString();
            if (value == null || value.equals("")) {
                continue;
            }
            if (first) {
                first = false;
            } else {
                sb.append("&");
            }
            sb.append(param.getKey()).append("=");
            sb.append(value);
        }
        return sb.toString();
    }

}
