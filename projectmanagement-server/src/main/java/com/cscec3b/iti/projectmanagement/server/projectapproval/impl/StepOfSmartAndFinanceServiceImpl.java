package com.cscec3b.iti.projectmanagement.server.projectapproval.impl;

import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnumV2;
import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeStepMappingMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.YunshuOrgSyncMapper;
import com.cscec3b.iti.projectmanagement.server.projectapproval.AbstractApprovalStep;
import com.cscec3b.iti.projectmanagement.server.projectapproval.StepServiceFactory;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import com.g3.G3OrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 立项步骤-财商和工地
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class StepOfSmartAndFinanceServiceImpl extends AbstractApprovalStep {


    /**
     * 事件发布器
     */
    private final ApplicationEventPublisher publisher;

    private final ProjectProgressService progressService;

    protected StepOfSmartAndFinanceServiceImpl(ProjectMapper projectMapper, BidApprovalMapper bidApprovalMapper,
            StepServiceFactory stepServiceFactory, ApplicationEventPublisher publisher,
            ProjectProgressService progressService, G3OrgService g3OrgService, YunshuOrgSyncMapper orgSyncMapper,
            ApprovalTypeStepMappingMapper typeStepMappingMapper,
            TaskAndMessageService taskAndMessageService, UcOpenApiProperties ucOpenApiProperties) {
        super(projectMapper, bidApprovalMapper, stepServiceFactory, typeStepMappingMapper, taskAndMessageService,
              ucOpenApiProperties);
        this.publisher = publisher;
        this.progressService = progressService;
    }


    @Override
    public Integer currentStepNo() {
        return ApprovalStepEnumV2.FINANCE_SMART.getNo();
    }


    /**
     * 自动任务，创建项目部及财商一体化推送事件
     *
     * @param bidApproval 中标未立项数据
     * @return
     */
    @Override
    public BidApproval autoRunTask(BidApproval bidApproval) {
        this.preEvent(bidApproval);
        // 判断项目部创建类型为是则去组织中心创建项目部
//        Integer deptCreateType = bidApproval.getDeptCreateType();
//        if (Objects.equals(deptCreateType, 1)) {
//            // 获取项目部名称
//            String deptName = bidApproval.getDeptName();
//            // 获取上级组织id，并获取上级treeId
//            String parentTreeId = Optional.ofNullable(bidApproval.getParentDeptId()).map(orgSyncMapper::getByDeptId)
//                    .map(YunshuOrgSync::getId).orElseThrow(() -> new FrameworkException(-1, "获取组织信息异常"));
//            ExecuteForAddDepartment departmentResp = g3OrgService.executeForAddDepartment(deptName,
//                    parentTreeId, String.valueOf(bidApproval.getCpmProjectId()));
//            if (departmentResp.isSuccess()) {
//                AddDepartmentResp department = departmentResp.getData();
//                bidApproval.setDeptId(department.getDepartmentId()).setDeptName(department.getDepartmentName());
//            }
//        } else if (Objects.equals(deptCreateType, 0)) {
//            YunshuOrgSync deptInfo = Optional.ofNullable(bidApproval.getDeptId())
//                    .map(orgSyncMapper::getByDeptId)
//                    .orElseThrow(() -> new FrameworkException(-1, "获取挂接项目部信息失败"));
//            bidApproval.setDeptName(deptInfo.getName()).setDeptId(deptInfo.getDeptId());
//        }
//        bidApprovalMapper.updateById(bidApproval);
        return bidApproval;
    }


    /**
     * 当前方法需要检查2项： <br>
     * 1. 财商是否已完成立项；
     * 2. 项目部是否已创建，或挂接的项目部是否已选择。
     *
     * @param approval 中标未立项
     * @return boolean
     */
    @Override
    public boolean isComplete(BidApproval approval) {
        String independentProject = approval.getIndependentProject();
        IndependTypeEnum independTypeEnum = IndependTypeEnum.getEnumByCode(independentProject);
        if (IndependTypeEnum.INDEPENDENT.equals(independTypeEnum)) {
            // 检查项目部创建状态： 0:挂接项目部； 1: 创建项目部; 最终都将写入deptId;
            String deptId = approval.getDeptId();
            // 检查财商是否立项完成
            ProjectProgress projectProgress = progressService.selectProjectProgress(approval.getCpmProjectId());
            // 项目部已挂接，且财商一体化立项已完成，可进入下一步
            return Objects.nonNull(projectProgress) && Objects.nonNull(deptId)
                    && Objects.equals(projectProgress.getApproveStatus(),
                    ProjectProgressEnum.COMPLETED.getDictCode());
        }
        // 除了独立立项能走到这一步，其他选项均不能走到这一步；
        return false;
    }

    /**
     * 只推送财商立项事件
     *
     * @param bidApproval 中标未立项
     */

    public void preEvent(BidApproval bidApproval) {
        if (Objects.nonNull(bidApproval)) {
            log.info("[智慧工地与财商一体化] preEvent： ->  市场营销事件触发");
            final String independentProject = bidApproval.getIndependentProject();
            final IndependTypeEnum independTypeEnum = IndependTypeEnum.getEnumByCode(independentProject);
            if (IndependTypeEnum.INDEPENDENT.equals(independTypeEnum) && Objects.nonNull(
                    bidApproval.getCpmProjectId())) {
                // 触发项目流转事件(市场营销板块立项 后置事件) 推送财商和工地 ，
                publisher.publishEvent(new CpmProjectFlowEvent(this, bidApproval.getCpmProjectId(),
                        FlowNodeEnum.MARKETING_SEGMENT, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
            }
        }
    }

    /**
     * 此处监听标准立项完成事件
     *
     * @param event 事件
     */
    /**
     * 此处应监听标准立项完成事件，
     * 防止特殊原因组织创建失败 或财商推送失败，人工补偿无法触发财商立项等情况
     *
     * @param event 事件
     */
    @EventListener(condition = "#event.nodeEnum == T(com.cscec3b.iti.projectmanagement.server.pushservice.enums" +
            ".FlowNodeEnum).FINANCE_SMART_SITE_APPROVAL && #event.handlerEnum == T(com.cscec3b.iti.projectmanagement" +
            ".server.pushservice.enums.FlowNodeHandlerEnum).POST && #event.dataTypeEnum == T(com.cscec3b.iti" +
            ".projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum).CREATE")
    public void event(CpmProjectFlowEvent event) {
        log.info("[智慧工地与财商一体化] eventListener： ->  标准立项完成: currentNo：{}", currentStepNo());
        super.eventListener(event);
    }
}
