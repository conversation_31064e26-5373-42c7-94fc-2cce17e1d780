package com.cscec3b.iti.projectmanagement.server.pushservice.filter;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.routines.UrlValidator;
import org.springframework.stereotype.Component;

import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;

/**
 * 消费者URL过滤器，
 *
 * <AUTHOR>
 * @date 2024/08/20
 */
@Component
public class SubscriberUrlFilter extends AbstractEventMsgFilter {

    /**
     * new UrlValidator().isValid(url)) 无法判断localhost的URL，请修改为127.0.0.1
     * @param project project
     * @param subscriber subscriber
     * @param pushRecord pushRecord
     * @return boolean
     */
    @Override
    protected boolean doFilter(Project project, ProjectFlowEventSubscribeDto subscriber,
            ProjectEventPushRecord pushRecord) {
        if (Objects.nonNull(subscriber) && StringUtils.isNotBlank(subscriber.getPushUrl())
            && new UrlValidator().isValid(subscriber.getPushUrl())) {
            return true;
        } else {
            final String errMsg = String.format("URL检查不通过: %s", subscriber.getPushUrl());
            pushRecord.setLogLevel(LOG_ERROR).setErrMsg(errMsg);
            return false;
        }
        // return Optional.of(subscriber).map(ProjectFlowEventSubscribeDto::getPushUrl)
        // .map(url -> new UrlValidator().isValid(url)).filter(Boolean.TRUE::equals)
        // .orElseThrow(() -> {
        //
        // return new FrameworkException(-1, String.format("URL检查不通过: %s", subscriber.getPushUrl()));
        // });
    }

    @Override
    public int getOrder() {
        return 2;
    }
}
