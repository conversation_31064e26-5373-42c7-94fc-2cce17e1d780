package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectMapping;
import com.cscec3b.iti.projectmanagement.server.entity.Project;

public interface EngineeringStandardProjectMappingService extends IService<EngineeringStandardProjectMapping> {

    /**
     * 检查各系统状态
     * 
     * @param project 项目信息
     * @return int
     */
    void performSystemCheck(Long engineeringStandardProjectId, Project project);
}
