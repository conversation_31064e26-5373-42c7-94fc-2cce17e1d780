package com.cscec3b.iti.projectmanagement.server.entity.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 项目流程事件，订阅信息dto
 * <AUTHOR>
 * @date 2023/09/20 23:14
 **/

@Getter
@Setter
@Accessors(chain = true)
public class ProjectFlowEventSubscribeDto {

	/**
	 * 消费者id
	 */
	private String consumerId;

	/**
	 * 请求id/msgId
	 */
	private String requestId;

	/**
	 * 项目中心项目id
	 */
	private Long projectId;

	/**
	 * 事件id
	 */
	private String eventId;

	/**
	 * 项目流转事件节点（板块）
	 */
	private String flowNodeCode;

	/**
	 * 节点监听点： pre：前置; post：后置
	 */
	private String flowHandlerCode;

	/**
	 * 数据处理类型： create：立项; update: 更新
	 */
	private String flowDataTypeCode;

	/**
	 * 系统来源(订阅系统标识)<br>
	 * 消费者中业务系统标识字段
	 */
	private String appCode;


	/**
	 * 推送url
	 */
	private String pushUrl;

	/**
	 * 事件类型：事件id,逗号分隔
	 */
	private String eventIds;

	private String eventCode;

	/**
	 * 去业务系统app鉴权用：业务系统颁发给项目中心的神禹appKey,未接入则自定义填入
	 */
	private String appKey;

	/**
	 * 去业务系统app鉴权用：业务系统颁发给项目中心的神禹secret,未接入则自定义填入
	 */
	private String appSecret;

	/**
	 * 扩展字段，appkey,secret无法满足时，可以此处扩展
	 */
	private String extension;
	/**
	 * 项目执行单位idPath
	 */
	private String yunshuExecuteUnitIdPath;

	/**
	 * 事件json
	 */
	private String flowItems;

	/**
	 * spi扩展实现类名称
	 */
	private String spiClassName;

}
