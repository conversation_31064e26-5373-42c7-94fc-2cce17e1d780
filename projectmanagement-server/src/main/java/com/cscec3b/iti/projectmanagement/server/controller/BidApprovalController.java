package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.bidapproval.IBidApprovalApi;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.BidApprovalPageReq;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalPageResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping(IBidApprovalApi.PATH)
public class BidApprovalController implements IBidApprovalApi {

    /**
     * 中标未立项服务类
     */
    private final IBidApprovalService bidApprovalService;

    @Override
    public GenericityResponse<Page<BidApprovalPageResp>> pageList(final BidApprovalPageReq request) {
        return ResponseBuilder.fromData(bidApprovalService.getBidApprovalPageList(request));
    }
}
