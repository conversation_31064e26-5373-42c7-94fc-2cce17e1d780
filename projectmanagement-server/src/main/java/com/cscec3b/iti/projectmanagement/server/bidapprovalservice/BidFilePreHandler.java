package com.cscec3b.iti.projectmanagement.server.bidapprovalservice;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.MarketFileBaseReq;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommonProjectFieldDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ContractFileUpdateParamReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFileDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.server.config.CommonDataCache;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.DictMainData;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectStatusBizEnum;
import com.cscec3b.iti.projectmanagement.server.enums.SourceSystemEnum;
import com.cscec3b.iti.projectmanagement.server.service.DictMainDataService;
import com.google.common.base.Joiner;

/**
 * 市场营销合同处理方法
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
public interface BidFilePreHandler<T extends MarketFileBaseReq> {

    /**
     * 通用保存文件方法
     *
     * @param data        文件 信息
     * @param preFileId   上游文件id
     * @param curFileType 当前文件类型
     * @return {@link Boolean}
     */
    IndContractsTypeEnum doSaveFile(String preFileId, String curFileType, T data);

    /**
     * 通过belongId查询文件是否存在
     * @param belongId belongId
     * @return {@link Boolean}
     */
    Boolean existFileByBelongId(Long belongId);


    /**
     * 转换成BidApproval对象
     *
     * @param data        文件信息
     * @param bidApproval 中标未立项信息
     * @return {@link BidApproval}
     */
    default void convertToBidApproval(BidApproval bidApproval, T data) {
    }


    /**
     * 合同文件列表
     *
     * @param pageReq     分页查询参数
     * @param scopeTypeEnum 合同文件类型
     * @return {@link Page}<{@link ContractFilePageResp}>
     */
    Page<ContractFilePageResp> getContractFilePageList(ContractFilePageReq pageReq, IndContractsTypeEnum scopeTypeEnum);

    /**
     * 下一节点文件类型
     *
     * @return {@link String}
     */
    default IndContractsTypeEnum nextContractFileType() {
        return null;
    }

    /**
     * 通过Id获取文件信息
     *
     * @param id 源文件id（市场营销）
     * @return {@link Object}
     */
    ContractFileRelationResp getFileInfoById(Long id);

    /**
     * 通过belongId获取前置文件信息
     *
     * @param belongId       前置文件id
     * @param belongFileType 前置文件类型
     * @param excludeId      排除的主键id
     * @return {@link ContractFileRelationResp}
     */
    default ContractFileRelationResp getFileInfoByBelongId(Integer belongFileType, Long belongId, Long excludeId) {
        return null;
    }

    /**
     * 通过前置文件id获取下一节点文件列表
     *
     * @param preFileId   前置文件id
     * @param preFileType 前置文件类型
     * @return {@link List}<{@link ContractFileRelationResp}>
     */
    default List<ContractFileRelationResp> getNextFileListByPreFileId(Long preFileId, Integer preFileType) {
        return Collections.emptyList();
    }

    /**
     * 创建项目
     *
     * @param bidApproval
     * @return {@link Long}
     */
    Long createProject(BidApproval bidApproval);


    /**
     * 设置项目通用属性
     *
     * @param project               项目信息
     * @param obj                   项目通用字段信息
     * @param independentContractId 独立id
     * @param contractTypeEnum      合同类型
     */
    default void setProjectProperties(Project project, CommonProjectFieldDto obj, Long independentContractId,
            IndContractsTypeEnum contractTypeEnum) {
        project.setId(null);
        // 默认初始化为 非重大项目
        project.setIsCreateHead(Constants.DATA_FROM_N_CODE);
        // 区域信息
        final String projectBelong = obj.getProjectBelong();
        String region = obj.getCountry();
        if (Constants.PROJECT_BELONG.equals(projectBelong)) {
            region = Joiner.on(Constants.REGION_CONNECTOR).skipNulls().join(obj.getProvince(),
                    obj.getCity(),
                    obj.getRegion());
        }
        final String standardType = Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(obj.getStandardType1(),
                obj.getStandardType2(), obj.getStandardType3(), obj.getStandardType4());
        final String standardTypeCode =
                Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(obj.getStandardType1Code(),
                        obj.getStandardType2Code(), obj.getStandardType3Code(), obj.getStandardType4Code());
        project.setCountDays(obj.getTotalDuration()).setWorkerDateRewardPunish(obj.getDurationAwardType())
                .setAdvancesWay(obj.getPaymentType())
                .setPaymentTypeCode(obj.getPaymentTypeCode())
                .setWorkerRewardPunishAppoint(obj.getDurationAwardClause())
                .setIssuerProject(obj.getSubpackageEngineering()).setQualityGuarantee(obj.getQualityRequirement())
                .setRewardPunishType(obj.getQualityAwardType())
                .setQualityAwardTypeCode(obj.getQualityAwardTypeCode())
                .setRewardPunishTerms(Optional.ofNullable(obj.getQualityAwardClause()).orElse(obj.getRewardPunishTerms()))
                .setSafetyRequirement(obj.getSafeConstruction()).setAdvancesFlag(obj.getAdvanceChargeType())
                .setCompletedRate(obj.getBeCompletedProportion()).setCompletedCycle(obj.getBeCompletedCycle())
                .setSettlementRate(obj.getSettlementPaymentProportion()).setWarrantyPremium(obj.getWarrantyMoney())
                .setWarrantyPremiumWay(obj.getWarrantyMoneyMode()).setAdvancesFundFlag(obj.getAdvanceOrNot())
                .setGuaranteeWay(obj.getPerformanceGuaranteeMode()).setRegion(region)
                .setContractMode(Joiner.on(Constants.MODE_CONNECTOR).skipNulls().join(null, obj.getContractMode1(),
                        obj.getContractMode2()))
                .setContractModeCode(Joiner.on(Constants.MODE_CONNECTOR).skipNulls().join(obj.getContractMode1Code(),
                        obj.getContractMode2Code()))
                .setMarketProjectType(Joiner.on(Constants.MODE_CONNECTOR).skipNulls().join(null,
                        obj.getMarketProjectType(), obj.getMarketProjectType2()))
                .setMarketProjectTypeCode(Joiner.on(Constants.MODE_CONNECTOR).skipNulls()
                        .join(obj.getMarketProjectTypeCode(), obj.getMarketProjectType2Code()))
                .setProjectType(Joiner.on(Constants.MODE_CONNECTOR).skipNulls().join(null, obj.getProjectType(),
                        obj.getProjectType2(), obj.getProjectType3(), obj.getProjectType4()))
                .setProjectTypeCode(Joiner.on(Constants.MODE_CONNECTOR).skipNulls().join(obj.getProjectTypeCode(),
                        obj.getProjectType2Code(), obj.getProjectType3Code(), obj.getProjectType4Code()))
                .setProjectAddress(Optional.ofNullable(region)
                        .map(re -> re.replace(Constants.REGION_CONNECTOR, Constants.MODE_CONNECTOR)
                                + (StringUtils.isEmpty(
                                obj.getAddress()) ? "" : Constants.MODE_CONNECTOR + obj.getAddress())).orElse(null))
                .setCreateAt(System.currentTimeMillis())
                .setContractAmount(
                        obj.getEstimatedCost() == null ? new BigDecimal(Constants.NUMBER_ZERO) : obj.getEstimatedCost())
                .setProjectStatus(YesNoEnum.NO.getDictCode()).setIndependentContractId(independentContractId)
                .setIndependentContractType(contractTypeEnum.getDictCode())
                .setNoTaxIncludedMoney(null).setSourceSystem(SourceSystemEnum.MARKETING.getDictCode())
                .setInnovativeBusinessType(Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(null,
                        obj.getInnovativeBusinessType(), obj.getInnovativeBusinessType2(),
                        obj.getInnovativeBusinessType3(), ""))
                .setStrategicNewBusinessType(Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(null,
                        obj.getStrategicNewBusinessType(), obj.getStrategicNewBusinessType2(),
                        obj.getStrategicNewBusinessType3(), ""))
                .setInnovativeBusinessTypeCode(Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(
                        obj.getInnovativeBusinessTypeCode(), obj.getInnovativeBusinessType2Code(),
                        obj.getInnovativeBusinessType3Code()))
                .setStrategicNewBusinessTypeCode(Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(
                        obj.getStrategicNewBusinessTypeCode(), obj.getStrategicNewBusinessType2Code(),
                        obj.getStrategicNewBusinessType3Code()))
                .setStandardType(standardType).setStandardTypeCodePath(standardTypeCode)
                // 项目初始化时设置项目状态(商务)为未结
                .setProjectStatusBiz(ProjectStatusBizEnum.UNSETTLED_ACCOUNT.getDictCode());
        // 财商业务板块为空并且标准分类，本地映射
        genBusinessSegmentCodePathInProject(project, standardType);
    }

    default void genBusinessSegmentCodePathInProject(Project project, String standardType) {
        final Map<String, String> projectTypeMap = CommonDataCache.PROJECT_TYPE_MAP;
        if (StringUtils.isNotBlank(standardType)) {
            // 标准立项流程，局标准分类不为空，直接映射
            project.setMarketingBusinessSegment(
                    StringUtils.isNotBlank(projectTypeMap.get(standardType)) ? projectTypeMap.get(standardType) : "");
        } else {
            if (SourceSystemEnum.MARKETING.getDictCode().equals(project.getSourceSystem()) && StringUtils.isBlank(
                project.getMarketingBusinessSegmentCodePath())) {
                // 标准立项流程，局标准分类和财商业务板块为空时，取综合口径映射
                final String projectType = project.getProjectType();
                project.setMarketingBusinessSegment(projectTypeMap.get(projectType));
            }
        }
        DictMainDataService dictMainDataService = SpringUtils.getBean(DictMainDataService.class);
        if (dictMainDataService != null && StringUtils.isBlank(project.getMarketingBusinessSegmentCodePath())) {
            // 填充财商业务板块Code
            project.setMarketingBusinessSegmentCodePath(Optional.ofNullable(project.getMarketingBusinessSegment())
                    .map(dictMainDataService::getByFullPathName).map(DictMainData::getOldFullPathCode).orElse(null));
            project.setCpmBusinessSegment(Optional.ofNullable(project.getMarketingBusinessSegmentCodePath())
                .map(dictMainDataService::getByFullPathName).map(DictMainData::getFullPathName).orElse(null));
            project.setCpmBusinessSegmentCodePath(project.getMarketingBusinessSegmentCodePath());
        }
    }

    /**
     * 业务板块映射，优化取局标准分类，其次取综合口径
     *
     * @param standardType 局标准分类
     * @param projectType  综合口径
     * @return {@link String }
     */
    default String genBusinessSegmentCodePathInFile(String standardType, String projectType) {
        final Map<String, String> projectTypeMap = CommonDataCache.PROJECT_TYPE_MAP;
        String businessSegmentStr = Optional.ofNullable(standardType).map(projectTypeMap::get)
                .orElse(projectTypeMap.get(projectType));
        DictMainDataService dictMainDataService = SpringUtils.getBean(DictMainDataService.class);
        if (dictMainDataService != null) {
            // 填充财商业务板块Code
            return Optional.ofNullable(businessSegmentStr)
                    .map(dictMainDataService::getByFullPathName).map(DictMainData::getOldFullPathCode).orElse(null);
        }
        return null;
    }

    /**
     * 通过belongId获取文件详情
     *
     * @param belongId 文件所属id
     * @return {@link ContractFileDetailResp}
     */
    ContractFileDetailResp<?> getFileDetailByBelongId(Long belongId);

    /**
     * 更新文件挂接信息
     *
     * @param independentContractId   独立id
     * @param independentContractType 独立类型
     * @param belongId                belongId
     * @param independentProject      独立项目
     * @return {@link Boolean}
     */
    Boolean hookProject(Long independentContractId, Integer independentContractType,
            Long belongId, String independentProject);

    /**
     * 获取文件详情
     *
     * @param belongFileType          文件类型
     * @param belongId                文件所属id
     * @param fileCode                文件编码
     * @param id                      文件id
     * @param yunshuExecuteUnitIdPath 云枢执行单位IdPath
     * @return {@link ContractFileDetailResp }<{@link ? }>
     */
    List<?> getFileDetail(Integer belongFileType, String belongId, String fileCode, Long id, String yunshuExecuteUnitIdPath);

    /**
     * 更新文件的前置文件信息
     *
     * @param curFileBelongId   当前文件的belongId
     * @param curFileBelongType 当前文件类型
     * @param preFileId         前置文件的belongId
     * @param preFileType       前置文件类型
     * @return boolean
     */
    boolean updateRelationFileByBelongId(String curFileBelongId, Integer curFileBelongType, String preFileId,
                                         Integer preFileType);


    /**
     * 通用更新文件方法
     *
     * @param paramReq 更新属性
     * @param BelongId 文件belongId
     * @return boolean
     */
    boolean commonUpdateData(ContractFileUpdateParamReq paramReq, Long BelongId);

    /**
     * 获取文件信息-根据belongId构建map
     *
     * @param belongId 文件belongId
     * @return {@link Map }<{@link String }, {@link Object }>
     */
    Map<String, Object> getMapByBelongId(Long belongId);
}
