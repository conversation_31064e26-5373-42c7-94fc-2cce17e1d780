package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.BureauContractReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.MarketProReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauContractResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.BureauContract;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.SourceSystemEnum;
import com.cscec3b.iti.projectmanagement.server.enums.WarningStatusEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.BureauContractMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.service.BureauContractService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SysDictDataService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BureauContractServiceImpl implements BureauContractService {

    /**
     * 局内分包合同
     */
    @Resource
    private BureauContractMapper bureauContractMapper;

    /**
     * 项目服务类
     */
    @Resource
    private ProjectService projectService;

    /**
     * 项目进度服务类
     */
    @Resource
    private ProjectProgressService projectProgressService;

    /**
     * 事件触发类
     */
    @Resource
    private ApplicationEventPublisher publisher;

    /**
     * 中标未立项服务类
     */
    @Resource
    private IBidApprovalService bidApprovalService;

    @Resource
    private SysDictDataService dictDataService;

    /**
     * 局内分包合同立项
     *
     * @param request 市场营销推送数据
     * @return 是否立项成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approvalByBureauContract(MarketProReq<BureauContractReq> request) {
        log.info("市场营销局内分包合同立项推送approvalByBureauContract=====>>MarketProReq<BureauContractReq>: {}",  request);
        //独立文件ID
        Long contractId = request.getAssociatedId();
        //独立文件类型
        String contractType = request.getOriginFileType();
        IndContractsTypeEnum contractTypeEnum = IndContractsTypeEnum.getEnumCode(contractType);
        if (Objects.isNull(contractTypeEnum) || Objects.isNull(contractTypeEnum.getDictCode())) {
            throw new BusinessException(8010004, new String[]{"独立文件"});
        }
        final long currentTime = Instant.now().toEpochMilli();

        //合同数据
        BureauContractReq bureauContractReq = request.getData();
        //构建局内分包合同实体
        BureauContract bureauContract = new BureauContract();
        BeanUtils.copyProperties(bureauContractReq, bureauContract);
        String proAttachment = JsonUtils.toJsonStr(bureauContractReq.getProjectAttachment());
        bureauContract.setProjectAttachment(proAttachment);
        bureauContract.setIndependentContractId(contractId);
        bureauContract.setIndependentContractType(contractTypeEnum.getDictCode());
        bureauContract.setUpdateAt(currentTime);
        if (StringUtils.isBlank(bureauContractReq.getYunshuExecuteUnitIdPath())
                && StringUtils.isNotBlank(bureauContractReq.getExecuteUnitIdPath())) {
            bureauContract.setYunshuExecuteUnitId(bureauContractReq.getExecuteUnitId())
                    .setYunshuExecuteUnitCode(bureauContractReq.getExecuteUnitCode())
                    .setYunshuExecuteUnit(bureauContractReq.getExecuteUnit())
                    .setYunshuExecuteUnitIdPath(bureauContractReq.getExecuteUnitIdPath());
        }
        //立项的合同文件的independentContractId和originFileId保持一致
        Long originFileId = bureauContract.getOriginFileId();
        if (Objects.isNull(originFileId) || !Objects.equals(contractId, originFileId)) {
            throw new BusinessException(8010019);
        }
        // 校验是否重复立项
        Long belongId = bureauContract.getBelongId();
        List<Project> projectList = projectService.qryProjectByConId(contractTypeEnum.getDictCode(), contractId);
        if (CollectionUtils.isNotEmpty(projectList)) {
            throw new BusinessException(8010006);
        }
        final BureauContract dbBureauContract =
                bureauContractMapper.selectOne(new LambdaQueryWrapper<BureauContract>().eq(BureauContract::getBelongId,
                        belongId));
        if (Objects.nonNull(dbBureauContract)) {
            bureauContractMapper.updateById(bureauContract.setId(dbBureauContract.getId()));
        } else {
            bureauContract.setCreateAt(currentTime);
            if (bureauContractMapper.insert(bureauContract) != Constants.NUMBER_ONE) {
                throw new BusinessException(8010005, new String[]{"局内分包合同"});
            }
        }


        //构造项目实体,插入项目表
        boolean mappingFlag = true;
        // 构造project 项目信息
        Project project = new Project();
        BeanUtils.copyProperties(bureauContract, project);
        project.setYunshuExecuteUnit(bureauContractReq.getYunshuExecuteUnit());
        project.setYunshuExecuteUnitId(bureauContractReq.getYunshuExecuteUnitId());
        project.setYunshuExecuteUnitCode(bureauContractReq.getYunshuExecuteUnitCode());
        project.setYunshuExecuteUnitIdPath(bureauContractReq.getYunshuExecuteUnitIdPath());
        // 设置项目状态初始状态为立项中，不统计投标总结中除预计造价之外的细分金额，设置立项创建的时间
        project.setProjectAbbreviation(bureauContract.getProjectShortName());
        project.setProjectStatus(YesNoEnum.NO.getDictCode());
        String region;
        if (Constants.PROJECT_BELONG.equals(bureauContractReq.getProjectBelong())) {
            region = bureauContractReq.getProvince() + Constants.REGION_CONNECTOR + bureauContractReq.getCity()
                    + Constants.REGION_CONNECTOR + bureauContractReq.getRegion();
        } else {
            region = StringUtils.isEmpty(bureauContractReq.getCountry()) ? "" : bureauContractReq.getCountry();
        }
        //设置项目的业务字段
        project.setContactPerson("").setContactPersonMobile("").setRealWorkBeginTime(null).setPredictWorkEndTime(null).setRegion(region);
        project.setContractMode((StringUtils.isEmpty(bureauContractReq.getContractMode1()) ? "" : bureauContractReq.getContractMode1())
                        + (StringUtils.isEmpty(bureauContractReq.getContractMode2()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getContractMode2()))
                .setContractModeCode((StringUtils.isEmpty(bureauContractReq.getContractMode1Code()) ? "" :
                        bureauContractReq.getContractMode1Code())
                        + (StringUtils.isEmpty(bureauContractReq.getContractMode2Code()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getContractMode2Code()));
        project.setMarketProjectType((StringUtils.isEmpty(bureauContractReq.getMarketProjectType()) ? "" : bureauContractReq.getMarketProjectType())
                        + (StringUtils.isEmpty(bureauContractReq.getMarketProjectType2()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getMarketProjectType2()))
                .setMarketProjectTypeCode((StringUtils.isEmpty(bureauContractReq.getMarketProjectTypeCode()) ? "" :
                        bureauContractReq.getMarketProjectTypeCode())
                        + (StringUtils.isEmpty(bureauContractReq.getMarketProjectType2Code()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getMarketProjectType2Code()));
        project.setProjectType((StringUtils.isEmpty(bureauContractReq.getProjectType()) ? "" : bureauContractReq.getProjectType())
                + (StringUtils.isEmpty(bureauContractReq.getProjectType2()) ? "" : Constants.MODE_CONNECTOR + bureauContractReq.getProjectType2())
                + (StringUtils.isEmpty(bureauContractReq.getProjectType3()) ? "" : Constants.MODE_CONNECTOR + bureauContractReq.getProjectType3())
                        + (StringUtils.isEmpty(bureauContractReq.getProjectType4()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getProjectType4()))
                .setProjectTypeCode((StringUtils.isEmpty(bureauContractReq.getProjectTypeCode()) ? "" :
                        bureauContractReq.getProjectTypeCode())
                        + (StringUtils.isEmpty(bureauContractReq.getProjectType2Code()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getProjectType2Code())
                        + (StringUtils.isEmpty(bureauContractReq.getProjectType3Code()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getProjectType3Code())
                        + (StringUtils.isEmpty(bureauContractReq.getProjectType4Code()) ? "" :
                        Constants.MODE_CONNECTOR + bureauContractReq.getProjectType4Code()));
        project.setProjectAddress(region.replace(Constants.REGION_CONNECTOR, Constants.MODE_CONNECTOR)
                + (StringUtils.isEmpty(bureauContractReq.getAddress()) ? ""
                : Constants.MODE_CONNECTOR + bureauContractReq.getAddress()));
        project.setContractAmount(bureauContractReq.getTotalAmount());
        project.setProjectManager(bureauContractReq.getContractManager());
        //时间设置
        project.setCreateAt(System.currentTimeMillis()).setUpdateAt(System.currentTimeMillis());
        project.setIndependentContractNo(bureauContractReq.getContractCode());
        project.setSourceSystem(SourceSystemEnum.MARKETING.getDictCode());
        project.setCountryProjectTypeCode(bureauContractReq.getCountryProjectTypeCode())
                .setBusinessTypeCode(bureauContractReq.getBusinessTypeCode())
                .setCustomerLevelCode(bureauContractReq.getCustomerLevelCode())
                .setEnterpriseTypeCode(bureauContractReq.getEnterpriseTypeCode())
                .setPaymentTypeCode(bureauContractReq.getAdvancesWayCode())
                .setQualityAwardTypeCode(bureauContractReq.getRewardPunishTypeCode());

        // 标准分类映射
        projectService.fillFinancialBusinessSegment(project);

        // 标准组织与执行单位映射
//        mappingFlag = projectService.executeUnitMapping(project);

        final String cpmMark = projectService.getCpmProjectKey();
        project.setCpmProjectKey(cpmMark).setCpmProjectName(bureauContractReq.getProjectName())
                .setCpmProjectAbbreviation(bureauContract.getProjectName());

        if (projectService.createProject(project) != Constants.NUMBER_ONE) {
            throw new BusinessException(8010005, new String[]{"项目信息"});
        }
        // 更新中标未立项
        bidApprovalService.update(null, new LambdaUpdateWrapper<BidApproval>()
                .set(BidApproval::getIndependentProject, bureauContract.getIsIndependent())
                .set(BidApproval::getCpmProjectId, project.getId())
                .set(BidApproval::getAssociatedId, contractId)
                .set(BidApproval::getUpdateAt, currentTime)
                .eq(BidApproval::getBelongId, bureauContract.getBelongId())
                .eq(BidApproval::getType, IndContractsTypeEnum.INTERNAL_PRESENTATION.getEnUS()));

        //同步创建项目进度
        projectProgressService.createProgressByApproval(contractTypeEnum, project, mappingFlag);

        log.info("市场营销补充协议立项:1.推送财商系统立项--->2.推送智慧工地立项");
        // 触发项目流转事件(市场营销板块立项 后置事件)
        publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.MARKETING_SEGMENT,
                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        // 设计总院EPC项目创建项目部待办任务
        projectService.publishSheJiYuanEpcEvent(project);
        return true;
    }

    /**
     * 局内分包合同录入（挂接）
     *
     * @param request 市场营销推送数据
     * @return 是否录入成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean entry(MarketProReq<BureauContractReq> request) {
        log.info("市场营销局内分包合同录入entry=====>>MarketProReq<BureauContractReq>: {}",  request);
        IndContractsTypeEnum contractTypeEnum = IndContractsTypeEnum.getEnumCode(request.getOriginFileType());
        if (Objects.isNull(contractTypeEnum) || Objects.isNull(contractTypeEnum.getDictCode())) {
            throw new BusinessException(8010004, new String[]{"独立文件"});
        }
        BureauContractReq bureauContractReq = request.getData();
        BureauContract bureauContract = new BureauContract();
        BeanUtils.copyProperties(bureauContractReq, bureauContract);
        String proAttachment = JsonUtils.toJsonStr(bureauContractReq.getProjectAttachment());
        bureauContract.setProjectAttachment(proAttachment);
        Long associatedId = request.getAssociatedId();
        List<Project> projectList = projectService.qryProjectByConId(contractTypeEnum.getDictCode(), associatedId);
        if (CollectionUtils.isEmpty(projectList) || projectList.size() > Constants.NUMBER_ONE) {
            if (ObjectUtils.isNotEmpty(request.getProjectId())) {
                projectList.add(projectService.selectById(request.getProjectId()));
            } else {
                throw new BusinessException(8010007);
            }
        }
        //录入时，independentContractId不能等于originFileId
        Long originFileId = bureauContract.getOriginFileId();
        if (Objects.equals(associatedId, originFileId)) {
            throw new BusinessException(8010203);
        }
        final long currentTime = Instant.now().toEpochMilli();
        bureauContract.setIndependentContractId(associatedId);
        bureauContract.setIndependentContractType(contractTypeEnum.getDictCode());
        bureauContract.setUpdateAt(currentTime);
        if (StringUtils.isBlank(bureauContractReq.getYunshuExecuteUnitIdPath())
                && StringUtils.isNotBlank(bureauContractReq.getExecuteUnitIdPath())) {
            bureauContract.setYunshuExecuteUnitIdPath(bureauContractReq.getExecuteUnitIdPath())
                    .setYunshuExecuteUnitId(bureauContractReq.getExecuteUnitId())
                    .setYunshuExecuteUnitCode(bureauContractReq.getExecuteUnitCode())
                    .setYunshuExecuteUnit(bureauContractReq.getExecuteUnit());
        }
        // 校验是否重复挂接、录入
        Long belongId = bureauContract.getBelongId();
        if (null == belongId) {
            throw new BusinessException(8010204);
        }
        final BureauContract dbBureauContract =
                bureauContractMapper.selectOne(new LambdaQueryWrapper<BureauContract>().eq(BureauContract::getBelongId,
                        belongId));
        if (Objects.nonNull(dbBureauContract)) {
            bureauContractMapper.updateById(bureauContract.setId(dbBureauContract.getId()));
        } else {
            bureauContract.setCreateAt(currentTime);
            if (bureauContractMapper.insert(bureauContract) != Constants.NUMBER_ONE) {
                throw new BusinessException(8010005, new String[]{"局内分包合同"});
            }
        }
        Project project = projectList.get(Constants.NUMBER_ZERO);
        // 更新中标未立项
        bidApprovalService.update(null, new LambdaUpdateWrapper<BidApproval>()
                .set(BidApproval::getIndependentProject, IndependTypeEnum.NON_INDEPENDENT.getCode())
                .set(BidApproval::getCpmProjectId, project.getId())
                .set(BidApproval::getAssociatedId, associatedId)
                .set(BidApproval::getUpdateAt, currentTime)
                .eq(BidApproval::getBelongId, bureauContract.getBelongId())
                .eq(BidApproval::getType, IndContractsTypeEnum.INTERNAL_PRESENTATION.getEnUS()));

        // 金额累加
        project = projectService.setProjectMoney(project);
        project.setUpdateAt(System.currentTimeMillis());
        if (projectService.updateProject(project) != Constants.NUMBER_ONE) {
            throw new BusinessException(8010008, new String[]{"项目信息"});
        }
        // 更新项目进度
        projectProgressService.updateProgressByEntry(contractTypeEnum, project);
        return true;
    }

    /**
     * 获取局内分包合同详情
     *
     * @param id
     * @return
     */
    @Override
    public BureauContractResp get(Long id) {
        BureauContract bureauContract = bureauContractMapper.getById(id);
        if (null == bureauContract) {
            throw new BusinessException(8010081, new String[]{"局内分包合同"});
        }
        BureauContractResp bureauContractResp = new BureauContractResp();
        BeanUtils.copyProperties(bureauContract, bureauContractResp);
        String region = "";
        //设置项目的所属区域
        if (!Constants.PROJECT_BELONG.equals(bureauContractResp.getProjectBelong())) {
            region = bureauContractResp.getCountry();
        } else {
            region = bureauContractResp.getProvince() + Constants.REGION_CONNECTOR + bureauContractResp.getCity()
                    + Constants.REGION_CONNECTOR + bureauContractResp.getRegion();
        }
        bureauContractResp.setRegion(region)
                .setAddress(region.replace(Constants.REGION_CONNECTOR, Constants.MODE_CONNECTOR)
                        + (StringUtils.isEmpty(bureauContractResp.getAddress()) ? ""
                        : Constants.MODE_CONNECTOR + bureauContractResp.getAddress()))
                .setContractMode1(
                        bureauContractResp.getContractMode1() + (StringUtils.isEmpty(bureauContractResp.getContractMode2()) ? ""
                                : Constants.MODE_CONNECTOR + bureauContractResp.getContractMode2()))
                .setMarketProjectType(bureauContractResp.getMarketProjectType()
                        + (StringUtils.isEmpty(bureauContractResp.getMarketProjectType2()) ? ""
                        : Constants.MODE_CONNECTOR + bureauContractResp.getMarketProjectType2()))
                .setProjectType(bureauContractResp.getProjectType()
                        + (StringUtils.isEmpty(bureauContractResp.getProjectType2()) ? ""
                        : Constants.MODE_CONNECTOR + bureauContractResp.getProjectType2())
                        + (StringUtils.isEmpty(bureauContractResp.getProjectType3()) ? ""
                        : Constants.MODE_CONNECTOR + bureauContractResp.getProjectType3())
                        + (StringUtils.isEmpty(bureauContractResp.getProjectType4()) ? ""
                        : Constants.MODE_CONNECTOR + bureauContractResp.getProjectType4()))
                .setStructuralStyle(
                        bureauContractResp.getStructuralStyle() + (StringUtils.isEmpty(bureauContractResp.getStructuralStyle2())
                                ? "" : Constants.MODE_CONNECTOR + bureauContractResp.getStructuralStyle2()));
        return bureauContractResp;
    }

    /**
     * 创建，更新项目进度
     *
     * @param type
     * @param project
     * @param mappingFlag
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProjectProgressStatus(IndContractsTypeEnum type, Project project, boolean mappingFlag) {
        log.info("=========局内分包合同更新项目进度=========");
        final Long projectId = project.getId();
        ProjectProgress progress = projectProgressService.selectProjectProgress(projectId);
        // 挂接到 投标总结下面
        if (progress != null && type.getDictCode().equals(IndContractsTypeEnum.TENDER_SUMMARY.getDictCode())) {
            if (YesNoEnum.YES.getDictCode().equals(progress.getSignStatus())) {
                return;
            }
            progress.setSignStatus(YesNoEnum.YES.getDictCode()).setSignTime(Instant.now().toEpochMilli());
            progress.setWarnStatus(Constants.NUMBER_TWO.equals(progress.getApproveStatus()) ? WarningStatusEnum.DISMISSED.getDictCode() : WarningStatusEnum.NOT_TRIGGERED.getDictCode());
            progress.setCpmRemarks(mappingFlag ? "" : project.getYunshuExecuteUnit());
            projectProgressService.updateProjectProgress(progress);
        } else if (progress == null && type.getDictCode().equals(IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode())) {
            ProjectProgress projectProgress = new ProjectProgress();
            projectProgress.setProjectId(projectId).setWarnStatus(WarningStatusEnum.NOT_TRIGGERED.getDictCode());
            projectProgress.setSignStatus(YesNoEnum.YES.getDictCode()).setSignTime(Instant.now().toEpochMilli());
            projectProgress.setCpmRemarks(mappingFlag ? "" : project.getYunshuExecuteUnit());
            projectProgress.setSmartApproveStatus(Constants.NUMBER_ONE);
            projectProgress.setSmartQueryTime(Instant.now().toEpochMilli());
            projectProgressService.saveProjectProgress(projectProgress);
        }
    }

}
