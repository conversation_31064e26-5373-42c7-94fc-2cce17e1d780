package com.cscec3b.iti.projectmanagement.server.config;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

import org.redisson.client.codec.BaseCodec;
import org.redisson.client.handler.State;
import org.redisson.client.protocol.Decoder;
import org.redisson.client.protocol.Encoder;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.spring.starter.RedissonAutoConfigurationCustomizer;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufInputStream;
import io.netty.buffer.ByteBufOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义redisson序列化配置
 *
 * <AUTHOR>
 * @date 2023/08/15 21:48
 **/

@Slf4j
//@Configuration
public class CustomRedissonConfig implements RedissonAutoConfigurationCustomizer {

    /**
     * 在redisson配置文件中配置序列化器
     *
     * @param configuration the {@link Config} to customize
     */
    @Override
    public void customize(Config configuration) {
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        // 指定序列化输入的类型，类必须是非final修饰的。序列化时将对象全类名一起保存下来
        om.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        configuration.setCodec(new RedissonCodec(om));

    }

    /**
     * 替换Redisson的序列化对象JsonJacksonCodec，兼容原有RedisTemplate的对象序列化。
     *
     * <AUTHOR>
     * @date 2022/12/13
     */
    public class RedissonCodec extends BaseCodec {

        protected final ObjectMapper mapObjectMapper;

        /**
         * @see JsonJacksonCodec
         */
        private final Encoder encoder = new Encoder() {
            @Override
            public ByteBuf encode(Object in) throws IOException {
                ByteBuf out = ByteBufAllocator.DEFAULT.buffer();
                try {
                    ByteBufOutputStream os = new ByteBufOutputStream(out);
                    mapObjectMapper.writeValue((OutputStream) os, in);
                    return os.buffer();
                } catch (IOException e) {
                    out.release();
                    throw e;
                } catch (Exception e) {
                    out.release();
                    throw new IOException(e);
                }
            }
        };

        private final Decoder<Object> decoder = new Decoder<Object>() {
            @Override
            public Object decode(ByteBuf buf, State state) throws IOException {
                return mapObjectMapper.readValue((InputStream) new ByteBufInputStream(buf), Object.class);
            }
        };

        public RedissonCodec(ObjectMapper mapObjectMapper) {
            this(mapObjectMapper, true);
        }

        public RedissonCodec(ObjectMapper mapObjectMapper, boolean copy) {
            if (copy) {
                this.mapObjectMapper = mapObjectMapper.copy();
            } else {
                this.mapObjectMapper = mapObjectMapper;
            }
        }

        @Override
        public Decoder<Object> getValueDecoder() {
            return decoder;
        }

        @Override
        public Encoder getValueEncoder() {
            return encoder;
        }

        @Override
        public ClassLoader getClassLoader() {
            if (mapObjectMapper.getTypeFactory().getClassLoader() != null) {
                return mapObjectMapper.getTypeFactory().getClassLoader();
            }

            return super.getClassLoader();
        }
    }
}
