package com.cscec3b.iti.projectmanagement.server.feign;

import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMEngineProjectPushDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * MDM开放API接口
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@FeignClient(name = "openapi-mdm", path = "${shenyuapi.thirdsys.contextPath:}", url = "${cscec.mdm.push: http://mdm-uat.cscec.com/gateway/esb-api}",
        configuration = MDMOpenApiFeignConfig.class)
public interface MDMOpenApiFeign {

    /**
     * 工程项目接收其他源头系统【中建三局】_仅校验
     *
     * @param pushDto 推送数据
     * @return MDM响应结果
     */
    @PostMapping("/GCXMZSJ/GCXM_JS_ZJSJ_JY")
    @ApiOperation(value = "工程项目接收其他源头系统【中建三局】_仅校验")
    MDMApiResponse validateEngineProject(@RequestBody MDMEngineProjectPushDto pushDto);

    /**
     * 工程项目接收其他源头系统【中建三局】
     *
     * @param pushDto 推送数据
     * @return MDM响应结果
     */
    @PostMapping("/GCXMZSJ/GCXM_JS_ZJSJ")
    @ApiOperation(value = "工程项目接收其他源头系统【中建三局】")
    MDMApiResponse pushEngineProject(@RequestBody MDMEngineProjectPushDto pushDto);

    /**
     * 工程项目按需分发ESB_中建三局
     *
     * @param pushDto 推送数据
     * @return MDM响应结果
     */
    @PostMapping("/GCXMZSJ/GCXM_ESB_010048")
    @ApiOperation(value = "工程项目按需分发ESB_中建三局")
    MDMApiResponse distributeEngineProject(@RequestBody MDMEngineProjectPushDto pushDto);

}
