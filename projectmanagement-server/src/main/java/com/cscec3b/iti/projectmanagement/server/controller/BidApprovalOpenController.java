package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.bidapproval.IBidApprovalOpenApi;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.MarketBidApprovalFileReq;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.MarketFileBaseReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.service.IdTableService;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping(IBidApprovalOpenApi.PATH)
public class BidApprovalOpenController implements IBidApprovalOpenApi {

    @Resource
    private IBidApprovalService bidApprovalService;

    @Resource
    private IdTableService idTableService;


    @Logger
    @ShenyuSpringMvcClient
    @Override
    public GenericityResponse<Boolean> syncBidDisable(Long belongId) {
        return ResponseBuilder.fromData(bidApprovalService.syncBidDisable(belongId));
    }


    /**
     * 市营营销合同录入
     *
     * @param request
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    @Logger
    @ShenyuSpringMvcClient
    @Lock(lockKey = "#request.currFileId", waitTime = 3000)
    public GenericityResponse<Boolean> fileRecord(final MarketBidApprovalFileReq<? extends MarketFileBaseReq> request) {
        return ResponseBuilder.fromData(bidApprovalService.fileRecord(request));
    }
}
