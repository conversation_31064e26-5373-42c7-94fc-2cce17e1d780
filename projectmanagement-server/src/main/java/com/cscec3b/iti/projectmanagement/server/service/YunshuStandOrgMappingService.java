package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;
import java.util.Set;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryYunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.UpdateYunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.YunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunshuStandOrgResp;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuStandOrgMapping;

/**
 * 映射关系
 *
 * <AUTHOR>
 */
public interface YunshuStandOrgMappingService {

    /**
     * 保存映射关系
     *
     * @param record 映射详情
     * @return int
     */
    int insert(YunshuStandOrgMapping record);

    /**
     * 查询映射详情
     *
     * @param id id
     * @return YunshuStandOrgMapping
     */
    YunshuStandOrgMapping selectByPrimaryKey(Long id);

    /**
     * 按主键批量更新
     *
     * @param record 映射详情
     * @return int
     */
    int updateByPrimaryKeySelective(YunshuStandOrgMapping record);

    /**
     * 批量可选更新
     * @param list list
     * @return int
     */
    int updateBatchSelective(List<YunshuStandOrgMapping> list);

    /**
     * 批量保存
     * @param list list
     * @return List<YunshuStandOrgReq>
     */
    List<YunshuStandOrgReq> batchInsert(List<YunshuStandOrgReq> list);

    /**
     * 分页查询
     * @param req 查询参数
     * @return page<YunshuStandOrgResp>
     */
    Page<YunshuStandOrgResp> pageList(QueryYunshuStandOrgReq req);

    /**
     * 更新映射关系
     * @param req req
     * @return boolean
     */
    Boolean update(UpdateYunshuStandOrgReq req);

    /**
     * 删除
     * @param ids ids
     * @return boolean
     */
    Boolean delete(Set<Long> ids);

    /**
     * 通过云枢id查询标准组织映射关系
     * <AUTHOR>
     * @date 2023/06/19 09:29
     * @param yunshuUnitId 云枢id
     * @return com.cscec3b.iti.projectmanagement.server.entity.YunshuStandOrgMapping
     */
    YunshuStandOrgMapping selectByYunshuUnitId(String yunshuUnitId);

    /**
     * 通过标准组织code查询标准组织映射关系
     * <AUTHOR>
     * @date 2023/06/19 09:29
     * @param standOrgCode 标准组织code
     * @return com.cscec3b.iti.projectmanagement.server.entity.YunshuStandOrgMapping
     */
    YunshuStandOrgMapping selectByStandOrgCode(String standOrgCode);
}
