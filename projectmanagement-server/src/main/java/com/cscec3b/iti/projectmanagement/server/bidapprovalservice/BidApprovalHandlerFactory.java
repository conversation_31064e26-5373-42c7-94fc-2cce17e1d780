package com.cscec3b.iti.projectmanagement.server.bidapprovalservice;

import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.BidBureauContractServiceImpl;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.BidBureauSupplementaryAgreementServiceImpl;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.BidContractServiceImpl;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.BidSummaryServiceImpl;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.BidSupplementaryAgreementServiceImpl;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl.InvestmentFileServiceImpl;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WorkFlowScopeTypeEnum;
import com.cscec3b.iti.projectmanagement.server.service.IWorkFlowCallBackService;
import com.cscec3b.iti.projectmanagement.server.service.impl.BidApprovalWorkFlowCallBackServiceImpl;
import com.cscec3b.iti.projectmanagement.server.service.impl.CivilMilitaryIntegrationServiceImpl;
import com.cscec3b.iti.projectmanagement.server.service.impl.CivilMilitaryWorkFlowCallBackServiceImpl;
import com.cscec3b.iti.projectmanagement.server.service.impl.SecrecyProjectInfoServiceImpl;
import com.cscec3b.iti.projectmanagement.server.service.impl.SecrecyWorkFlowCallBackServiceImpl;
import org.springframework.stereotype.Component;

@Component
public class BidApprovalHandlerFactory {


    /**
     * 获取文件处理类
     *
     * @param scopeType 文件类型
     * @return {@link BidFilePreHandler}
     */
    public BidFilePreHandler getFileHandler(IndContractsTypeEnum scopeType) {
        switch (scopeType) {
            case TENDER_SUMMARY:
                return SpringUtils.getBean(BidSummaryServiceImpl.class);
            case PRESENTATION:
                return SpringUtils.getBean(BidContractServiceImpl.class);
            case AGREEMENT_PRESENTATION:
            case AGREEMENT:
            case NO_CONTRACT_AGREEMENT:
                return SpringUtils.getBean(BidSupplementaryAgreementServiceImpl.class);
            case INTERNAL_PRESENTATION:
                return SpringUtils.getBean(BidBureauContractServiceImpl.class);
            case INTERNAL_AGREEMENT:
            case NO_CONTRACT_INTERNAL_AGREEMENT:
                return SpringUtils.getBean(BidBureauSupplementaryAgreementServiceImpl.class);
            case SECRECY:
                return SpringUtils.getBean(SecrecyProjectInfoServiceImpl.class);
            case CIVIL_MILITARY_INTEGRATION:
                return SpringUtils.getBean(CivilMilitaryIntegrationServiceImpl.class);
            case INVESTMENT:
                return SpringUtils.getBean(InvestmentFileServiceImpl.class);
            // 其他文件类型
            default:
                throw new IllegalArgumentException("Invalid file type: " + scopeType.getEnUS());
        }
    }

    public IWorkFlowCallBackService getWorkFlowCallBackService(String scopeType) {

        final WorkFlowScopeTypeEnum scopeTypeEnum = WorkFlowScopeTypeEnum.match(scopeType);

        switch (scopeTypeEnum) {
            case BID_APPROVAL:
                // 中标未立项审批回调服务
                return SpringUtils.getBean(BidApprovalWorkFlowCallBackServiceImpl.class);
            case SECRECY:
                // 保密及军民融合项目审批回调服务
                return SpringUtils.getBean(SecrecyWorkFlowCallBackServiceImpl.class);
            case CIVIL_MILITARY:
                // 保密及军民融合项目审批回调服务
                return SpringUtils.getBean(CivilMilitaryWorkFlowCallBackServiceImpl.class);
            default:
                throw new IllegalArgumentException("Invalid scope type: " + scopeType);
        }

    }


}
