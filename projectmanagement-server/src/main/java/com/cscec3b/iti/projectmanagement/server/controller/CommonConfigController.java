package com.cscec3b.iti.projectmanagement.server.controller;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.projectmanagement.api.ICommonConfigApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommonConfigDto;
import com.cscec3b.iti.projectmanagement.server.service.CommonConfigService;

import io.swagger.annotations.Api;

/**
 * 基础配置信息
 * <AUTHOR>
 * @date 2023/1/6 9:05
 */

@Api(tags = {"基础配置信息"})
@RestController
@RequestMapping(ICommonConfigApi.PATH)
public class CommonConfigController implements ICommonConfigApi {

    private final CommonConfigService configService;

    public CommonConfigController(CommonConfigService commonConfigService) {
        this.configService = commonConfigService;
    }

    /**
     * 公共配置信息
     *
     * @return CommonConfigDto
     */
    @Override
    public GenericityResponse<CommonConfigDto> commonConfig() {
        return ResponseBuilder.fromData(configService.commonConfig());
    }
}
