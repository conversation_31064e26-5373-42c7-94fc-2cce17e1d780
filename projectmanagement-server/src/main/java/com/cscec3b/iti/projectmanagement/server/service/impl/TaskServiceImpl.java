package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskQry;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskStatusReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.task.TaskResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.constant.TaskConstant;
import com.cscec3b.iti.projectmanagement.server.entity.Task;
import com.cscec3b.iti.projectmanagement.server.enums.TaskEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.TaskMapper;
import com.cscec3b.iti.projectmanagement.server.service.ITaskService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class TaskServiceImpl implements ITaskService {

    @Resource
    private TaskMapper taskMapper;

    @Override
    public Boolean createTask(TaskReq taskReq) {
        log.info("创建待办任务createTask==>>taskReq:{}", taskReq);
        Task task = new Task();
        BeanUtils.copyProperties(taskReq, task);
        task.setStatus(TaskEnum.TODO.getCode());
        task.setInitTime(System.currentTimeMillis());
        task.setCreatePerson(TaskConstant.SYSTEM);
        task.setCreateTime(System.currentTimeMillis());
        Integer result = taskMapper.createTask(task);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010005, new String[]{TaskConstant.TODO_TASK});
        }
        return true;
    }

    @Override
    public Boolean updateStatusDoing(TaskStatusReq statusReq) {
        log.info("更新任务状态为在办updateStatusToDoing==>>statusReq:{}", statusReq);
        Task task = taskMapper.getById(statusReq.getId());
        // 更新为在办:校验当前状态为待办
        if (TaskEnum.TODO.getCode().equals(task.getStatus())) {
            task.setStatus(TaskEnum.DOING.getCode());
            task.setUpdatePerson(LoginUserUtil.userCode());
            task.setUpdateTime(System.currentTimeMillis());
            Integer result = taskMapper.updateStatus(task);
            if (Constants.NUMBER_ZERO.equals(result)) {
                throw new BusinessException(8010008, new String[]{TaskConstant.TASK_STATUS});
            }
        }
        return true;
    }

    @Override
    public Boolean updateStatusDone(TaskStatusReq statusReq) {
        log.info("更新任务状态为已办updateStatusDone==>>statusReq:{}", statusReq);
        Task task = taskMapper.getById(statusReq.getId());
        // 更新为已办:校验当前状态是否为已办
        if (TaskEnum.DONE.getCode().equals(task.getStatus())) {
            throw new BusinessException(8010035);
        }
        // 更新为已办
        task.setStatus(TaskEnum.DONE.getCode());
        // 设置完成时间
        task.setFinishTime(System.currentTimeMillis());
        // 设置更新人
        task.setUpdatePerson(LoginUserUtil.userCode());
        // 设置更新时间
        task.setUpdateTime(System.currentTimeMillis());
        // 更新状态
        Integer result = taskMapper.updateStatus(task);
        if (Constants.NUMBER_ZERO.equals(result)) {
            throw new BusinessException(8010008, new String[]{TaskConstant.TASK_STATUS});
        }
        return true;
    }

    @Override
    public Page<TaskResp> pageList(TaskQry taskQry) {
        log.info("查询任务列表pageList==>>taskQry:{}", taskQry);
        taskQry.setHandlerPerson(LoginUserUtil.userCode());
        //设置任务查询参数
        Long total = taskMapper.getCount(taskQry);
        //获取任务列表
        List<Task> taskList = taskMapper.pageList(taskQry);
        //创建分页对象
        Page<TaskResp> page = new Page<>(total, taskQry.getCurrent(), taskQry.getSize());
        //判断列表是否为空
        if (CollUtil.isNotEmpty(taskList)) {
            //将列表转换为任务接口返回值
            List<TaskResp> taskRespList = BeanUtil.copyToList(taskList, TaskResp.class, CopyOptions.create());
            //将任务接口返回值赋值给分页对象
            page.setRecords(taskRespList);
        }
        //返回分页对象
        return page;
    }

    @Override
    public Task getTaskByProjectId(Long relationId) {
        log.info("查询项目的待办信息getById==>>relationId:{}", relationId);
        Task task = taskMapper.getTaskByProjectId(relationId);
        if (Objects.isNull(task)) {
            throw new BusinessException(8010055);
        }
        return task;
    }
}
