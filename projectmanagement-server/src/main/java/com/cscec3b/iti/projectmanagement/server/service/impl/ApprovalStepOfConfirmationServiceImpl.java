//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.cscec3b.iti.common.web.exception.FrameworkException;
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.Objects;
//
/// **
// * 立项步骤-信息确认服务
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("confirmationService")
//@RequiredArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfConfirmationServiceImpl extends AbstractApprovalStepService {
//
//    private final BidApprovalMapper bidApprovalMapper;
//
//
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.CONFIRMATION.getNo();
//    }
//
//
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//        final BidApproval bidApproval = this.bidApprovalMapper.selectById(stepReq.getBidApprovalId());
//        if (!Objects.equals(bidApproval.getCurrentStepNo(), currentStep())) {
//            throw new FrameworkException(-1, "进度信息异常");
//        }
//        submitApprovalStep(bidApproval);
//        return true;
//    }
//
//
//
//    @Override
//    public boolean checkCurrentStep(BidApproval bidApproval) {
//        return super.checkCurrentStep(bidApproval);
//    }
//
//}
