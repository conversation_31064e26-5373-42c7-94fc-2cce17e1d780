package com.cscec3b.iti.projectmanagement.server.pushservice.enums;

import lombok.Getter;

/**
 * 项目信息事件枚举
 *
 * <AUTHOR>
 * @date 2023/04/14 16:06
 **/

@Getter
public enum ProjectEventEnum {
    /**
     * 项目立项
     */
    INITIATION("A100001", "项目立项", "initiation"), SMART_UPDATE("UU10001", "项目更新", "smart_update"),
    // 项目挂接
    HOOK("UU10002", "项目挂接", "smart_hook"),
    // 解除挂接
    UNHOOK("UU10003", "取消挂接", "smart_unhook"),
    // 项目废弃
    CANCEL("UU10004", "项目废弃", "smart_cancel"),

    /**
     * 项目中心项目更新
     */
    CPM_UPDATE("UD10001", "项目中心项目更新", "cpm_update");

    final String dictCode;

    final String zhCN;

    final String enUS;

    ProjectEventEnum(String dictCode, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    public String getDesc() {
        return "项目事件校举";
    }


    /**
     * getEnumByCode
     *
     * @param code 代码
     * @return {@link ProjectEventEnum}
     */
    public static ProjectEventEnum getEnumByCode(String code) {
        ProjectEventEnum[] values = ProjectEventEnum.values();
        for (ProjectEventEnum value : values) {
            if (value.getDictCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
