package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.request.MarketProReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.SupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SupplementaryAgreementResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.entity.SupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.SourceSystemEnum;
import com.cscec3b.iti.projectmanagement.server.enums.TaskEnum;
import com.cscec3b.iti.projectmanagement.server.enums.WarningStatusEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.SupplementaryAgreementMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.service.IPmPortalMsgService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.service.SysDictDataService;

import lombok.extern.slf4j.Slf4j;

/**
 * @Description SupplementaryAgreementServiceImpl
 * <AUTHOR>
 * @Date 2022/10/19 16:10
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class SupplementaryAgreementServiceImpl implements SupplementaryAgreementService {

    /**
     * 补充协议服务类
     */
    @Resource
    private SupplementaryAgreementMapper supplementaryAgreementMapper;

    /**
     * 任务服务类
     */
    @Resource
    private TaskServiceImpl taskService;

    /**
     * 项目服务类
     */
    @Resource
    private ProjectService projectService;

    /**
     * 项目进度服务类
     */
    @Resource
    private ProjectProgressService projectProgressService;

    /**
     * 三局通服务类
     */
    @Resource
    private IPmPortalMsgService pmPortalMsgService;

    /**
     * 事件触发类
     */
    @Resource
    private ApplicationEventPublisher publisher;

    /**
     * 中标未立项服务类
     */
    @Resource
    private IBidApprovalService bidApprovalService;

    @Resource
    private SysDictDataService dictDataService;

    private void checkParameters( SupplementaryAgreementReq agreementReq){
        // 补充协议参数校验
        final IndContractsTypeEnum typeEnum = IndContractsTypeEnum.getEnumCode(agreementReq.getBelongFileType());
        if (IndContractsTypeEnum.AGREEMENT.equals(typeEnum)) {
            final String bureauProject = agreementReq.getBureauProject();
            if (!ObjectUtils.allNotNull(bureauProject)) {
                throw new FrameworkException(-1, "是否局重点项目不能为空");
            }
        }
        if (IndContractsTypeEnum.AGREEMENT_PRESENTATION.equals(typeEnum)) {
            final String countryProjectType = agreementReq.getCountryProjectType();
            final String marketProjectType = agreementReq.getMarketProjectType();
            final String projectType = agreementReq.getProjectType();
            if (!ObjectUtils.allNotNull(countryProjectType, marketProjectType, projectType)) {
                throw new FrameworkException(-1, "工程类型（国家标准/总公司市场口径/总公司综合口径）不能为空");
            }
        }
    }

    @Override
    public boolean approvalBySupplementaryAgreement(MarketProReq<SupplementaryAgreementReq> request) {
        log.info("市场营销推送补充协议approvalBySupplementaryAgreement=====>>MarketProReq<SupplementaryAgreementReq>: {}", request);
        SupplementaryAgreementReq saReq = request.getData();
        Long contractId = request.getAssociatedId();
        String contractType = request.getOriginFileType();
        IndContractsTypeEnum contractTypeEnum = IndContractsTypeEnum.getEnumCode(contractType);
        if (contractTypeEnum == null || contractTypeEnum.getDictCode() == null) {
            //独立合同类型获取异常
            throw new BusinessException(8010009);
        }
        // 参数校验
        checkParameters(saReq);

        //校验项目信息
        List<Project> list = projectService.qryProjectByConId(contractTypeEnum.getDictCode(), contractId);
        if (CollectionUtils.isNotEmpty(list)) {
            // 项目已存在，请勿重复立项
            throw new BusinessException(8010011);
        }
        boolean mappingFlag = true;
        Project project = new Project();
        BeanUtils.copyProperties(saReq, project);
        // 设置项目状态初始状态为立项中，录入项目信息
        fillProjectFields(project, saReq, contractTypeEnum, contractId);

        setProjectFields(saReq, project);
        projectService.fillFinancialBusinessSegment(project);
        // 标准组织与执行单位映射
//        mappingFlag = projectService.executeUnitMapping(project);

        StringBuilder region = new StringBuilder();
        if (Constants.PROJECT_BELONG.equals(saReq.getProjectBelong())) {
            region.append(StringUtils.isEmpty(saReq.getProvince()) ? "" : saReq.getProvince())
                    .append(StringUtils.isEmpty(saReq.getCity()) ? "" : Constants.REGION_CONNECTOR + saReq.getCity())
                    .append(StringUtils.isEmpty(saReq.getRegion()) ? "" : Constants.REGION_CONNECTOR + saReq.getRegion());
        } else {
            region.append(StringUtils.isEmpty(saReq.getCountry()) ? "" : saReq.getCountry());
        }
        project.setRegion(region.toString())
                .setProjectAddress(region.toString().replace(Constants.REGION_CONNECTOR, Constants.MODE_CONNECTOR)
                        + (StringUtils.isEmpty(saReq.getAddress()) ? "" : Constants.MODE_CONNECTOR + saReq.getAddress()));

        final String cpmMark = projectService.getCpmProjectKey();
        final String cpmProjectAbbreviation = StringUtils.isBlank(saReq.getProjectAbbreviation())
                ? saReq.getProjectName() : saReq.getProjectAbbreviation();
        project.setCpmProjectKey(cpmMark).setCpmProjectName(saReq.getProjectName()).setCpmProjectAbbreviation(cpmProjectAbbreviation);

        // if (StringUtils.isNotBlank(saReq.getSignedSubjectValue())) {
        //     // 设置签约主体code
        //     project.setSignedSubjectValue(saReq.getSignedSubjectValue());
        //     final String signedSubjectCode = dictDataService.selectDictValue(MARKET_DICT_TYPE,
        //             saReq.getSignedSubjectValue());
        //     project.setSignedSubjectCode(signedSubjectCode);
        //     saReq.setSignedSubjectCode(signedSubjectCode);
        // }

        int resultNum = projectService.createProject(project);
        if (resultNum != Constants.NUMBER_ONE) {
            // 项目信息入库失败
            throw new BusinessException(8010012);
        }

        // 新增补充协议
        saveSupplementaryAgreement(saReq, contractId, contractTypeEnum, project.getId());

        //同步创建项目进度
        projectProgressService.createProgressByApproval(contractTypeEnum, project, mappingFlag);

        //创建立项待办任务
        if (Constants.IS_CREATE_HEAD.equals(saReq.getIsCreateHead())
                && Constants.BUSINESS_TYPE.equals(saReq.getBusinessType())) {
            TaskReq taskReq = new TaskReq().setRelationId(project.getId()).setTaskName("【" + saReq.getProjectName() + "】" + "立项任务")
                    .setTaskType(TaskEnum.CREATE_DIRECTORATE.getCode()).setInitPerson("system").setHandlerPerson("")
                    .setHandlerPerson(saReq.getApprovalPerson());
            taskService.createTask(taskReq);
            //推送待办消息到经办人三局通账户
            pmPortalMsgService.pushMessageToPortal(taskReq);
        }
        log.info("市场营销补充协议立项:1.推送财商系统立项--->2.推送智慧工地立项");
        // 触发项目流转事件(市场营销板块立项 后置事件)
        publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.MARKETING_SEGMENT,
                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        // 设计总院EPC项目创建项目部待办任务
        projectService.publishSheJiYuanEpcEvent(project);
        return true;
    }

    /**
     * 填充项目属性
     *
     * @param project          项目信息
     * @param saReq            请求参数
     * @param contractTypeEnum 合同类型
     * @param contractId       合同id/挂接idd
     */
    private static void fillProjectFields(Project project, SupplementaryAgreementReq saReq,
            IndContractsTypeEnum contractTypeEnum, Long contractId) {
        project.setContactPerson("").setContactPersonMobile("").setCountDays(saReq.getCountdays())
                .setWorkerDateRewardPunish(saReq.getWorkerDatetimeRewardPunish()).setRealWorkBeginTime(null)
                .setPredictWorkEndTime(null).setProjectStatus(YesNoEnum.NO.getDictCode())
                .setCountryProjectType(saReq.getCountryProjectType()).setCountryProjectTypeCode(saReq.getCountryProjectTypeCode())
                .setContractMode((StringUtils.isEmpty(saReq.getContractMode1()) ? "" : saReq.getContractMode1())
                        + (StringUtils.isEmpty(saReq.getContractMode2()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getContractMode2()))
                .setContractModeCode((StringUtils.isEmpty(saReq.getContractMode1Code()) ? "" :
                        saReq.getContractMode1Code())
                        + (StringUtils.isEmpty(saReq.getContractMode2Code()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getContractMode2Code()))
                .setMarketProjectType((StringUtils.isEmpty(saReq.getMarketProjectType()) ? ""
                        : saReq.getMarketProjectType())
                        + (StringUtils.isEmpty(saReq.getMarketProjectType2()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getMarketProjectType2()))
                .setMarketProjectTypeCode((StringUtils.isEmpty(saReq.getMarketProjectTypeCode()) ? ""
                        : saReq.getMarketProjectTypeCode())
                        + (StringUtils.isEmpty(saReq.getMarketProjectType2Code()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getMarketProjectType2Code()))
                .setProjectType((StringUtils.isEmpty(saReq.getProjectType()) ? "" : saReq.getProjectType())
                        + (StringUtils.isEmpty(saReq.getProjectType2()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType2())
                        + (StringUtils.isEmpty(saReq.getProjectType3()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType3())
                        + (StringUtils.isEmpty(saReq.getProjectType4()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType4()))
                .setProjectTypeCode((StringUtils.isEmpty(saReq.getProjectTypeCode()) ? "" : saReq.getProjectTypeCode())
                        + (StringUtils.isEmpty(saReq.getProjectType2Code()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType2Code())
                        + (StringUtils.isEmpty(saReq.getProjectType3Code()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType3Code())
                        + (StringUtils.isEmpty(saReq.getProjectType4Code()) ? ""
                        : Constants.MODE_CONNECTOR + saReq.getProjectType4Code()))
                .setCreateAt(System.currentTimeMillis())
                .setInvestmentProjects(saReq.getInvestmentProjects())
                .setInvestors(StringUtils.isEmpty(saReq.getInvestors()) ? "" : saReq.getInvestors())
                .setBusinessType(saReq.getBusinessType())
                .setSupervisor(StringUtils.isEmpty(saReq.getSupervisor()) ? "" : saReq.getSupervisor())
                .setCustomerName(saReq.getCustomerName()).setEnterpriseType(saReq.getEnterpriseType())
                .setProjectManager(saReq.getContractManager())
                .setContractAmount(contractTypeEnum.equals(IndContractsTypeEnum.AGREEMENT) ? saReq.getSupplementAmount()
                        : saReq.getTotalAmount())
                .setIndependentContractId(contractId)
                .setIndependentContractType(contractTypeEnum.getDictCode())
                .setIndependentContractNo(saReq.getSupplementaryAgreementCode())
                .setSourceSystem(SourceSystemEnum.MARKETING.getDictCode())
                .setStandardType((StringUtils.isEmpty(saReq.getStandardType1()) ? "" : saReq.getStandardType1())
                        + (StringUtils.isEmpty(saReq.getStandardType2()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType2())
                        + (StringUtils.isEmpty(saReq.getStandardType3()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType3())
                        + (StringUtils.isEmpty(saReq.getStandardType4()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType4()))
                .setStandardTypeCodePath((StringUtils.isEmpty(saReq.getStandardType1Code()) ? "" :
                        saReq.getStandardType1Code())
                        + (StringUtils.isEmpty(saReq.getStandardType2Code()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType2Code())
                        + (StringUtils.isEmpty(saReq.getStandardType3Code()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType3Code())
                        + (StringUtils.isEmpty(saReq.getStandardType4Code()) ? ""
                        : Constants.ID_PATH_CONNECTOR + saReq.getStandardType4Code()))
                .setAdvancesRate(saReq.getAdvancesRate()).setPaymentTypeCode(saReq.getAdvancesWayCode())
                .setQualityAwardTypeCode(saReq.getRewardPunishTypeCode());
    }

    private void setProjectFields(SupplementaryAgreementReq obj, Project project) {
        project.setInnovativeBusinessType((StringUtils.isEmpty(obj.getInnovativeBusinessType()) ? "" : obj.getInnovativeBusinessType())
                + (StringUtils.isEmpty(obj.getInnovativeBusinessType2()) ? "" : Constants.ID_PATH_CONNECTOR + obj.getInnovativeBusinessType2())
                + (StringUtils.isEmpty(obj.getInnovativeBusinessType3()) ? "" : Constants.ID_PATH_CONNECTOR + obj.getInnovativeBusinessType3()));
        project.setInnovativeBusinessTypeCode((StringUtils.isEmpty(obj.getInnovativeBusinessTypeCode()) ? "" :
                obj.getInnovativeBusinessTypeCode())
                + (StringUtils.isEmpty(obj.getInnovativeBusinessType2Code()) ? "" :
                Constants.ID_PATH_CONNECTOR + obj.getInnovativeBusinessType2Code())
                + (StringUtils.isEmpty(obj.getInnovativeBusinessType3Code()) ? "" :
                Constants.ID_PATH_CONNECTOR + obj.getInnovativeBusinessType3Code()));
        project.setStrategicNewBusinessType((StringUtils.isEmpty(obj.getStrategicNewBusinessType()) ? "" : obj.getStrategicNewBusinessType())
                + (StringUtils.isEmpty(obj.getStrategicNewBusinessType2()) ? "" : Constants.ID_PATH_CONNECTOR + obj.getStrategicNewBusinessType2())
                + (StringUtils.isEmpty(obj.getStrategicNewBusinessType3()) ? "" : Constants.ID_PATH_CONNECTOR + obj.getStrategicNewBusinessType3()));
        project.setStrategicNewBusinessTypeCode((StringUtils.isEmpty(obj.getStrategicNewBusinessTypeCode()) ? "" :
                obj.getStrategicNewBusinessTypeCode())
                + (StringUtils.isEmpty(obj.getStrategicNewBusinessType2Code()) ? "" :
                Constants.ID_PATH_CONNECTOR + obj.getStrategicNewBusinessType2Code())
                + (StringUtils.isEmpty(obj.getStrategicNewBusinessType3Code()) ? "" :
                Constants.ID_PATH_CONNECTOR + obj.getStrategicNewBusinessType3Code()));
    }

    @Override
    public boolean entrySupplementaryAgreement(MarketProReq<SupplementaryAgreementReq> request) {
        log.info("市场营销补充协议文件推送entrySupplementaryAgreement=====>>MarketProReq<BidSummaryReq>: {}", request);
        SupplementaryAgreementReq saReq = request.getData();
        Long contractId = request.getAssociatedId();
        String contractType = request.getOriginFileType();
        IndContractsTypeEnum contractTypeEnum = IndContractsTypeEnum.getEnumCode(contractType);
        if (contractTypeEnum == null || contractTypeEnum.getDictCode() == null) {
            // 独立合同类型获取异常
            throw new BusinessException(8010009);
        }
        // 参数校验
        checkParameters(saReq);
        //查询补充协议信息是否存在所属源文件id
        // SupplementaryAgreement saExist =
        //         supplementaryAgreementMapper.qrySupplementaryAgreementByBelongId(saReq.getBelongId());
        // if (saExist != null && saExist.getId() != null) {
        //     // 补充协议已录入，请勿重复录入
        //     throw new BusinessException(8010025);
        // }

        //获取项目信息并根据挂接文件更新项目信息属性
        List<Project> list = projectService.qryProjectByConId(contractTypeEnum.getDictCode(), contractId);
        if (CollectionUtils.isEmpty(list) || list.size() != Constants.NUMBER_ONE) {
            if (ObjectUtils.isNotEmpty(request.getProjectId())) {
                list.add(projectService.selectById(request.getProjectId()));
            } else {
                // 无法录入挂接补充协议，项目为空或存在多个使用同一独立文件的项目
                throw new BusinessException(8010021);
            }
        }
        Project project = list.get(Constants.NUMBER_ZERO);
        project.setUpdateAt(System.currentTimeMillis());


//        project = projectService.setProjectMoney(project);
        //         projectService.recalculationProjectAmounts(project);


        // 挂接项目默认为非独立项目
        saReq.setIsIndependent(IndependTypeEnum.NON_INDEPENDENT.getCode());
        // 新增补充协议
        saveSupplementaryAgreement(saReq, contractId, contractTypeEnum, project.getId());

        // 计算更新项目合同金额
        projectService.recalculationProjectAmountsV3(project);
        int resultNum = projectService.updateProject(project);
        if (resultNum != Constants.NUMBER_ONE) {
            // 补充协议更新项目信息失败
            throw new BusinessException(8010022);
        }
        // 更新项目进度表
        projectProgressService.updateProgressByEntry(contractTypeEnum, project);
        return true;
    }

    /**
     * @param saReq            补充协议信息
     * @param contractId       合同id
     * @param contractTypeEnum 合同类型
     * @param projectId        项目id
     */
    private void saveSupplementaryAgreement(SupplementaryAgreementReq saReq, Long contractId,
            IndContractsTypeEnum contractTypeEnum,
            Long projectId) {
        SupplementaryAgreement saEntity = new SupplementaryAgreement();
        BeanUtils.copyProperties(saReq, saEntity);

        final long currentTime = Instant.now().toEpochMilli();
        String proAttachment = JsonUtils.toJsonStr(saReq.getProjectAttachment1());
        saEntity.setProjectAttachment(proAttachment).setIndependentContractId(contractId)
                .setIndependentContractType(contractTypeEnum.getDictCode()).setUpdateAt(currentTime);
        if (StringUtils.isBlank(saReq.getYunshuExecuteUnitIdPath()) && StringUtils.isNotBlank(saReq.getExecuteUnitIdPath())) {
            saEntity.setYunshuExecuteUnitIdPath(saReq.getExecuteUnitIdPath())
                    .setYunshuExecuteUnitId(saReq.getExecuteUnitId())
                    .setYunshuExecuteUnitCode(saReq.getExecuteUnitCode())
                    .setYunshuExecuteUnit(saReq.getExecuteUnit());
        }
        // 填充补充协议金额信息(补充协议立项/录入时取 补充协议金额)
        final IndContractsTypeEnum typeEnum = IndContractsTypeEnum.getEnumCode(saReq.getBelongFileType());
        if (IndContractsTypeEnum.AGREEMENT.equals(typeEnum)) {
            saEntity.setTotalAmount(saReq.getSupplementAmount());
            saEntity.setBelongFileType(typeEnum.getDictCode());
        }

        final SupplementaryAgreement supplementaryAgreement =
                supplementaryAgreementMapper.selectOne(new LambdaQueryWrapper<SupplementaryAgreement>()
                        .eq(SupplementaryAgreement::getBelongId, saEntity.getBelongId()));
        if (Objects.nonNull(supplementaryAgreement)) {
            // 沿用原来的文件类型
            saEntity.setBelongFileType(supplementaryAgreement.getBelongFileType());
            supplementaryAgreementMapper.updateById(saEntity.setId(supplementaryAgreement.getId()));
        } else {
            // 补充协议入库
            saEntity.setCreateAt(currentTime);
            int resultNum = supplementaryAgreementMapper.insert(saEntity);
            if (resultNum != Constants.NUMBER_ONE) {
                // 补充协议入库失败
                throw new BusinessException(8010026);
            }
        }
        // 更新中标未立项
        bidApprovalService.update(null, new LambdaUpdateWrapper<BidApproval>()
                .set(BidApproval::getIndependentProject, saEntity.getIsIndependent())
                .set(BidApproval::getCpmProjectId, projectId)
                .set(BidApproval::getAssociatedId, contractId)
                .set(BidApproval::getUpdateAt, currentTime)
                .eq(BidApproval::getBelongId, saEntity.getBelongId()));
    }

    private Project getProjectBySa(Long contractId, IndContractsTypeEnum contractTypeEnum) {
        List<Project> list = projectService.qryProjectByConId(contractTypeEnum.getDictCode(), contractId);
        if (CollectionUtils.isEmpty(list) || list.size() != Constants.NUMBER_ONE) {
            // 无法录入挂接补充协议，项目为空或存在多个使用同一独立文件的项目
            throw new BusinessException(8010021);
        }
        Project project = list.get(Constants.NUMBER_ZERO);
        return project;
    }

    @Override
    public SupplementaryAgreementResp supplementaryAgreementDetail(Long id) {
        SupplementaryAgreementResp resp = supplementaryAgreementMapper.getSupplementaryAgreementDetail(id);
        if (Objects.isNull(resp)) {
            // 补充协议详情为空
            throw new BusinessException(8010029);
        }
        StringBuilder region = new StringBuilder();

        if (!Constants.PROJECT_BELONG.equals(resp.getProjectBelong())) {
            region.append(resp.getCountry());
        } else {
            region.append(Optional.ofNullable(resp.getProvince()).map(v -> v + Constants.REGION_CONNECTOR).orElse(""))
                    .append(Optional.ofNullable(resp.getCity()).map(v -> v + Constants.REGION_CONNECTOR).orElse(""))
                    .append(Optional.ofNullable(resp.getRegion()).orElse(""));
//            region.append(resp.getProvince()).append(Constants.REGION_CONNECTOR)
//                    .append(resp.getCity()).append(Constants.REGION_CONNECTOR)
//                    .append(resp.getRegion());
        }
        resp.setRegion(region.toString())
                .setStructuralStyle(Optional.ofNullable(resp.getStructuralStyle()).map(v -> v + Constants.MODE_CONNECTOR).orElse("") +
                        Optional.ofNullable(resp.getStructuralStyle2()).orElse(""))
//                .setStructuralStyle(resp.getStructuralStyle() + (StringUtils.isEmpty(resp.getStructuralStyle2()) ? ""
//                        : Constants.MODE_CONNECTOR + resp.getStructuralStyle2()))

                .setAddress(region.toString().replace(Constants.REGION_CONNECTOR, Constants.MODE_CONNECTOR)
                        + (StringUtils.isEmpty(resp.getAddress()) ? "" : Constants.MODE_CONNECTOR + resp.getAddress()))
                .setContractMode1(Optional.ofNullable(resp.getContractMode1()).map(v -> v + Constants.MODE_CONNECTOR).orElse("") +
                        Optional.ofNullable(resp.getContractMode2()).orElse(""))
//                .setContractMode1(resp.getContractMode1() + (StringUtils.isEmpty(resp.getContractMode2()) ? ""
//                        : Constants.MODE_CONNECTOR + resp.getContractMode2()))
                .setMarketProjectType(Optional.ofNullable(resp.getMarketProjectType()).map(v -> v + Constants.MODE_CONNECTOR).orElse("") +
                        Optional.ofNullable(resp.getMarketProjectType2()).orElse(""))
//                .setMarketProjectType(resp.getMarketProjectType() + (StringUtils.isEmpty(resp.getMarketProjectType2()) ? ""
//                        : Constants.MODE_CONNECTOR + resp.getMarketProjectType2()))
                .setProjectType(Optional.ofNullable(resp.getProjectType()).map(v -> v + Constants.MODE_CONNECTOR).orElse("")+
                        Optional.ofNullable(resp.getProjectType2()).map(v -> v + Constants.MODE_CONNECTOR).orElse("")+
                        Optional.ofNullable(resp.getProjectType3()).map(v -> v + Constants.MODE_CONNECTOR).orElse("")+
                        Optional.ofNullable(resp.getProjectType4()).orElse(""));
        return resp;
    }

    @Override
    public void updateProjectProgressStatus(IndContractsTypeEnum type, Project project, ProjectProgress preProgress, boolean mappingFlag) {
        log.info("=========补充协议立项更新项目进度=========");
        final Long projectId = project.getId();
        // 查询项目进度
        ProjectProgress progress = projectProgressService.selectProjectProgress(projectId);
        // 挂接到投标总结下面
        if (progress != null && type.getDictCode().equals(IndContractsTypeEnum.TENDER_SUMMARY.getDictCode())) {
            // 如果已经签约，则不再处理
            if (YesNoEnum.YES.getDictCode().equals(progress.getSignStatus())) {
                return;
            }
            // 设置签约时间
            progress.setSignTime(Instant.now().toEpochMilli()).setSignStatus(YesNoEnum.YES.getDictCode());
            // 设置警告状态
            progress.setWarnStatus(Constants.NUMBER_TWO.equals(progress.getApproveStatus()) ?
                                           WarningStatusEnum.DISMISSED.getDictCode() : WarningStatusEnum.NOT_TRIGGERED.getDictCode());
            // 设置备注
            progress.setCpmRemarks(mappingFlag ? "" : project.getYunshuExecuteUnit());
            // 设置创建命令状态
            progress.setCreateCommandStatus(preProgress.getCreateCommandStatus())
                    .setCreateCommandStartTime(preProgress.getCreateCommandStartTime())
                    .setCreateCommandEndTime(preProgress.getCreateCommandEndTime());
            // 更新项目进度
            projectProgressService.updateProjectProgress(progress);
        } else if (progress == null && type.getDictCode().equals(IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode())) {

            // 创建新的项目进度
            ProjectProgress projectProgress = new ProjectProgress();
            projectProgress.setProjectId(projectId);
            // 设置签约状态
            projectProgress.setSignStatus(YesNoEnum.YES.getDictCode()).setSignTime(Instant.now().toEpochMilli());
            // 设置警告状态
            projectProgress.setWarnStatus(WarningStatusEnum.NOT_TRIGGERED.getDictCode());
            // 设置系统备注
            projectProgress.setCpmRemarks(mappingFlag ? "" : project.getYunshuExecuteUnit());
            // 设置项目部状态
            projectProgress.setCreateCommandStatus(preProgress.getCreateCommandStatus())
                    .setCreateCommandStartTime(preProgress.getCreateCommandStartTime())
                    .setCreateCommandEndTime(preProgress.getCreateCommandEndTime())
                    // 设置工地状态
                    .setSmartApproveStatus(preProgress.getSmartApproveStatus())
                    .setApproveStatus(preProgress.getApproveStatus())
                    .setSmartQueryTime(preProgress.getSmartQueryTime());
            // 保存项目进度
            projectProgressService.saveProjectProgress(projectProgress);
        }
    }
}
