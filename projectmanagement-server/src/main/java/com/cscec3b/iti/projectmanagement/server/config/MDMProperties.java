package com.cscec3b.iti.projectmanagement.server.config;

import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@RefreshScope
@ConfigurationProperties(prefix = "cscec.project-approval")
@Configuration
public class ProApprovalProperties {

    /**
     * 需要排除立项的组织
     */
    private List<String> bidExcludeOrg;

    /**
     * 设计研究院 idPath
     */
    private String sjyOrgId;
}
