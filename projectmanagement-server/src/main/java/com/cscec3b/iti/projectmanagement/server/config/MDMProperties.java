package com.cscec3b.iti.projectmanagement.server.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * MDM配置属性
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
@NoArgsConstructor
@ToString
@RefreshScope
@ConfigurationProperties(prefix = "cscec.mdm")
@Configuration
public class MDMProperties {

    /**
     * MDM推送地址
     */
    private String push = "http://mdm-uat.cscec.com/gateway";

    /**
     * 是否启用MDM集成
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 60000;

    /**
     * 重试次数
     */
    private int retryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval = 2000;

    /**
     * 是否启用校验
     */
    private boolean validateEnabled = true;

    /**
     * 是否启用推送
     */
    private boolean pushEnabled = true;

    /**
     * 是否启用分发
     */
    private boolean distributeEnabled = true;

    /**
     * 校验接口URI
     */
    private String validateUri = "/esb-api/GCXMZSJ/GCXM_JS_ZJSJ_JY";

    /**
     * 推送接口URI
     */
    private String pushUri = "/esb-api/GCXMZSJ/GCXM_JS_ZJSJ";

    /**
     * 分发接口URI
     */
    private String distributeUri = "/esb-api/GCXMZSJ/GCXM_ESB_010048";

}
