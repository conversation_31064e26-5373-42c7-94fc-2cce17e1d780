package com.cscec3b.iti.projectmanagement.server.enums;

import lombok.Getter;

@Getter
public enum TaskEnum {
    
    /**
     * 待办
     */
    // 任务状态
    TODO(0, "todo", "待办"),
    /**
     * 在办
     */
    DOING(1, "doing", "在办"),
    /**
     * 已办
     */
    DONE(2, "done", "已办"),
    
    /**
     * 创建指挥部
     */
    // 任务类型
    CREATE_DIRECTORATE(1, "createDirectorate", "创建指挥部");

    TaskEnum(Integer code, String name, String msg) {
        this.code = code;
        this.name = name;
        this.msg = msg;
    }

    private final Integer code;
    private final String name;
    private final String msg;

}
