package com.cscec3b.iti.projectmanagement.server.pushservice.util;

import com.cscec3b.iti.model.resp.ProjectBindingSegment;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringProject;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectMapping;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import lombok.experimental.UtilityClass;

import java.time.Instant;

/**
 * 施工项目换绑事件数据构建工具类
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@UtilityClass
public class ProjectTransferEventBuilder {

    /**
     * 构建换绑事件数据
     *
     * @param project           施工项目
     * @param sourceEngineering 原工程项目
     * @param targetEngineering 目标工程项目
     * @param operatorId        操作人ID
     * @param operatorName      操作人姓名
     * @return 换绑事件数据
     */
    public static ProjectBindingSegment buildTransferData(Project project,
                                                          EngineeringProject sourceEngineering,
                                                          EngineeringProject targetEngineering
    ) {
        ProjectBindingSegment transferData = new ProjectBindingSegment();

        // 施工项目信息
        transferData.setProjectId(project.getId());
        transferData.setCpmProjectKey(project.getCpmProjectKey());
        transferData.setCpmProjectName(project.getCpmProjectName());

        // 原工程项目信息
        if (sourceEngineering != null) {
            transferData.setSourceEngineeringProjectId(sourceEngineering.getId());
            transferData.setSourceEngineeringKey(sourceEngineering.getEngineeringKey());
            transferData.setSourceEngineeringName(sourceEngineering.getEngineeringName());
        }

        // 目标工程项目信息
        if (targetEngineering != null) {
            transferData.setTargetEngineeringProjectId(targetEngineering.getId());
            transferData.setTargetEngineeringKey(targetEngineering.getEngineeringKey());
            transferData.setTargetEngineeringName(targetEngineering.getEngineeringName());
        }

        // 操作信息
        transferData.setTransferTimestamp(Instant.now().toEpochMilli());
        transferData.setOperatorId(LoginUserUtil.userId());
        transferData.setOperatorName(LoginUserUtil.userName());

        return transferData;
    }

    /**
     * 构建简化的换绑事件数据（仅包含ID信息）
     *
     * @param projectId                  施工项目ID
     * @param sourceEngineeringProjectId 原工程项目ID
     * @param targetEngineeringProjectId 目标工程项目ID
     * @return 换绑事件数据
     */
    public static ProjectBindingSegment buildSimpleTransferData(Long projectId,
                                                                Long sourceEngineeringProjectId,
                                                                Long targetEngineeringProjectId) {
        ProjectBindingSegment transferData = new ProjectBindingSegment();
        transferData.setProjectId(projectId);
        transferData.setSourceEngineeringProjectId(sourceEngineeringProjectId);
        transferData.setTargetEngineeringProjectId(targetEngineeringProjectId);
        transferData.setTransferTimestamp(Instant.now().toEpochMilli());
        return transferData;
    }

    /**
     * 构建解绑事件数据
     *
     * @param project            施工项目
     * @param engineeringProject 工程项目
     * @param mapping            关联关系
     * @return 解绑事件数据
     */
    public static ProjectBindingSegment buildUnbindingData(Project project,
                                                           EngineeringProject engineeringProject,
                                                           EngineeringStandardProjectMapping mapping) {
        ProjectBindingSegment transferData = new ProjectBindingSegment();

        // 施工项目信息
        if (project != null) {
            transferData.setProjectId(project.getId());
            transferData.setCpmProjectKey(project.getCpmProjectKey());
            transferData.setCpmProjectName(project.getCpmProjectName());
            transferData.setProjectFinanceCode(project.getProjectFinanceCode());
        }

        // 工程项目信息（作为原工程项目）
        if (engineeringProject != null) {
            transferData.setSourceEngineeringProjectId(engineeringProject.getId());
            transferData.setSourceEngineeringKey(engineeringProject.getEngineeringKey());
            transferData.setSourceEngineeringName(engineeringProject.getEngineeringName());
            transferData.setSourceEngineeringCode(engineeringProject.getEngineeringCode());
        }

        // 是否主施工项目
        if (mapping != null) {
            transferData.setMainStandardProject(mapping.getMain() != null && mapping.getMain() ? 1 : 0);
        }

        // 操作信息
        transferData.setTransferTimestamp(Instant.now().toEpochMilli());
        try {
            transferData.setOperatorId(LoginUserUtil.userCode());
            transferData.setOperatorName(LoginUserUtil.userName());
        } catch (Exception e) {
            // 忽略获取用户信息失败的情况
        }

        return transferData;
    }

    /**
     * 构建简化的解绑事件数据（仅包含基本信息）
     *
     * @param projectId            施工项目ID
     * @param cpmProjectKey        施工项目标识
     * @param cpmProjectName       施工项目名称
     * @param projectFinanceCode   财商项目编码
     * @param engineeringProjectId 工程项目ID
     * @param engineeringKey       工程项目标识
     * @param engineeringName      工程项目名称
     * @param engineeringCode      工程项目编码
     * @param isMainProject        是否主施工项目
     * @return 解绑事件数据
     */
    public static ProjectBindingSegment buildSimpleUnbindingData(Long projectId,
                                                                 String cpmProjectKey,
                                                                 String cpmProjectName,
                                                                 String projectFinanceCode,
                                                                 Long engineeringProjectId,
                                                                 String engineeringKey,
                                                                 String engineeringName,
                                                                 String engineeringCode,
                                                                 Boolean isMainProject) {
        ProjectBindingSegment transferData = new ProjectBindingSegment();

        // 施工项目信息
        transferData.setProjectId(projectId);
        transferData.setCpmProjectKey(cpmProjectKey);
        transferData.setCpmProjectName(cpmProjectName);
        transferData.setProjectFinanceCode(projectFinanceCode);

        // 工程项目信息（作为原工程项目）
        transferData.setSourceEngineeringProjectId(engineeringProjectId);
        transferData.setSourceEngineeringKey(engineeringKey);
        transferData.setSourceEngineeringName(engineeringName);
        transferData.setSourceEngineeringCode(engineeringCode);

        // 是否主施工项目
        transferData.setMainStandardProject(isMainProject != null && isMainProject ? 1 : 0);

        // 操作信息
        transferData.setTransferTimestamp(Instant.now().toEpochMilli());
        try {
            transferData.setOperatorId(LoginUserUtil.userCode());
            transferData.setOperatorName(LoginUserUtil.userName());
        } catch (Exception e) {
            // 忽略获取用户信息失败的情况
        }

        return transferData;
    }

    /**
     * 构建绑定事件数据
     *
     * @param project            施工项目
     * @param engineeringProject 工程项目
     * @param mapping            关联关系
     * @return 绑定事件数据
     */
    public static ProjectBindingSegment buildBindingData(Project project,
                                                         EngineeringProject engineeringProject,
                                                         EngineeringStandardProjectMapping mapping) {
        ProjectBindingSegment transferData = new ProjectBindingSegment();

        // 施工项目信息
        if (project != null) {
            transferData.setProjectId(project.getId());
            transferData.setCpmProjectKey(project.getCpmProjectKey());
            transferData.setCpmProjectName(project.getCpmProjectName());
            transferData.setProjectFinanceCode(project.getProjectFinanceCode());
        }

        // 工程项目信息（作为目标工程项目）
        if (engineeringProject != null) {
            transferData.setTargetEngineeringProjectId(engineeringProject.getId());
            transferData.setTargetEngineeringKey(engineeringProject.getEngineeringKey());
            transferData.setTargetEngineeringName(engineeringProject.getEngineeringName());
            transferData.setTargetEngineeringCode(engineeringProject.getEngineeringCode());
        }

        // 是否主施工项目
        if (mapping != null) {
            transferData.setMainStandardProject(mapping.getMain() != null && mapping.getMain() ? 1 : 0);
        }

        // 操作信息
        transferData.setTransferTimestamp(Instant.now().toEpochMilli());
        try {
            transferData.setOperatorId(LoginUserUtil.userCode());
            transferData.setOperatorName(LoginUserUtil.userName());
        } catch (Exception e) {
            // 忽略获取用户信息失败的情况
        }

        return transferData;
    }
}
