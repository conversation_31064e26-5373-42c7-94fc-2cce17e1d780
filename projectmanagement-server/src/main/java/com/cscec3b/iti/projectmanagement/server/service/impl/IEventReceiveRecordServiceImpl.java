package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommEnumDict;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.EventReceiveRecordReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.EventReceiveRecordResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventReceiveRecord;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventReceiveRecordMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;
import com.cscec3b.iti.projectmanagement.server.service.IEventReceiveRecordService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目事件服务层
 *
 * <AUTHOR>
 */
@Service
public class IEventReceiveRecordServiceImpl extends
    ServiceImpl<ProjectEventReceiveRecordMapper, ProjectEventReceiveRecord> implements IEventReceiveRecordService {

    @Resource
    private ProjectEventReceiveRecordMapper eventReceiveRecordMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private IYunshuOrgSyncService yunshuOrgSyncService;

    @Override
    public int insert(ProjectEventReceiveRecord record) {
        return eventReceiveRecordMapper.insert(record);
    }


    @Override
    public ProjectEventReceiveRecord selectByPrimaryKey(Long id) {
        return eventReceiveRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public Page<EventReceiveRecordResp> getRecordPages(final EventReceiveRecordReq receiveRecordReq) {
        final Integer current = receiveRecordReq.getCurrent();
        Integer size = receiveRecordReq.getSize();
        if (size > 100) {
            size = 100;
        }
        // 因前端组件问题，无法传递executeUnitIdPath，此处用treeId转换
        final String reqTreeId = receiveRecordReq.getTreeId();
        String executeUnitIdPath = receiveRecordReq.getExecuteUnitIdPath();
        if (StringUtils.isBlank(executeUnitIdPath) && StringUtils.isNotBlank(reqTreeId)) {
            final ExecuteUnitTreeDto reqUnitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheById(reqTreeId);
            executeUnitIdPath = Optional.ofNullable(reqUnitTreeDto).map(ExecuteUnitTreeDto::getIdPath).orElse(null);
            receiveRecordReq.setExecuteUnitIdPath(executeUnitIdPath);
        }

        final com.github.pagehelper.Page<EventReceiveRecordResp> pageInfo =
                PageHelper.startPage(current, size).doSelectPage(() -> eventReceiveRecordMapper.getRecordPages(receiveRecordReq));
        final List<EventReceiveRecordResp> recordRespList = pageInfo.getResult();
        if (CollectionUtils.isNotEmpty(recordRespList)) {
            recordRespList.forEach(recordResp -> {
                final String[] treeIds = recordResp.getYunshuExecuteUnitIdPath().split("#");
                final String treeId = treeIds[treeIds.length - 1];
                final ExecuteUnitTreeDto unitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheById(treeId);
                Optional.ofNullable(unitTreeDto).ifPresent(unit -> {
                    recordResp.setYunshuExecuteUnitFullPathName(unit.getIdPathName());
                    recordResp.setYunshuExecuteUnitFullPathAbbreviation(unit.getIdPathAbbreviation());
                });
            });
        }
        return new Page<EventReceiveRecordResp>(pageInfo.getTotal(), pageInfo.getPageNum(), size)
                .setRecords(recordRespList).setCurrent(current).setSize(size).setTotal(pageInfo.getTotal());
    }


    /**
     * 获取工地推送事件类型
     *
     * @return {@link CommEnumDict}
     */
    @Override
    public List<CommEnumDict> getSmartSiteNoticeEnum() {
        return Arrays.stream(ProjectEventEnum.values()).filter(event -> !event.getDictCode().startsWith("UD"))
                .map(eventEnum -> new CommEnumDict(eventEnum.getDictCode(),
                eventEnum.getZhCN(), eventEnum.getEnUS())).collect(Collectors.toList());
    }
}
