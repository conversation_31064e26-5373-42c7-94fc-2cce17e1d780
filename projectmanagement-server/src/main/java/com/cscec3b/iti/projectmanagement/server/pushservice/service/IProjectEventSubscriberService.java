package com.cscec3b.iti.projectmanagement.server.pushservice.service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.CreateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersListReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.UpdateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowNodeListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberPageResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectFlowEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;

import java.util.List;

/**
 * 事件订阅者配置服务
 *
 * <AUTHOR>
 * @description IProjectEventSubscriberService
 * @date 2023/04/19 09:32
 */
public interface IProjectEventSubscriberService {

    /**
     * 分页查询订阅配置
     *
     * @param subscribersReq 订阅者请求
     * @return {@link Page}<{@link SubscriberPageResp}>
     */
    Page<SubscriberPageResp> getSubscriberPages(QuerySubscribersReq subscribersReq);

    /**
     * 创建订阅者
     *
     * @param subscriberReq 订阅者请求
     * @return {@link Boolean}
     */
    Boolean createSubscriber(CreateSubscriberReq subscriberReq);

    /**
     * 更新订阅配置
     *
     * @param subscriberReq 订阅者请求
     * @return {@link Boolean}
     */
    Boolean updateSubscriber(UpdateSubscriberReq subscriberReq);

    /**
     * 更改订阅配置的状态
     *
     * @param subscriberReq 订阅者请求
     * @return {@link Boolean}
     */
    Boolean changeSubscriberStatus(UpdateSubscriberReq subscriberReq);

    List<ProjectFlowEventSubscribeDto> getFlowEventSubscribers(FlowNodeEnum nodeEnum, FlowNodeHandlerEnum handlerEnum);

    List<ProjectFlowEventSubscribeDto> getFlowEventSubscribers(FlowNodeEnum nodeEnum,
            FlowNodeHandlerEnum handlerEnum, FlowNodeDataTypeEnum dataTypeEnum, String customerId);

    /**
     * 通过ID获取信息
     *
     * @param subscribeId 订阅者id
     * @return {@link ProjectFlowEventSubscribe }
     * <AUTHOR>
     * @date 2023/09/18
     */
    ProjectEventSubscribe getById(Long subscribeId);

    List<ProjectFlowNodeListResp> getFlowNodeEnumInfo();
    List<SubscriberListResp> getSubscriberList(QuerySubscribersListReq subscribersReq);

    List<ProjectEventSubscribe> getList();
}
