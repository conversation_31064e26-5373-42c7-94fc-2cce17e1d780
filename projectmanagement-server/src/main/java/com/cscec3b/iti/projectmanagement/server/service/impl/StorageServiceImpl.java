package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.comm.ResponseMessage;
import com.aliyun.oss.internal.OSSHeaders;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.DeleteObjectsRequest;
import com.aliyun.oss.model.GenericResult;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.StorageClass;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.OSSFileInfo;
import com.cscec3b.iti.projectmanagement.server.config.OSSProperties;
import com.cscec3b.iti.projectmanagement.server.config.OssProgressListener;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.constant.OSSConstants;
import com.cscec3b.iti.projectmanagement.server.enums.OSSPathTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.AttachmentMapper;
import com.cscec3b.iti.projectmanagement.server.service.IStorageService;

import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2022-12-2022/12/30 15:09
 */


@Slf4j
@Service
@Import(OSSProperties.class)
public class StorageServiceImpl implements IStorageService {
    
    /**
     * header
     */
    public static final String INLINE = "inline";
    /**
     * MD5
     */
    public static final String MD5 = "MD5";
    private final OSS ossClient;

    private final OSSProperties properties;

    private final AttachmentMapper attachmentMapper;

    private final ApplicationEventPublisher publisher;


    public StorageServiceImpl(OSS ossClient, OSSProperties properties, AttachmentMapper attachmentMapper, ApplicationEventPublisher publisher) {
        this.ossClient = ossClient;
        this.properties = properties;
        this.attachmentMapper = attachmentMapper;
        this.publisher = publisher;
    }


    @Override
    public OSSFileInfo upload(MultipartFile file, int businessType) {
        if (file.isEmpty()) {
            throw new BusinessException(8010302);
        }
        String businessPath = OSSPathTypeEnum.getByCode(businessType).getName();
        String originalFilename = file.getOriginalFilename();
        String suffix = FileNameUtil.getSuffix(originalFilename);

        byte[] bytes;
        try {
            bytes = file.getBytes();
        } catch (IOException e) {
            throw new BusinessException(8010303);
        }

        String fileId = IdUtil.objectId();
        String md5 = getMd5(bytes);

        String fileName = fileId + Constants.REGION_CONNECTOR + suffix;
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern(OSSConstants.DATA_FORMAT));
        // 生成objectKey 即filePath
        String objectKey = properties.getBasePath() + businessPath + datePath + Constants.ID_PATH_CONNECTOR + fileName;
        String contentType = getContentType(file, suffix);
        long size = file.getSize();
        // 组装返回值
        OSSFileInfo ossFileInfo = new OSSFileInfo(objectKey, size, fileId, originalFilename, contentType, md5);

        ObjectMetadata metadata = getObjectMetadata(contentType, size, md5, originalFilename);

        //// 判断文件是否已上传
        //List<String> existObjectNames = doesFileExist(md5);
        //// 在库里存在，并且在oss上也存在才进行秒传
        //if (CollectionUtils.isNotEmpty(existObjectNames) && ossClient.doesObjectExist(properties.getBucketName(), existObjectNames.get(0))) {
        //    // 已上传过的文件,直接copy一份到目标文件夹下
        //    CopyObjectResult copyObjectResult = ossClient.copyObject(properties.getBucketName(), existObjectNames.get(0), properties.getBucketName(), objectKey);
        //    checkResponse(copyObjectResult);
        //    return ossFileInfo.setCreateAt(Instant.now().toEpochMilli()).setCompleted(true);
        //}

        // 创建PutObjectRequest对象
        PutObjectRequest putObjectRequest = new PutObjectRequest(properties.getBucketName(), objectKey, new ByteArrayInputStream(bytes), metadata);
        // 设置该属性可以返回response。如果不设置，则返回的response为空。
        putObjectRequest.setProcess("true");

        GenericResult ossPutResult;
        try {
            ossPutResult = ossClient.putObject(putObjectRequest);
        } catch (OSSException e) {
            log.error("OSS服务异常 ：{}", e.getMessage());
            throw new FrameworkException(-1, e.getMessage());
        } catch (ClientException e) {
            log.error("OSS客户端异常 ：{}", e.getMessage());
            throw new FrameworkException(-1, e.getMessage());
        }
        checkResponse(ossPutResult);
        return ossFileInfo.setCreateAt(Instant.now().toEpochMilli()).setCompleted(true);
    }

    @Override
    public OSSFileInfo uploadAsync(MultipartFile file, int businessType) {
        if (file.isEmpty()) {
            throw new BusinessException(8010302);
        }
        String businessPath = OSSPathTypeEnum.getByCode(businessType).getName();
        String originalFilename = file.getOriginalFilename();
        String suffix = FileNameUtil.getSuffix(originalFilename);

        byte[] bytes;
        try {
            bytes = file.getBytes();
        } catch (IOException e) {
            throw new BusinessException(8010303);
        }

        String fileId = IdUtil.objectId();
        String md5 = getMd5(bytes);

        String fileName = fileId + Constants.REGION_CONNECTOR + suffix;
        String datePath = LocalDate.now().format(DateTimeFormatter.ofPattern(OSSConstants.DATA_FORMAT));
        // 生成objectKey 即filePath
        String objectKey = properties.getBasePath() + businessPath + datePath + Constants.ID_PATH_CONNECTOR + fileName;
        String contentType = getContentType(file, suffix);
        long size = file.getSize();
        // 组装返回值
        OSSFileInfo ossFileInfo = new OSSFileInfo(objectKey, size, fileId, originalFilename, contentType, md5);

        ObjectMetadata metadata = getObjectMetadata(contentType, size, md5, originalFilename);

        // 判断文件是否已上传
        //List<String> existObjectNames = doesFileExist(md5);
        //// 在库里存在，并且在oss上也存在才进行秒传
        //if (CollectionUtils.isNotEmpty(existObjectNames) && ossClient.doesObjectExist(properties.getBucketName(), existObjectNames.get(0))) {
        //    // 已上传过的文件,直接copy一份到目标文件夹下
        //    CopyObjectResult copyObjectResult = ossClient.copyObject(properties.getBucketName(), existObjectNames.get(0), properties.getBucketName(), objectKey);
        //    checkResponse(copyObjectResult);
        //    return ossFileInfo.setCreateAt(Instant.now().toEpochMilli()).setCompleted(true);
        //}

        // 创建PutObjectRequest对象
        PutObjectRequest putObjectRequest = new PutObjectRequest(properties.getBucketName(), objectKey, new ByteArrayInputStream(bytes), metadata);
        // 设置该属性可以返回response。如果不设置，则返回的response为空。
        putObjectRequest.setProcess("true");

        //初始化进度条
        putObjectRequest.withProgressListener(new OssProgressListener(fileId, size, publisher));

        GenericResult ossPutResult;

        CompletableFuture.supplyAsync(() -> ossClient.putObject(putObjectRequest)).handleAsync((res, ex) -> {
            if (null != ex) {
                return ex;
            }
            return res;
        }).exceptionally(ex -> new FrameworkException(-1, ex.getMessage()));

        return ossFileInfo.setCreateAt(Instant.now().toEpochMilli());
    }

    /**
     * 检查上传结果
     *
     * @param ossPutResult
     */
    private static void checkResponse(GenericResult ossPutResult) {
        // 上传失败
        ResponseMessage response = ossPutResult.getResponse();
        // 失败抛出异常
        if (!response.isSuccessful()) {
            String errorResponseAsString = response.getErrorResponseAsString();
            log.error("文件上传失败 ：{}", errorResponseAsString);
            throw new FrameworkException(-1, errorResponseAsString);
        }
    }

    /**
     * 判断文件是否存在
     *
     * @param md5 文件md5
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023/01/09 15:46
     */
    private List<String> doesFileExist(String md5) {
        return attachmentMapper.getFilePathByMd5(md5);
    }

    /**
     * 获取文件Md5值
     *
     * @param bytes
     * @return
     */
    private static String getMd5(byte[] bytes) {
        MessageDigest md;
        try {
            md = MessageDigest.getInstance(MD5); // NOSONAR
            byte[] b = md.digest(bytes);
            return Base64.encodeBase64String(b);
        } catch (NoSuchAlgorithmException e) {
            throw new BusinessException(1);
        }


    }

    /**
     * 生成 ObjectMetadata
     *
     * @param contentType      文件contentType
     * @param contentLength    文件大小
     * @param originalFilename 原始文件名
     * @return com.aliyun.oss.model.ObjectMetadata
     * <AUTHOR>
     * @date 2023/01/06 15:55
     */
    private static ObjectMetadata getObjectMetadata(String contentType, long contentLength, String md5, String originalFilename) {
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(contentType);
        metadata.setContentLength(contentLength);
        metadata.setCacheControl("no-cache");
        // 设置MD5 保证文件完整性
        metadata.setContentMD5(md5);
        // 设置上传对象的 Acl 为公共读
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        // 设置存储类型和访问权限，请参考以下示例代码。
        metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
        // 设置直接预览文件内容
        metadata.setContentDisposition(INLINE);
        // 下载时显示为原文件名
        try {
            String encodeName = URLEncoder.encode(originalFilename, CharsetUtil.UTF_8);
            metadata.setContentDisposition("inline;filename*=utf-8'zh_cn'" + encodeName);
        } catch (UnsupportedEncodingException e) {
            throw new BusinessException(1);
        }

        return metadata;
    }


    @Override
    public Boolean deleteFile(List<String> objectKeys) {
        DeleteObjectsRequest deleteObjectsRequest = new DeleteObjectsRequest(properties.getBucketName()).withKeys(objectKeys);
        ossClient.deleteObjects(deleteObjectsRequest);
        return Boolean.TRUE;
    }

    private String getContentType(MultipartFile file, String suffix) {
        List<String> viewSuffixes = properties.getViewSuffix();
        if (CollectionUtils.isNotEmpty(viewSuffixes) && viewSuffixes.contains(suffix)) {
            return file.getContentType();
        }
        return MediaType.APPLICATION_OCTET_STREAM_VALUE;
    }
}
