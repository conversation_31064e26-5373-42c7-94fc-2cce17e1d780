package com.cscec3b.iti.projectmanagement.server.util;

import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;
import com.cscec3b.iti.projectmanagement.server.config.UserContextHolder;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Optional;

/**
 * 用户信息工具类
 *
 * <AUTHOR>
 * @date 2022/10/18 16:04:11
 */
public class LoginUserUtil {

    private LoginUserUtil() {
    }

    private static final StringRedisTemplate STRING_REDIS_TEMPLATE;

    static {
        STRING_REDIS_TEMPLATE = SpringUtils.getBean(StringRedisTemplate.class);
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    public static UserInfo userInfo() {
        return UserContextHolder.getUser();
    }

    /**
     * 获取当前用户id
     *
     * @return 用户id
     */
    public static String userId() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getUserCode).orElse("admin");
    }

    /**
     * 获取当前用户id
     *
     * @return 用户id
     */
    public static String userIdOrNull() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getUserCode).orElse(null);
    }

    /**
     * 获取用户名
     *
     * @return 用户名
     */
    public static String userName() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getUserName).orElse(null);
    }

    /**
     * user code  对应老UC userName
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/10/30
     */
    public static String userCode() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getUserCode).orElse("admin");

    }


    /**
     * 获取当前用户所在标准组织下的外部组织id(云枢)
     *
     * @return getExternalId
     */
    public static String getTreeId() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getOrgInfo).map(YunshuOrgSyncResp::getId).orElse(null);
    }

    /**
     * 获取当前登录用户的组织id
     * 当前用户的实体组织
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/10/25
     */
    public static String orgId() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getOrgInfo).map(YunshuOrgSyncResp::getDeptId).orElse(null);
    }

    /**
     * 获取当前登陆用户的部门信息，为实际所在部门，有可能是非实体
     *
     * @return {@link String }
     */
    public static String deptId() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getOrgId).orElse(null);
    }

    /**
     * 获取当前登录用户的组织信息
     *
     * @return {@link YunshuOrgSyncResp}
     */
    public static YunshuOrgSyncResp userOrgInfo() {
        return Optional.ofNullable(userInfo()).map(UserInfo::getOrgInfo).orElseThrow(() -> new BusinessException(8010102));
    }

    /**
     * 获取当前登录用户的组织idPath
     *
     * @return {@link String}
     */
    public static String queryCode() {
        return Optional.ofNullable(userOrgInfo()).map(YunshuOrgSyncResp::getIdPath)
                .orElseThrow(() -> new BusinessException(8010102));
    }
}
