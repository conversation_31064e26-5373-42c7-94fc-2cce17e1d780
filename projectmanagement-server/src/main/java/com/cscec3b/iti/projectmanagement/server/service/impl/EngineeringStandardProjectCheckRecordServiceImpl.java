package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.HookDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.ProjectDetailOutResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.ProjectHookInfo;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.ProjectInfo;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectCheckRecord;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringStandardProjectMapping;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringStandardProjectCheckRecordMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringStandardProjectMappingMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteSyncProjectInfoMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IEventCallbackService;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectCheckRecordService;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectMappingService;
import com.cscec3b.iti.tg.common.base.json.JsonUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EngineeringStandardProjectCheckRecordServiceImpl
    extends ServiceImpl<EngineeringStandardProjectCheckRecordMapper, EngineeringStandardProjectCheckRecord>
    implements EngineeringStandardProjectCheckRecordService {

    // public EngineeringStandardProjectCheckRecordServiceImpl() {
    // // 初始化系统标识与二进制位的映射关系 (从左往右: 工程, 财商, 商务, 供应链)
    // // map Key值为 消费系统的系统标识 appCode (如: 工程：SmartSite, 财商：Finance, 商务:Bms, 供应链:Scm)
    // systemBitMap.put("工程", 3); // 1000 (第 3 位)
    // systemBitMap.put("财商", 2); // 0100 (第 2 位)
    // systemBitMap.put("商务", 1); // 0010 (第 1 位)
    // systemBitMap.put("供应链", 0); // 0001 (第 0 位)
    // }

    private final IEventCallbackService eventCallbackService;

    private final SmartsiteSyncProjectInfoMapper smartsiteSyncProjectInfoMapper;

    private final EngineeringStandardProjectMappingMapper espmMapper;
    private final ProjectMapper projectMapper;

    @Override
    public EngineeringStandardProjectCheckRecord checkStandardProjectInSmartSite(
        EngineeringStandardProjectMapping standardProjectMapping, Project project, ProjectEventSubscribe subscribe) {
        final EngineeringStandardProjectCheckRecord projectCheckRecord =
            buildCheckRecord(standardProjectMapping.getId(), subscribe);

        try {
            final ProjectDetailOutResp detailOutResp =
                eventCallbackService.getProjectInitInfo2Update(project.getCpmProjectKey());
            detailOutResp.setProjectRelyInfo(null).setProjectBasicInfo(null).setProjectContractInfo(null)
                .setProjectAgreementInfo(null).setProjectContactInfo(null);
            projectCheckRecord.setStatus(true);
            projectCheckRecord.setResponseInfo(JsonUtils.toJsonStr(detailOutResp));
            this.saveOrUpdate(projectCheckRecord);
            this.checkSmartSiteHookProject(project, standardProjectMapping.getEngineeringProjectId(), detailOutResp);
        } catch (Exception e) {
            final String message = e.getMessage();
            projectCheckRecord.setStatus(false);
            projectCheckRecord.setResponseInfo(JsonUtils.toJsonStr(message));
            log.warn("获取工地挂接项目失败: {}", message);
        }
        return projectCheckRecord;
    }

    /**
     *
     * 检查 Smart Site Hook 项目 检查 当前项目是工程是否挂接，需要将关联的项目添加进来 <br>
     * 1. 挂接项目 projectHookInfo 不为空 ，hookNum hookdtoList 不为空 <Br>
     * 2. 主项目：projectHookInfo 为空，hookNum hookdtoList 不为空 <br>
     * 3. 独立项目：projectHookInfo 为空，hookNum hookdtoList 为空
     * 
     * @param project 项目
     * @param engineeringProjectId 工程项目 ID
     */
    private void checkSmartSiteHookProject(Project project, Long engineeringProjectId,
        ProjectDetailOutResp detailOutResp) {
        // 检查当前项目是否挂接项目，如果是挂接，则要将关联项目添加进来
        // 检查是否挂接项目有2个场景：1. 通过主项目找子项目，2. 通过子项目找父项目
        final ProjectInfo projectInfo = detailOutResp.getProjectInfo();
        final ProjectHookInfo projectHookInfo = detailOutResp.getProjectHookInfo();
        final List<HookDto> hookDTOList = detailOutResp.getHookDTOList();
        final Integer hookNum = detailOutResp.getHookNum();

        // 20250326 由于工地历史项目挂接数据没有hookInfo信息，所以以下全部以 hookNum 和 hookDTOList 为准
        log.info("checkSmartSiteHookProject projectInfo: {}, projectHookInfo: {}, hookDTOList: {}, hookNum: {}",
            JsonUtils.toJsonStr(projectInfo), JsonUtils.toJsonStr(projectHookInfo), JsonUtils.toJsonStr(hookDTOList),
            hookNum);
        if (ObjectUtils.allNotNull(hookNum, hookDTOList) && hookNum > 0) {
            // 子项目的主项目信息为projectInfo
            if (ObjectUtils.allNotNull(projectInfo, projectInfo.getStandardProjectId())) {
                final long smartSiteMainProjectId = Long.parseLong(projectInfo.getStandardProjectId());
                final List<Long> projectIds =
                    hookDTOList.stream().map(HookDto::getStandardProjectId).filter(StringUtils::isNotBlank)
                        .filter(standardProjectId -> !Objects.equals(standardProjectId, project.getId().toString()))
                        .map(Long::valueOf).collect(Collectors.toList());
                filterAndSaveEngineerMapping(engineeringProjectId, projectIds);
            }
        }

    }

    private void filterAndSaveEngineerMapping(Long engineeringProjectId, List<Long> projectIds) {
        // 过滤出未关联的id
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }
        final List<Long> hasMappingProjectIds = this.espmMapper
            .selectList(Wrappers.<EngineeringStandardProjectMapping>lambdaQuery()
                .in(EngineeringStandardProjectMapping::getStandardProjectId, projectIds)
                .ne(EngineeringStandardProjectMapping::getEngineeringProjectId, engineeringProjectId))
            .stream().map(EngineeringStandardProjectMapping::getStandardProjectId).collect(Collectors.toList());
        projectIds.removeAll(hasMappingProjectIds);
        // 挂接到当前工程项目
        // 生成关联表数据
        final List<EngineeringStandardProjectMapping> engineeringStandardProjectMappings = projectIds
            .stream().map(projectId -> EngineeringStandardProjectMapping.builder()
                .engineeringProjectId(engineeringProjectId).standardProjectId(projectId).main(false).build())
            .collect(Collectors.toList());
        final EngineeringStandardProjectMappingService mappingService =
            SpringUtils.getBean(EngineeringStandardProjectMappingService.class);
        if (Objects.isNull(mappingService)) {
            return;
        }
        // 挂接兄弟项目
        mappingService.saveOrUpdateBatch(engineeringStandardProjectMappings);
    }

    @Override
    public EngineeringStandardProjectCheckRecord checkStandardProjectInFinance(
        EngineeringStandardProjectMapping standardProjectMapping, Project project, ProjectEventSubscribe subscribe) {
        final EngineeringStandardProjectCheckRecord projectCheckRecord =
            buildCheckRecord(standardProjectMapping.getId(), subscribe);
        try {
            // todo 通过财商编码检查项目财商系统项目是否存在
            // 检查同一个财商下的其他项目，如果存在，则需要同步挂接到工程项目下
            final List<
                Long> projectIds =
                    projectMapper
                        .selectList(Wrappers.<Project>lambdaQuery()
                            .eq(Project::getProjectFinanceCode, project.getProjectFinanceCode())
                            .ne(Project::getId, project.getId()))
                        .stream().map(Project::getId).collect(Collectors.toList());
            log.info("通过财商编码检查项目财商系统项目是否存在，项目ids: {}", projectIds);
            // 挂接影子项目
            filterAndSaveEngineerMapping(standardProjectMapping.getEngineeringProjectId(), projectIds);
        } catch (Exception e) {
            log.warn("通过财商编码检查项目财商系统项目是否存在失败: {}", e.getMessage());
        }
        return projectCheckRecord;
    }

    private static EngineeringStandardProjectCheckRecord buildCheckRecord(Long engineeringStandardMappingId,
        ProjectEventSubscribe subscribe) {
        return EngineeringStandardProjectCheckRecord.builder()
            .engineeringStandardMappingId(engineeringStandardMappingId).appCode(subscribe.getAppCode())
            .lastCheckTime(System.currentTimeMillis()).status(false).build();
    }

    @Override
    public EngineeringStandardProjectCheckRecord checkStandardProjectInBms(
        EngineeringStandardProjectMapping standardProjectMapping,
        Project project, ProjectEventSubscribe subscribe) {
        final EngineeringStandardProjectCheckRecord projectCheckRecord =
            buildCheckRecord(standardProjectMapping.getId(), subscribe);
        return projectCheckRecord;
    }

    @Override
    public EngineeringStandardProjectCheckRecord checkStandardProjectInScm(
        EngineeringStandardProjectMapping standardProjectMapping,
        Project project, ProjectEventSubscribe subscribe) {
        final EngineeringStandardProjectCheckRecord projectCheckRecord =
            buildCheckRecord(standardProjectMapping.getId(), subscribe);
        return projectCheckRecord;
    }
}
