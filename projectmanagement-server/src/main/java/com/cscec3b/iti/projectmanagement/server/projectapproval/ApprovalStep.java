package com.cscec3b.iti.projectmanagement.server.projectapproval;

import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;

/**
 * 立项步骤
 *
 * <AUTHOR>
 * @date 2024/10/18
 */
public interface ApprovalStep {

    /**
     * 当前步骤编号
     *
     * @return {@link Integer }
     */
    Integer currentStepNo();


    /**
     * 执行当前步骤的逻辑
     *
     * @param bidApproval 中标未立项信息
     */
    void execute(BidApproval bidApproval);


    /**
     * 开始当前步骤
     *
     * @param bidApproval 中标未立项信息
     * @return {@link BidApproval }
     */
    BidApproval start(BidApproval bidApproval);

    /**
     * 运行自动任务
     *
     * @param bidApproval 中标未立项信息
     * @return {@link BidApproval }
     */
    BidApproval autoRunTask(BidApproval bidApproval);


    /**
     * 验证任务是否完成
     *
     * @param bidApproval 中标未立项信息
     * @return boolean
     */
    boolean isComplete(BidApproval bidApproval);

    /**
     * 提交当前步骤
     *
     * @param param 中标未立项信息
     * @return {@link BidApproval }
     */
    BidApproval submit(ApprovalStepReq param);


    /**
     * 保存草稿
     *
     * @param req 中标未立项信息
     */
    void saveDrafts(ApprovalStepReq req);
}
