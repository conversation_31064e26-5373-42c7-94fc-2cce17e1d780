package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.server.config.YunShuConfig;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.SyncUpdateOrgEvent;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.g3.G3OrgService;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrgDepartmentResp;
import com.g3.org.api.dto.resp.ExecuteGetOrganizationResp;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.g3.org.api.dto.resp.org.YunshuOrgTreeEntity;
import com.google.common.collect.Lists;
import com.odin.freyr.common.orika.BeanMapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.cache.annotation.CacheResult;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cscec3b.iti.projectmanagement.server.constant.Constants.YUNSHU_DEPT_INFO;
import static com.cscec3b.iti.projectmanagement.server.constant.Constants.YUNSHU_TREE_CURR_INFO;


/**
 * 组织相关服务
 *
 * <AUTHOR>
 * @date 2023/08/12 23:10
 **/

@Slf4j
@Service
@Validated
@Transactional(rollbackFor = Exception.class)
public class YunshuOrgServiceImpl implements IYunshuOrgService {

    /**
     * 线程池定义
     */
    private static final ForkJoinPool FORK_JOIN_POOL = new ForkJoinPool(4);
    /**
     * 批量数据量
     */
    private static final int BATCH_SIZE = 2000;

    // @Resource(name = "yunShuSmartsConstructionClient")
    // private CloudPivotClient yunShuClient;

    private final G3OrgService g3OrgService;

    private final YunShuConfig yunShuConfig;

    private final Executor cpmTaskExecutor;

    private final IYunshuOrgSyncService yunshuOrgSyncService;

    private final RedisTemplate<String, Object> redisTemplate;

    private final ApplicationEventPublisher pushlisher;


    public YunshuOrgServiceImpl(YunShuConfig yunShuConfig, Executor cpmTaskExecutor,
            IYunshuOrgSyncService yunshuOrgSyncService, RedisTemplate<String, Object> redisTemplate,
            ApplicationEventPublisher pushlisher, G3OrgService g3OrgService) {
        this.yunShuConfig = yunShuConfig;
        this.cpmTaskExecutor = cpmTaskExecutor;
        this.yunshuOrgSyncService = yunshuOrgSyncService;
        this.redisTemplate = redisTemplate;
        this.pushlisher = pushlisher;
        this.g3OrgService = g3OrgService;
    }

    /**
     * 钻取组织树
     *
     * @param parentId   父id
     * @param isEntities 是否实体
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    public List<ExecuteUnitTreeResp> getOrgTree(String parentId, boolean isEntities) {
        // 为空则从当前用户节点获取组织树
        if (StringUtils.isBlank(parentId)) {
            parentId = LoginUserUtil.getTreeId();
            if (StringUtils.isBlank(parentId)) {
                throw new BusinessException(8010102);
            }
            // 根节点直接获取当前节点信息
            final ExecuteUnitTreeDto resp = yunshuOrgSyncService.getUnitTreeDtoCacheById(parentId);
            // (ExecuteUnitTreeDto) redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO).get(parentId);
            return Lists.newArrayList(BeanMapUtils.map(resp, ExecuteUnitTreeResp.class));
        }
        // 获取子节点
        return yunshuOrgSyncService.getChildAndFilter(parentId, null, null, isEntities);
    }

    /**
     * 钻取组织树（修订执行单位） parentId为空时，则取精益建造根节点
     *
     * @param parentId 根节点id
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    @CacheResult
    public List<ExecuteUnitTreeResp> getOrgTreeForRevision(String parentId, boolean isEntities) {
        if (StringUtils.isBlank(parentId)) {
            parentId = getRootTreeIdFromYunshu();
        }
        return yunshuOrgSyncService.getChildAndFilter(parentId, null, null, isEntities);
    }

    /**
     * 模糊搜索组织
     *
     * @param parentTreeId 父id
     * @param name         名字
     * @param abbreviation 缩写
     * @param isEntities   是否实体
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @Override
    @CacheResult
    public List<ExecuteUnitTreeResp> fuzzySearchOrg(String parentTreeId, String name, String abbreviation,
            boolean isEntities) {
        final List<ExecuteUnitTreeResp> dtoList = yunshuOrgSyncService.getAllChildAndFilter(parentTreeId, name,
                abbreviation, isEntities);

        return listToTree(dtoList);
    }

    /**
     * 列表转树，非根节点不能丢失与根节点进行平铺
     *
     * @param list 列表
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/23
     */
    private List<ExecuteUnitTreeResp> listToTree(List<ExecuteUnitTreeResp> list) {
        final Map<String, ExecuteUnitTreeResp> nodeMap =
                list.stream().collect(Collectors.toMap(ExecuteUnitTreeDto::getId, o -> o, (o1, o2) -> o2));

        List<ExecuteUnitTreeResp> rootNodes = new ArrayList<>();
        for (ExecuteUnitTreeResp node : list) {
            ExecuteUnitTreeResp parent = nodeMap.get(node.getParentId());
            if (parent == null) {
                rootNodes.add(node);
            } else {
                parent.getChildren().add(node);
            }
        }
        return rootNodes;
    }

    /**
     * 从云枢获取组织树
     *
     * @param orgId       org id
     * @param isEntityOrg 是实体机构
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public List<YunshuOrgDepartmentTreeModel> getOrgTreeFromYunshu(String orgId, boolean isEntityOrg) {
        log.debug("获取云枢组织信息，id:{}", orgId);
        // orgId 如果为空，则从根组织下取值
        if (StringUtils.isBlank(orgId)) {
            orgId = getRootTreeIdFromYunshu();
        }
        // orgId不为空则取下级部门
        final ExecuteGetChildDepartmentsResp childDepartmentResp = g3OrgService.executeGetChildDepartments(orgId);
        checkResponseEntity(childDepartmentResp);
        final List<YunshuOrgDepartmentTreeModel> departments = childDepartmentResp.getData();
        if (isEntityOrg) {
            return departments.stream().filter(o -> yunShuConfig.getEntityOrg().contains(o.getOrgCategory()))
                    .collect(Collectors.toList());
        }
        return departments;
    }

    /**
     * 从云枢获取根节点树id
     *
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/08/22
     */
    @PmReTry
    private String getRootTreeIdFromYunshu() {
        final ExecuteGetOrganizationResp organizationResponse = g3OrgService.executeGetOrganization();
        checkResponseEntity(organizationResponse);
        // 查询详情
        final YunshuOrgTreeEntity entity = organizationResponse.getData();
        if (ObjectUtil.isNull(entity)) {
            throw new BusinessException(-1);
        }
        return entity.getId();
    }

    /**
     * 从云枢获取部门信息
     *
     * @param departmentId 部门id
     * @return {@link YunshuOrgDepartmentEntity }
     * <AUTHOR>
     * @date 2023/10/30
     */
    @Override
    @PmReTry
    public YunshuOrgDepartmentEntity getDepartmentFromYunshu(String departmentId) {
        if (StringUtils.isBlank(departmentId)) {
            return null;
        }
        final ExecuteGetDepartmentResp orgDepartmentResponse = g3OrgService.executeGetDepartment(departmentId);
        checkResponseEntity(orgDepartmentResponse);
        return orgDepartmentResponse.getData();

    }

    /**
     * 检查响应
     *
     * @param response 响应
     * <AUTHOR>
     * @date 2023/10/30
     */
    private void checkResponseEntity(CloudPivotResponse response) {
        if (ObjectUtils.allNotNull(response, response.getErrcode()) && !response.getErrcode().equals(0L)) {
            log.error("响应失败:{}", response);
            throw new FrameworkException(80107000, JsonUtils.toJsonStr(response));
        }
    }

    /**
     * 缓存yunshuorg
     *
     * @param parentTreeId 父id
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Lock(lockKey = "cacheYunshuOrgTree", leaseTime = 30 * 60 * 1000L)
    public void cacheYunshuOrgTree(String parentTreeId) {

        // 初始化缓存operation
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations =
                redisTemplate.boundHashOps(YUNSHU_TREE_CURR_INFO);
        final BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations =
                redisTemplate.boundHashOps(YUNSHU_DEPT_INFO);

        final String parentId = StringUtils.isBlank(parentTreeId) ? getRootTreeIdFromYunshu() : parentTreeId;

        final String syncMark = IdUtil.objectId();
        log.info("云枢同步标识：{}, time: {}", parentId, Instant.now().toEpochMilli());
        // 获取根节点 并从根节点缓存所有组织
        CompletableFuture.runAsync(() -> {
                    log.debug("begin to sync yunshu org");
                    ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap = new ConcurrentHashMap<>(8);
                    cacheYunshuOrg(parentId, tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark);
                    log.debug("sync yunshu org end");
                }, cpmTaskExecutor)
                // 移除未同步数据
                .thenRun(() -> {
                    log.debug("remove unSync data");
                    // 先查询是同步数据是否写入成功
                    if (yunshuOrgSyncService.getCountBySyncMark(syncMark) > 0) {
                        // 支持按层级删除
                        final ExecuteUnitTreeDto unitTreeDto = treeBoundHashOperations.get(parentId);
                        String deleteIdPath = ObjectUtils.isEmpty(unitTreeDto) ? null : unitTreeDto.getIdPath();
                        yunshuOrgSyncService.deleteUnSynchronizedData(deleteIdPath, syncMark);
                    }
                });
        // 设置缓存23小时过期(同步组织大概在30分钟左右)
        treeBoundHashOperations.expire(23, TimeUnit.HOURS);
        deptBoundHashOperations.expire(23, TimeUnit.HOURS);
    }


    /**
     * 缓存yunshu组织
     *
     * @param treeId                  树id
     * @param tempMap                 缓存Map
     * @param treeBoundHashOperations treeBoundHashOperations
     * @param syncMark                同步标志
     * <AUTHOR>
     * @date 2023/08/24
     */
    private void cacheYunshuOrg(String treeId, ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations,
            String syncMark) {
        // 获取子节点
        final List<YunshuOrgDepartmentTreeModel> orgTree = getOrgTreeFromYunshu(treeId, false);
        if (CollectionUtils.isNotEmpty(orgTree)) {
            log.info("开始缓存{}的子节点:{}条", treeId, orgTree.size());
            // 递归缓存
            cacheAsync(orgTree, tempMap, treeBoundHashOperations, deptBoundHashOperations, syncMark);
        }

        List<ExecuteUnitTreeDto> arrays = new ArrayList<>(tempMap.values());
        // 重新缓存

        // 分批保存到数据表
        while (!arrays.isEmpty()) {
            int endIndex = Math.min(BATCH_SIZE, arrays.size());
            List<ExecuteUnitTreeDto> needInsert = new ArrayList<>(arrays.subList(0, endIndex));
            yunshuOrgSyncService.batchInsertOrUpdate(needInsert);
            arrays.removeAll(needInsert);
        }
    }

    /**
     * 异步缓存云枢组织
     *
     * @param orgTree                 云枢部门树
     * @param tempMap                 临时Map缓存
     * @param treeBoundHashOperations redisOperations
     * @param deptBoundHashOperations
     * @param syncMark                同步标志
     * <AUTHOR>
     * @date 2023/10/30
     */
    private void cacheAsync(List<YunshuOrgDepartmentTreeModel> orgTree,
            ConcurrentHashMap<String, ExecuteUnitTreeDto> tempMap,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> treeBoundHashOperations,
            BoundHashOperations<String, String, ExecuteUnitTreeDto> deptBoundHashOperations, String syncMark) {
        ConcurrentLinkedQueue<YunshuOrgDepartmentTreeModel> treeModels = new ConcurrentLinkedQueue<>();
        ConcurrentHashMap<String, ExecuteUnitTreeDto> treeCacheMap = new ConcurrentHashMap<>(8);
        ConcurrentHashMap<String, ExecuteUnitTreeDto> deptCacheMap = new ConcurrentHashMap<>(8);
        // 异步任务列表
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (YunshuOrgDepartmentTreeModel org : orgTree) {
            // 开启异步线程
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                YunshuOrgDepartmentEntity department = null;
                try {
                    // 尝试从Yunshu获取部门信息
                    department = getDepartmentFromYunshu(org.getDepartmentId());
                } catch (Exception e) {
                    log.error("从Yunshu获取部门信息失败, deptId: {}", org.getDepartmentId(), e);
                    // 发生错误，跳过此部门
                }
                final String fullName =
                        department != null ? department.getOrgFullName() : org.getDepartmentName();
                final String abbreviation = department != null ? department.getOrgShortName() : org.getDepartmentName();
                final ExecuteUnitTreeDto unitDto = new ExecuteUnitTreeDto();
                final String queryCode = org.getQueryCode();
                final int level = CharSequenceUtil.count(queryCode, "#");
                unitDto.setId(org.getId()).setName(fullName).setDeptId(org.getDepartmentId())
                        .setAbbreviation(abbreviation).setIdPath(queryCode).setLevel(level).setSyncMark(syncMark)
                        .setCode(org.getDepartmentCode()).setOrgType(org.getOrgCategory())
                        .setParentId(org.getTreeParentId()).setDeptSort(org.getDeptSort());

                final ExecuteUnitTreeDto parentTreeDto = tempMap.get(org.getTreeParentId());
                StringBuilder idPathName = new StringBuilder(unitDto.getName());
                StringBuilder idPathAbbreviation = new StringBuilder(unitDto.getAbbreviation());
                if (parentTreeDto != null) {
                    idPathName.insert(0, parentTreeDto.getIdPathName()).insert(parentTreeDto.getIdPathName().length(),
                            Constants.ID_PATH_CONNECTOR);
                    idPathAbbreviation.insert(0, parentTreeDto.getIdPathAbbreviation())
                            .insert(parentTreeDto.getIdPathAbbreviation().length(), Constants.ID_PATH_CONNECTOR);
                }
                unitDto.setIdPathName(idPathName.toString()).setIdPathAbbreviation(idPathAbbreviation.toString());

                List<YunshuOrgDepartmentTreeModel> orgTreeFromYunshu = new ArrayList<>();
                try {
                    // 尝试从Yunshu获取组织树信息
                    orgTreeFromYunshu = getOrgTreeFromYunshu(org.getId(), false);

                } catch (Exception e) {
                    log.error("从Yunshu获取组织树信息失败, orgId: {}", org.getId(), e);
                    // 发生错误，跳过此组织树
                }
                // 如果没有子节点，则设置为叶子节点
                if (CollectionUtils.isEmpty(orgTreeFromYunshu)) {
                    unitDto.setLeaf(Boolean.TRUE);
                }
                // 暂存
                tempMap.put(org.getId(), unitDto);
                // 以treeId为Key 缓存
                treeCacheMap.put(org.getId(), unitDto);
                // 以deptId 为Key 缓存
                deptCacheMap.put(org.getDepartmentId(), unitDto);
                treeModels.addAll(orgTreeFromYunshu);
                // 注意此处线程数量，太多会导致外部系统异常，数据丢失
            }, FORK_JOIN_POOL);
            // 加入异步任务列表
            futures.add(future);
        }
        // 等待所有线程完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        // 每处理完一层级的数据，就缓存一次，保证下级组织能获取上级组织信息
        // 同时去更新
        if (!treeCacheMap.isEmpty()) {
            treeBoundHashOperations.putAll(treeCacheMap);
            deptBoundHashOperations.putAll(deptCacheMap);
            // 更新项目表数据
            final Collection<ExecuteUnitTreeDto> yunshuDtos = treeCacheMap.values();
            // 更新业务表中的数据
            pushlisher.publishEvent(new SyncUpdateOrgEvent(this, new ArrayList<>(yunshuDtos)));
        }

        if (!treeModels.isEmpty()) {
            cacheAsync(new ArrayList<>(treeModels), tempMap, treeBoundHashOperations, deptBoundHashOperations,
                    syncMark);
        }
    }



    /**
     * 获取实体组织
     *
     * @param deptId
     * @return {@link ExecuteUnitTreeDto }
     * <AUTHOR>
     * @date 2023/10/29
     */
    @Override
    public ExecuteUnitTreeDto getEntityOrgByDeptId(String deptId) {
        String treeId = null;
        String newDeptId = null;
        final ExecuteUnitTreeDto executeUnitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheByDeptId(deptId);
        if (Objects.isNull(executeUnitTreeDto)) {
            final YunshuOrgSync yunshuOrgSync = yunshuOrgSyncService.getYunshuOrgByDeptId(deptId);
            if (Objects.isNull(yunshuOrgSync)) {
                final ExecuteGetDepartmentResp executeGetDepartmentResp = g3OrgService.executeGetDepartment(deptId);
                checkResponseEntity(executeGetDepartmentResp);
                final YunshuOrgDepartmentEntity deptInfo = executeGetDepartmentResp.getData();
                if (Objects.isNull(deptInfo)) {
                    throw new BusinessException(8010105);
                }
                treeId = deptInfo.getTreeId();
            } else {
                treeId = yunshuOrgSync.getId();
            }
        } else {
            treeId = executeUnitTreeDto.getId();
        }

        return this.getEntityOrgByTreeId(treeId);
    }

    @Override
    public ExecuteUnitTreeDto getEntityOrgByTreeId(String treeId) {
        ExecuteUnitTreeDto executeUnitTreeDto = yunshuOrgSyncService.getUnitTreeDtoCacheById(treeId);
        if (Objects.isNull(executeUnitTreeDto)) {
            YunshuOrgSync orgSync = yunshuOrgSyncService.selectById(treeId);
            if (ObjectUtils.isEmpty(orgSync)) {
                // 通过G3Service获取组织信息
                final ExecuteGetOrgDepartmentResp orgDepartmentResp = g3OrgService.executeGetOrgDepartment(treeId);
                checkResponseEntity(orgDepartmentResp);
                final YunshuOrgDepartmentTreeModel treeModel = orgDepartmentResp.getData();
                if (ObjectUtils.isEmpty(treeModel)) {
                    log.error("通过G3Service获取组织信息失败, treeId: {}", treeId);
                    throw new BusinessException(8010105);
                }
                executeUnitTreeDto =
                        new ExecuteUnitTreeDto().setId(treeModel.getId()).setName(treeModel.getDepartmentName())
                                .setDeptId(treeModel.getDepartmentId()).setAbbreviation(treeModel.getSourceDepartmentName())
                                .setIdPath(treeModel.getQueryCode()).setCode(treeModel.getDepartmentCode())
                                .setOrgType(treeModel.getOrgCategory()).setParentId(treeModel.getTreeParentId())
                                .setDeptSort(treeModel.getDeptSort());
            } else {
                executeUnitTreeDto = BeanUtil.copyProperties(orgSync, ExecuteUnitTreeDto.class);
            }
        }
        final Integer orgType = executeUnitTreeDto.getOrgType();

        // 实体则返回
        if (yunShuConfig.getEntityOrg().contains(orgType)) {
            return executeUnitTreeDto;
        }
        return getEntityOrgByTreeId(executeUnitTreeDto.getParentId());
    }

    @Override
    public List<ExecuteUnitTreeResp> getOrgTreeRevisionForRealTime(String parentId, boolean isEntities) {
        final List<YunshuOrgDepartmentTreeModel> orgTreeFromYunshu = getOrgTreeFromYunshu(parentId, isEntities);
        return orgTreeFromYunshu.stream().parallel().map(org -> {
            final ExecuteUnitTreeResp unitDto = new ExecuteUnitTreeResp();
            final String queryCode = org.getQueryCode();
            final int level = CharSequenceUtil.count(queryCode, "#");
            unitDto.setId(org.getId()).setName(org.getDepartmentName()).setDeptId(org.getDepartmentId())
                    .setAbbreviation(org.getSourceDepartmentName()).setIdPath(queryCode).setLevel(level)
                    .setCode(org.getDepartmentCode()).setOrgType(org.getOrgCategory())
                    .setParentId(org.getTreeParentId()).setDeptSort(org.getDeptSort()).setOrgType(org.getOrgCategory());
            return unitDto;

        }).collect(Collectors.toList());
    }


    /**
     * 通过中标未立项的组织id查询智慧工地的组织树Id
     *
     * @param parentOrgId 上级组织id
     * @return {@link List}<{@link ExecuteUnitTreeResp}>
     */
    public String getSmartOrgTreeId(String parentOrgId) {
        final ExecuteGetDepartmentResp orgDepartmentResponse = g3OrgService.executeGetDepartment(parentOrgId);
        checkResponseEntity(orgDepartmentResponse);
        final YunshuOrgDepartmentEntity entity = orgDepartmentResponse.getData();
        return Optional.ofNullable(entity).map(YunshuOrgDepartmentEntity::getTreeId)
                .orElseThrow(() -> new BusinessException(8010105));
    }

}
