package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * yunshu org同步映射器
 *
 * <AUTHOR>
 * @date 2023/08/22
 */
@Mapper
public interface YunshuOrgSyncMapper extends BaseMapper<YunshuOrgSync> {
    /**
     * insert dto to table
     * @param dto the dto
     * @return insert count
     */
    int insert(ExecuteUnitTreeDto dto);

    /**
     * 插入或更新
     *
     * @param dto 记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/22
     */
    int insertOrUpdate(ExecuteUnitTreeDto dto);

    /**
     * 插入或更新选择性
     *
     * @param orgSync 记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/22
     */
    int insertOrUpdateSelective(YunshuOrgSync orgSync);

    /**
     * 删除未同步数据
     *
     * @param idPath  idPath
     * @param syncMark     同步标志
     * <AUTHOR>
     * @date 2023/08/25
     */
    void deleteUnSynchronizedData(@Param("idPath") String idPath, @Param("syncMark") String syncMark);

    /**
     * 获取所有子节点并过滤
     *
     * @param idPath       id路径
     * @param name         名字
     * @param abbreviation 缩写
     * @param entityOrg    实体机构
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/22
     */
    List<ExecuteUnitTreeResp> getAllChildAndFilter(@Param("idPath") String idPath, @Param("name") String name,
            @Param("abbreviation") String abbreviation, @Param("entityOrg") List<Integer> entityOrg);

    /**
     * 获取子节点并过滤
     *
     * @param parentId       id路径
     * @param name         名字
     * @param abbreviation 缩写
     * @param entityOrg    实体机构
     * @return {@link List }<{@link ExecuteUnitTreeResp }>
     * <AUTHOR>
     * @date 2023/08/23
     */
    List<ExecuteUnitTreeResp> getChildAndFilter(@Param("parentId") String parentId, @Param("name") String name,
            @Param("abbreviation") String abbreviation, @Param("entityOrg") List<Integer> entityOrg);

    /**
     * 查询通过id
     *
     * @param id id
     * @return {@link YunshuOrgSync }
     * <AUTHOR>
     * @date 2023/08/23
     */
    YunshuOrgSync selectById(@Param("id") String id);


    /**
     * 设置是否叶子节点
     *
     * @param entities 实体组织列表
     * <AUTHOR>
     * @date 2023/08/23
     */
    void setIsLeaf(@Param("entities") List<Integer> entities);

    /**
     * 批量插入或更新
     *
     * @param needInsert 需要插入的列表
     * <AUTHOR>
     * @date 2023/08/24
     */
    void batchInsertOrUpdate(@Param("list") List<ExecuteUnitTreeDto> needInsert);

    /**
     * 通过部门Id获取组织信息
     *
     * @param deptId 部门id
     * @return {@link YunshuOrgSync }
     * <AUTHOR>
     * @date 2023/10/29
     */
    YunshuOrgSync getByDeptId(@Param("deptId") String deptId);

    /**
     * 获取当前同步的数量syncmark
     *
     * @param syncMark
     * @return int
     * <AUTHOR>
     * @date 2023/11/01
     */
    int getCountBySyncMark(@Param("syncMark") String syncMark);

    /**
     * 更新是否叶子节点
     */
    void updateIsLeaf();
}
