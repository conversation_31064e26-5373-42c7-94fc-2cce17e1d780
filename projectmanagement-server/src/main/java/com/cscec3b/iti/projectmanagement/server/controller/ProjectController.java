package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.projectmanagement.api.IProjectApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectEffectPictureUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ErrorProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectLatAndLngUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateFinanceReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateUnitExecuteReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateYunshuOrgIdReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.CreateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.QuerySpecialProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.UpdateSpecialProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.UpdateYzwProjectIdReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ManualPushEventFlowNodeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectResp;
import com.cscec3b.iti.projectmanagement.server.enums.RevisionTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ManualPushEventFlowNode;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.ProjectEventListener;
import com.cscec3b.iti.projectmanagement.server.service.BureauContractService;
import com.cscec3b.iti.projectmanagement.server.service.BureauSupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.service.IProjectRevisionRecordService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SpecialProjectService;
import com.cscec3b.iti.projectmanagement.server.service.SupplementaryAgreementService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目控制类
 */
@RestController
@RequestMapping(IProjectApi.PATH)
@Api(tags = "项目")
@Slf4j
@Validated
public class ProjectController implements IProjectApi {

    @Resource
    private ProjectService projectService;

    @Resource
    private BureauContractService bureauContractService;

    @Resource
    private BureauSupplementaryAgreementService bureauSupplementaryAgreementService;

    @Resource
    private SupplementaryAgreementService supplementaryAgreementService;

    @Resource
    private SpecialProjectService specialProjectService;

    @Resource
    private IProjectRevisionRecordService revisionRecordService;

    @Resource
    private ProjectEventListener projectEventListener;

    /**
     * 立项详情查询
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<ProjectResp> detail(Long id) {
        return new GenericityResponse<>(projectService.detail(id));
    }

    /**
     * 带分页项目列表查询
     * @param queryParams 查询参数
     * @return
     */
    @Override
    public GenericityResponse<Page<ProjectResp>> page(ProjectQueryParams queryParams) {
        return new GenericityResponse<>(projectService.pageListByCondition(queryParams));
    }

    /**
     * 项目更新
     * @param projectUpdateReq 项目更新要求
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> update(ProjectUpdateReq projectUpdateReq) {
        return new GenericityResponse<>(projectService.updateProjectInner(projectUpdateReq));
    }

    /**
     * 项目详情
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<ProjectDetailResp> display(Long id) {
        return new GenericityResponse<>(projectService.getProjectInfo(id));
    }

    /**
     * 局内分包合同详情
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<BureauContractResp> bureauContractDetail(Long id) {
        return new GenericityResponse<>(bureauContractService.get(id));
    }

    /**
     * 合同/补充协议关系信息查询
     * @param independentContractId   独立合同id
     * @param independentContractType 独立合同类型
     * @return
     */
    @Override
    public GenericityResponse<ContractualRelationshipResp> contractRelationList(Long independentContractId,
                                                                                Integer independentContractType) {
        return new GenericityResponse<>(
                projectService.contractRelationList(independentContractId, independentContractType));
    }

    /**
     * 投标总结关系信息查询
     * @param independentContractId   独立合同id
     * @param independentContractType 独立合同类型
     * @return
     */
    @Override
    public GenericityResponse<ContractualRelationshipResp> tenderRelationList(Long independentContractId,
                                                                              Integer independentContractType) {
        return new GenericityResponse<>(
                projectService.tenderRelationList(independentContractId, independentContractType));
    }

    /**
     * 合同定案详情查询
     * @param contractId id
     * @return
     */
    @Override
    public GenericityResponse<ContractDetailResp> contractDetail(Long contractId) {
        return new GenericityResponse<>(projectService.contractDetail(contractId));
    }

    /**
     * 投标总结详情查询
     * @param contractId id
     * @return
     */
    @Override
    public GenericityResponse<BidSummaryResp> tenderDetail(Long contractId) {
        return new GenericityResponse<>(projectService.tenderDetail(contractId));
    }

    /**
     * 局内补充协议详情
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<BureauSupplementaryAgreementResp> bureauSupplementaryAgreementDetail(Long id) {
        return new GenericityResponse<>(bureauSupplementaryAgreementService.get(id));
    }

    /**
     * 保存立项任务
     * @param projectReq 项目申请
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> saveProjectTask(ProjectReq projectReq) {
        return new GenericityResponse<>(projectService.saveProjectTask(projectReq));
    }

    /**
     * 提交立项任务
     * @param projectReq 项目申请
     * @return
     */
    @Override
    public GenericityResponse<Boolean> submitProjectTask(ProjectReq projectReq) {
        return new GenericityResponse<>(projectService.submitProjectTask(projectReq));
    }

    /**
     * 补充协议详情
     * @param id id
     * @return
     */
    @Override
    public GenericityResponse<SupplementaryAgreementResp> supplementAgreementDetail(Long id) {
        return new GenericityResponse<>(supplementaryAgreementService.supplementaryAgreementDetail(id));
    }

//    /**
//     * 创建指挥部更新项目信息API
//     * @param projectId            id
//     * @param createCommandDeptReq 创建命令部门
//     * @return
//     */
//    @Override
//    @Logger
//    public GenericityResponse<CreatedRes> createCommandDeptByUc(Long projectId, CreateCommandDeptReq
//    createCommandDeptReq) {
//        return new GenericityResponse<>(projectService.createCommandDeptByUc(projectId,createCommandDeptReq));
//    }

//    /**
//     * 查询指挥部详情
//     * @param code 部门编码
//     * @return
//     */
//    @Override
//    public GenericityResponse<CommandDeptInfoRes> getCommandDeptInfo(String code) {
//        return new GenericityResponse<>(projectService.getCommandDeptInfo(code));
//    }

//    /**
//     * 查询项目部详情
//     * @param code 编码
//     * @return
//     */
//    @Override
//    public GenericityResponse<ProjectDeptInfoRes> getProjectDeptInfo(String code) {
//        return new GenericityResponse<>(projectService.getProjectDeptInfo(code));
//    }

//    /**
//     * 分域模糊搜索组织
//     * @param code         代码
//     * @param name         名字
//     * @param abbreviation 缩写
//     * @return
//     */
//    @Override
//    public GenericityResponse<List<OrgNodeResp>> fuzzySearchOrg(String code, String name, String abbreviation) {
//        return new GenericityResponse<>(projectService.searchOrgByNameDomain(code, name, abbreviation));
//    }

    /**
     * 钻取查询组织
     * @param code 代码
     * @return
     */
//    @Override
//    public GenericityResponse<List<OrgNodeResp>> orgTreeNode(String code) {
//        return new GenericityResponse<>(projectService.getNodeListByParentCode(code));
//    }

//    /**
//     * 获取非标映射的标准组织根节点
//     * @return
//     */
//    @Override
//    public GenericityResponse<OrgNodeResp> orgRoot() {
//        return new GenericityResponse<>(projectService.getRootNode());
//    }

    /**
     * 编辑特殊立项API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateSpecialProject(UpdateSpecialProjectReq request) {
        return new GenericityResponse<>(specialProjectService.update(request));
    }
    /**
     * 特殊立项详情API
     *
     * @param id id
     * @return {@link GenericityResponse }<{@link SpecialProjectDetailResp }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<SpecialProjectDetailResp> getSpecialProjectDetail(Long id) {
        return new GenericityResponse<>(specialProjectService.getDetail(id));
    }

    /**
     * 特殊立项列表分页查询
     *
     * @param params 参数个数
     * @return {@link GenericityResponse }<{@link Page }<{@link SpecialProjectResp }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Page<SpecialProjectResp>> specialProjectPage(QuerySpecialProjectParams params) {
        return new GenericityResponse<>(specialProjectService.specialProjectList(params));
    }

    /**
     * 新建特殊立项API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> create(CreateSpecialProjectReq request) {
        return new GenericityResponse<>(specialProjectService.create(request));
    }

    // /**
    //  * 返回整个组织树
    //  *
    //  * @return {@link GenericityResponse }<{@link TreeNodeBO[] }>
    //  * <AUTHOR>
    //  * @date 2023/08/21
    //  */
    // @Override
    // public GenericityResponse<TreeNodeBO[]> getAllTree() {
    //     return new GenericityResponse<>(projectService.getAllTree());
    // }
    //
    //
    // /**
    //  * 根据departmentId返回名称
    //  *
    //  * @param departmentId 部门id
    //  * @return {@link GenericityResponse }<{@link String }>
    //  * <AUTHOR>
    //  * @date 2023/08/21
    //  */
    // @Override
    // public GenericityResponse<String> getNameByDepartmentId(String departmentId) {
    //     return new GenericityResponse<>(projectService.getNameByDepartmentId(departmentId));
    // }

    /**
     * 修订云枢id
     * @param projectUpdateReq 项目更新要求
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateYunshuOrgId(@Validated @RequestBody ProjectUpdateYunshuOrgIdReq projectUpdateReq){
        return new GenericityResponse<>(projectService.updateYunshuOrgId(projectUpdateReq,
                RevisionTypeEnum.YUNSHU_ORG_ID, null));
    }

    @Override
    public GenericityResponse<Boolean> additionYunshuOrgId(final ProjectUpdateYunshuOrgIdReq projectAdditionsReq) {
        return new GenericityResponse<>(projectService.additionYunshuOrgId(projectAdditionsReq,
                RevisionTypeEnum.CREATE_ORG_IN_UC));
    }

    /**
     * 项目云枢更新记录
     *
     * @param current            当前
     * @param size               大小
     * @param projectId          项目id
     * @param revisionType       修改类型
     * @param projectFinanceCode 项目财商编码
     * @param projectFinanceName 项目财商名称
     * @param originalValue      原始值
     * @param revisedValue       修改后值
     * @param userId             用户id
     * @param username           用户名
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @return {@link GenericityResponse}<{@link Page}<{@link RevisionRecordResp}>>
     */
    @Override
    public GenericityResponse<Page<RevisionRecordResp>> yunshuOrgRevisionRecord(int current, int size, Long projectId,
        Integer revisionType, String projectFinanceCode, String projectFinanceName, String originalValue,
        String revisedValue, String userId, String username, Long startTime, Long endTime, String remark) {
        return ResponseBuilder.fromData(revisionRecordService.getRecord(current, size, projectId, revisionType,
            projectFinanceCode, projectFinanceName, originalValue, revisedValue, userId, username, startTime, endTime
                , remark));
    }


    /**
     * 修订财商信息
     *
     * @param financeReq 财商相关参数
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateFinance(ProjectUpdateFinanceReq financeReq) {
        return ResponseBuilder.fromData(projectService.updateFinance(financeReq));
    }


    /**
     * 更新执行单位
     * Xinfa
     * 2023/08/21
     *
     * @param executeReq 执行要求
     * @return {@link GenericityResponse }<{@link Boolean }>
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateExecuteUnit(ProjectUpdateUnitExecuteReq executeReq) {
        return ResponseBuilder.fromData(projectService.updateExecuteUnit(executeReq));
    }

    /**
     * 云枢组织-带分页项目列表查询
     * @param queryParams
     * @return
     */
    @Override
    public GenericityResponse<Page<ProjectResp>> pageCloudPivot(ProjectQueryParams queryParams) {
        return new GenericityResponse<>(projectService.pageListByConditionCloudPivot(queryParams));
    }

    /**
     * 云枢组织-特殊立项列表分页查询
     * @param params 参数个数
     * @return
     */
    @Override
    public GenericityResponse<Page<SpecialProjectResp>> specialProjectPageCloudPivot(QuerySpecialProjectParams params) {
        return new GenericityResponse<>(specialProjectService.specialProjectListCloudPivot(params));
    }

    /**
     * @param req 云筑网编码
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateYZW(UpdateYzwProjectIdReq req) {
        return ResponseBuilder.fromData(projectService.updateYZW(req));
    }

    /**
     * 手动推送事件信息-获取业务板块信息
     *
     * @return {@link GenericityResponse}<{@link List}<{@link ManualPushEventFlowNodeResp}>>
     */
    @Override
    public GenericityResponse<List<ManualPushEventFlowNodeResp>> manualPushEventFlowNode() {
        return ResponseBuilder.fromData(Arrays.stream(ManualPushEventFlowNode.values())
                .map(val -> new ManualPushEventFlowNodeResp().setName(val.getName()).setCode(val.getCode())
                        .setType(val.getType()).setFiled(val.getFiled()).setDescription(val.getDescription()))
                .collect(Collectors.toList()));
    }

    /**
     * 提前生成事件推送数据
     *
     * @param projectId    项目id
     * @param type 类型
     * @param flowNodeCode 项目流转节点
     * @param subscriberId 业务系统id
     * @return {@link GenericityResponse}<{@link ProjectArchiveResp}>
     */
    @Override
    public GenericityResponse<ProjectArchiveResp> generateEventPushDataInAdvance(final Long projectId,
            String type, final String flowNodeCode, final Long subscriberId) {
        return ResponseBuilder.fromData(projectEventListener.generateEventPushDataInAdvance(projectId, type,
                flowNodeCode, subscriberId));
    }

    /**
     * 手动推送数据
     *
     * @param projectId    项目id
     * @param type  类型
     * @param flowNodeCode 项目流转节点
     * @param subscriberId 业务系统id
     * @return {@link GenericityResponse}<{@link ProjectArchiveResp}>
     */
    @Override
    public GenericityResponse<String> manualPushEvent(final Long projectId, String type, final String flowNodeCode,
                                                      final Long subscriberId) throws InterruptedException {
        return ResponseBuilder.fromData(projectEventListener.manualPushEvent(projectId, type, flowNodeCode,
                subscriberId));
    }

    /**
     * 更新智慧工地板块信息信息
     *
     * @param req 请求
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    public GenericityResponse<Boolean> updateLatitudeAndLongitude(final ProjectLatAndLngUpdateReq req) {
        return ResponseBuilder.fromData(projectService.updateLatitudeAndLongitude(req));
    }

    @Override
    public GenericityResponse<ProjectStatusEnumDataResp> getProjectStatusEnumData() {

        return ResponseBuilder.fromData(projectService.getProjectStatusEnumData());
    }

    @Override
    public GenericityResponse<Boolean> updateEffectPicture(final ProjectEffectPictureUpdateReq req) {
        return ResponseBuilder.fromData(projectService.updateEffectPicture(req));
    }

    @Override
    public GenericityResponse<List<ContractFileRelationResp>> contractRelation(Integer independentContractType,
            Long independentContractId) {
        return ResponseBuilder.fromData(projectService.projectContractRelationList(independentContractType,
                independentContractId));
    }

    @Override
    public GenericityResponse<ProjectAmountCategoryResp> getAmountCategory(Long projectId) {
        return ResponseBuilder.fromData(projectService.getAmountCategory(projectId));
    }

    /**
     * 导出项目列表
     *
     * @param response    response
     * @param queryParams 查询参数
     */
    @Override
    public void exportProjectList(HttpServletResponse response, ProjectQueryParams queryParams) {
        projectService.exportProjectPage(response, queryParams);
    }

    @Override
    @Logger
    public GenericityResponse<Boolean> delete(Long id) {
        return ResponseBuilder.fromData(projectService.deleteProjectById(id));
    }

    @Override
    public GenericityResponse<Page<ErrorProjectResp>> getProjectErrorPageList(ErrorProjectPageReq req) {
        return ResponseBuilder.fromData(projectService.getProjectErrorPageList(req));
    }

    @Override
    public GenericityResponse<Boolean> deleteErrorProject(List<Long> ids) {
        return ResponseBuilder.fromData(projectService.deleteErrorProject(ids));
    }

    @Override
    public GenericityResponse<Boolean> syncFinanceMdm(Long projectId) {
        return ResponseBuilder.fromData(projectService.syncFinanceMdm(projectId));
    }
}
