package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgCodeSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgCodeUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;
import com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfig;

public interface PendingTaskMsgConfigService extends IService<TaskMsgConfig> {

    /**
     * 新增配置
     *
     * @param saveReq 保存请求
     * @return {@link Boolean }
     */
    <PERSON><PERSON><PERSON> create(PendingTaskMsgCodeSaveReq saveReq);

    /**
     * 更新信息
     *
     * @param updateReq 更新请求
     * @return {@link Boolean }
     */
    Boolean updateInfo(PendingTaskMsgCodeUpdateReq updateReq);

    /**
     * 页面列表
     *
     * @param req 要求
     * @return {@link Page }<{@link PendingTaskMsgConfigResp }>
     */
    Page<PendingTaskMsgConfigResp> pageList(PendingTaskMsgReq req);
}
