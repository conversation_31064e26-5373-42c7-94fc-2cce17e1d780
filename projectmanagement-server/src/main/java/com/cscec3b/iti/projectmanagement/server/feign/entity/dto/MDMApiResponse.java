package com.cscec3b.iti.projectmanagement.server.feign.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * MDM API响应结果
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "MDM API响应结果")
public class MDMApiResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码：0:成功；1：失败
     */
    @ApiModelProperty(value = "状态码：0:成功；1：失败")
    private String code;

    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String message;

    /**
     * 数据编码（工程项目生成编码）
     */
    @ApiModelProperty(value = "数据编码（工程项目生成编码）")
    private String mdmCode;

    /**
     * 消息ID
     */
    @ApiModelProperty(value = "消息ID")
    private String msgId;

    /**
     * 消息内容（分发接口使用）
     */
    @ApiModelProperty(value = "消息内容（分发接口使用）")
    private String msg;

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "0".equals(code);
    }

    /**
     * 判断是否失败
     */
    public boolean isFailed() {
        return "1".equals(code);
    }
}
