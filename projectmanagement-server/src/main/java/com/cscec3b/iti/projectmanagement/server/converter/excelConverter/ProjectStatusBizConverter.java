package com.cscec3b.iti.projectmanagement.server.converter.excelConverter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectStatusBizEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

@Slf4j
public class ProjectStatusBizConverter implements Converter<String> {
    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        log.info("projectStatusBizConverter convertToExcelData value:  {}, filedName: {}", value,
                contentProperty.getField().getName());
        if (Objects.nonNull(value)) {
            final ProjectStatusBizEnum bizEnum = ProjectStatusBizEnum.getZhCNByCode(value);

            return new WriteCellData<>(null == bizEnum ? value : bizEnum.getZhCN());
        }
        return null;
    }
}
