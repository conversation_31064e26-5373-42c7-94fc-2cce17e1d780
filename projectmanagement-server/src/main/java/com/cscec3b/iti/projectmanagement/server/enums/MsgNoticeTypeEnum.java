package com.cscec3b.iti.projectmanagement.server.enums;

import java.util.Arrays;

import com.cscec3b.iti.common.base.dictionary.IDataDictionary;

import lombok.Getter;

/**
 * msg notice 类型枚举
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
public enum MsgNoticeTypeEnum implements IDataDictionary {
    /**
     * Ehr
     */
    EHR(100, "ehr", "人资系统-项目人员调配", "EHR_NOTICE"),
    /**
     * 设计研究院-项目部创建
     */
    SJY(101, "sjy", "设计研究院-项目部创建", "SJY_NOTICE");

    final Integer dictCode;

    @Getter
    final String code;

    final String zhCN;

    final String enUS;

    MsgNoticeTypeEnum(Integer dictCode, String code, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.code = code;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    /**
     * 按编码获取枚举
     *
     * @param code 法典
     * @return {@link MsgNoticeTypeEnum }
     */
    public MsgNoticeTypeEnum getByCode(String code) {
        return Arrays.stream(MsgNoticeTypeEnum.values()).filter(e -> e.code.equals(code)).findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid code: " + code));

    }

    @Override
    public Integer getDictCode() {
        return dictCode;
    }

    @Override
    public String getZhCN() {
        return zhCN;
    }

    @Override
    public String getEnUS() {
        return enUS;
    }

    @Override
    public String getDesc() {
        return "消息配置通知类型";
    }

}
