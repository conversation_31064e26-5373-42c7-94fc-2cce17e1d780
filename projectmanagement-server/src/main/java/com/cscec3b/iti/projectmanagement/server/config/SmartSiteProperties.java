package com.cscec3b.iti.projectmanagement.server.config;

import com.cscec3b.iti.projectmanagement.server.entity.Notice;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import lombok.Data;

/**
 * @description 智慧工地接口配置
 * <AUTHOR>
 * @date 2022/11/25
 */
@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "cscec.api.smart-site")
public class SmartSiteProperties {

    /**
     * 智慧工地host
     */
    private String host;

    /**
     * 服务名称
     */
    private String serviceName = "/thirdParty";

    /**
     * 智慧工地接口地址
     */
    private String projectUrl;

    /**
     * 智慧工地立项成功查询url
     */
    private String initProjectCallbackUrl;

    /**
     * appKey
     */
    private String appKey;

    /**
     * secretKey
     */
    private String secretKey;

    /**
     * 项目信息地址
     */
    private String endPoint;

    /**
     * 分组(环境)
     */
    private String group;

    /**
     * 智慧工也施工参数配置
     */
    private String engineerParameter;

    /**
     * 通知
     */
    private TaskNotice taskNotice;

    /**
     * 组合url
     *
     * @return host + serviceName
     */
    public String reqUrl() {
        return getHost() + getServiceName();
    }

    @Data
    public static class TaskNotice {
        private String webLink;
        private String appLink;
        private String msgConfigCode;
        private String retryCycle;
    }

}
