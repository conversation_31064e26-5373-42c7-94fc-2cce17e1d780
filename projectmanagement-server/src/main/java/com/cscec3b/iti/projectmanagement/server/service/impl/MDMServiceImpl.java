package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.feign.MDMOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.feign.entity.MdmPushEntity;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMEngineProjectPushDto;
import com.cscec3b.iti.projectmanagement.server.service.MDMService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.UUID;

/**
 * MDM集成服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MDMServiceImpl implements MDMService {

    private final MDMOpenApiFeign mdmOpenApiFeign;
    private final ProjectService projectService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat TIMESTAMP_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    @Override
    public MDMApiResponse validateEngineProject(Project project) {
        log.info("开始校验工程项目数据，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.validateEngineProject(pushDto);
            log.info("校验工程项目数据完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("校验工程项目数据失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("校验工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse pushEngineProject(Project project) {
        log.info("开始推送工程项目数据到MDM，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.pushEngineProject(pushDto);
            log.info("推送工程项目数据到MDM完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("推送工程项目数据到MDM失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("推送工程项目数据到MDM失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse distributeEngineProject(Project project) {
        log.info("开始分发工程项目数据，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.distributeEngineProject(pushDto);
            log.info("分发工程项目数据完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("分发工程项目数据失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("分发工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse validateEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return validateEngineProject(project);
    }

    @Override
    public MDMApiResponse pushEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return pushEngineProject(project);
    }

    @Override
    public MDMApiResponse distributeEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return distributeEngineProject(project);
    }

    /**
     * 构建MDM推送数据
     *
     * @param project 项目实体
     * @return MDM推送DTO
     */
    private MDMEngineProjectPushDto buildPushDto(Project project) {
        MDMEngineProjectPushDto pushDto = new MDMEngineProjectPushDto();
        pushDto.setMsgId(generateMsgId());
        pushDto.setDataType("Engproject");
        
        MdmPushEntity entity = buildMdmPushEntity(project);
        pushDto.setData(Collections.singletonList(entity));
        
        return pushDto;
    }

    /**
     * 构建MDM推送实体
     *
     * @param project 项目实体
     * @return MDM推送实体
     */
    private MdmPushEntity buildMdmPushEntity(Project project) {
        MdmPushEntity entity = new MdmPushEntity();
        
        // 基本信息
        entity.setCode(project.getCpmProjectKey()); // 项目唯一标识作为编码
        entity.setName(project.getCpmProjectName());
        entity.setStname(project.getCpmProjectAbbreviation());
        entity.setProjType("01"); // 默认值
        
        // 地理信息
        entity.setContinent("CN"); // 默认中国
        entity.setOffRegion(project.getRegionCode());
        entity.setIsInternal("1"); // 境内
        
        // 项目属性
        entity.setOwnerType(project.getProjectContractorType());
        entity.setType(project.getProjectCategory());
        entity.setAInsParentCode(project.getProjectDeptId());
        entity.setIsAssType(project.getFabricated());
        entity.setConType(project.getContractMode());
        
        // 日期信息
        if (project.getWorkerBeginTime() != null) {
            entity.setPlanStartDate(formatDate(project.getWorkerBeginTime()));
        }
        if (project.getWorkerEndTime() != null) {
            entity.setPlanEndDate(formatDate(project.getWorkerEndTime()));
        }
        if (project.getContractStartDate() != null) {
            entity.setContStartDate(formatDate(project.getContractStartDate()));
        }
        if (project.getContractEndDate() != null) {
            entity.setActualConstrCompltDate(formatDate(project.getContractEndDate()));
        }
        
        // 项目状态和其他信息
        entity.setBuiProStatus(getProjectStatus(project));
        entity.setCustomCode(project.getCustomCode());
        entity.setProjectAddress(project.getSmartProjectAddress());
        
        // 经纬度
        if (StringUtils.isNotBlank(project.getLng())) {
            entity.setLongitude(new BigDecimal(project.getLng()));
        }
        if (StringUtils.isNotBlank(project.getLat())) {
            entity.setLatitude(new BigDecimal(project.getLat()));
        }
        
        entity.setProjectDepartment(project.getProjectDeptId());
        entity.setCompanyLvl2(getCompanyLvl2(project));
        
        // 项目规模和面积
        entity.setProjScale(project.getProjectScale());
        if (project.getBuildingArea() != null) {
            entity.setProjBuildArea(project.getBuildingArea());
        }
        
        // 工程信息
        entity.setEngineImportanceCate(project.getProjectImportanceClass());
        entity.setSignPrime(project.getSignedSubjectValue());
        entity.setBuildUnit(project.getBuildUnit());
        entity.setDesignComp(project.getDesignUnit());
        entity.setRecceComp(project.getSurveyUnit());
        entity.setSupevsComp(project.getSupervisionUnit());
        
        // 特殊字段
        entity.setFSftybd(project.getSameSectionAsGc());
        entity.setIsBeltRoad("2"); // 默认否
        entity.setIsFreeze("2"); // 默认否
        
        // 系统字段
        String currentTime = TIMESTAMP_FORMAT.format(new Date());
        entity.setCreateUser(project.getCreateBy());
        entity.setCreateTime(currentTime);
        entity.setUpdateUser(project.getUpdateBy());
        entity.setUpdateTime(currentTime);
        entity.setSendTime(currentTime);
        entity.setSubmittedByOrgId(project.getProjectDeptId());
        entity.setVersion("1.0");
        entity.setOperType("add");
        
        return entity;
    }

    /**
     * 格式化日期
     */
    private String formatDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return DATE_FORMAT.format(new Date(timestamp));
    }

    /**
     * 获取项目状态
     */
    private String getProjectStatus(Project project) {
        // 根据项目状态映射到MDM状态
        if (project.getProjectStatus() != null) {
            switch (project.getProjectStatus()) {
                case 0:
                    return "1.1"; // 立项中
                case 1:
                    return "1.2"; // 完成立项
                default:
                    return "1.1";
            }
        }
        return "1.1";
    }

    /**
     * 获取二级单位
     */
    private String getCompanyLvl2(Project project) {
        // 根据项目部门路径获取二级单位
        String deptIdPath = project.getProjectDeptIdPath();
        if (StringUtils.isNotBlank(deptIdPath)) {
            String[] parts = deptIdPath.split("/");
            if (parts.length >= 3) {
                return parts[2]; // 假设第三级是二级单位
            }
        }
        return project.getProjectDeptId();
    }

    /**
     * 生成消息ID
     */
    private String generateMsgId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
