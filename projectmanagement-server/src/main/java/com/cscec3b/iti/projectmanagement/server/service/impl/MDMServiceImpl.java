package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.feign.MDMOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.feign.entity.MdmPushEntity;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMEngineProjectPushDto;
import com.cscec3b.iti.projectmanagement.server.service.MDMService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.util.MDMDataConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.UUID;

/**
 * MDM集成服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MDMServiceImpl implements MDMService {

    private final MDMOpenApiFeign mdmOpenApiFeign;
    private final ProjectService projectService;
    private final IBidApprovalService bidApprovalService;

    @Override
    public MDMApiResponse validateEngineProject(BidApproval bidApproval) {
        log.info("开始校验工程项目数据，BidApproval ID: {}", bidApproval.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDtoFromBidApproval(bidApproval);
            MDMApiResponse response = mdmOpenApiFeign.validateEngineProject(pushDto);
            log.info("校验工程项目数据完成，BidApproval ID: {}, 响应: {}", bidApproval.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("校验工程项目数据失败，BidApproval ID: {}", bidApproval.getId(), e);
            throw new FrameworkException("校验工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse pushEngineProject(BidApproval bidApproval) {
        log.info("开始推送工程项目数据到MDM，BidApproval ID: {}", bidApproval.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDtoFromBidApproval(bidApproval);
            MDMApiResponse response = mdmOpenApiFeign.pushEngineProject(pushDto);
            log.info("推送工程项目数据到MDM完成，BidApproval ID: {}, 响应: {}", bidApproval.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("推送工程项目数据到MDM失败，BidApproval ID: {}", bidApproval.getId(), e);
            throw new FrameworkException("推送工程项目数据到MDM失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse distributeEngineProject(BidApproval bidApproval) {
        log.info("开始分发工程项目数据，BidApproval ID: {}", bidApproval.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDtoFromBidApproval(bidApproval);
            MDMApiResponse response = mdmOpenApiFeign.distributeEngineProject(pushDto);
            log.info("分发工程项目数据完成，BidApproval ID: {}, 响应: {}", bidApproval.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("分发工程项目数据失败，BidApproval ID: {}", bidApproval.getId(), e);
            throw new FrameworkException("分发工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse validateEngineProjectByBidApprovalId(Long bidApprovalId) {
        BidApproval bidApproval = bidApprovalService.getById(bidApprovalId);
        if (bidApproval == null) {
            throw new FrameworkException("中标未立项不存在，ID: " + bidApprovalId);
        }
        return validateEngineProject(bidApproval);
    }

    @Override
    public MDMApiResponse pushEngineProjectByBidApprovalId(Long bidApprovalId) {
        BidApproval bidApproval = bidApprovalService.getById(bidApprovalId);
        if (bidApproval == null) {
            throw new FrameworkException("中标未立项不存在，ID: " + bidApprovalId);
        }
        return pushEngineProject(bidApproval);
    }

    @Override
    public MDMApiResponse distributeEngineProjectByBidApprovalId(Long bidApprovalId) {
        BidApproval bidApproval = bidApprovalService.getById(bidApprovalId);
        if (bidApproval == null) {
            throw new FrameworkException("中标未立项不存在，ID: " + bidApprovalId);
        }
        return distributeEngineProject(bidApproval);
    }

    @Override
    public MDMApiResponse validateEngineProject(Project project) {
        log.info("开始校验工程项目数据，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.validateEngineProject(pushDto);
            log.info("校验工程项目数据完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("校验工程项目数据失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("校验工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse pushEngineProject(Project project) {
        log.info("开始推送工程项目数据到MDM，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.pushEngineProject(pushDto);
            log.info("推送工程项目数据到MDM完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("推送工程项目数据到MDM失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("推送工程项目数据到MDM失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse distributeEngineProject(Project project) {
        log.info("开始分发工程项目数据，项目ID: {}", project.getId());
        try {
            MDMEngineProjectPushDto pushDto = buildPushDto(project);
            MDMApiResponse response = mdmOpenApiFeign.distributeEngineProject(pushDto);
            log.info("分发工程项目数据完成，项目ID: {}, 响应: {}", project.getId(), response);
            return response;
        } catch (Exception e) {
            log.error("分发工程项目数据失败，项目ID: {}", project.getId(), e);
            throw new FrameworkException("分发工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
    public MDMApiResponse validateEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return validateEngineProject(project);
    }

    @Override
    public MDMApiResponse pushEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return pushEngineProject(project);
    }

    @Override
    public MDMApiResponse distributeEngineProjectById(Long projectId) {
        Project project = projectService.getById(projectId);
        if (project == null) {
            throw new FrameworkException("项目不存在，项目ID: " + projectId);
        }
        return distributeEngineProject(project);
    }

    /**
     * 构建MDM推送数据（基于BidApproval）
     *
     * @param bidApproval 中标未立项实体
     * @return MDM推送DTO
     */
    private MDMEngineProjectPushDto buildPushDtoFromBidApproval(BidApproval bidApproval) {
        MDMEngineProjectPushDto pushDto = new MDMEngineProjectPushDto();
        pushDto.setMsgId(generateMsgId());
        pushDto.setDataType("Engproject");

        MdmPushEntity entity = MDMDataConverter.convertFromBidApproval(bidApproval);
        pushDto.setData(Collections.singletonList(entity));

        return pushDto;
    }

    /**
     * 构建MDM推送数据（基于Project，保留兼容性）
     *
     * @param project 项目实体
     * @return MDM推送DTO
     */
    private MDMEngineProjectPushDto buildPushDto(Project project) {
        MDMEngineProjectPushDto pushDto = new MDMEngineProjectPushDto();
        pushDto.setMsgId(generateMsgId());
        pushDto.setDataType("Engproject");

        MdmPushEntity entity = MDMDataConverter.convertFromProject(project);
        pushDto.setData(Collections.singletonList(entity));

        return pushDto;
    }

    /**
     * 生成消息ID
     */
    private String generateMsgId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
