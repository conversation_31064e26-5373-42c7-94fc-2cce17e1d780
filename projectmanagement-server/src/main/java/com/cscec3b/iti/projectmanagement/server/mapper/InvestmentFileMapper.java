package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.server.entity.InvestmentFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InvestmentFileMapper extends BaseMapper<InvestmentFile> {

    /**
     * 获取文件列表
     *
     * @param belongFileType          所属文件类型
     * @param belongId                所属文件ID
     * @param fileCode                文件编码
     * @param id                      ID
     * @param yunshuExecuteUnitIdPath 执行单位ID
     * @return {@link List }<{@link InvestmentFile }>
     */
    List<InvestmentFile> getList(@Param("belongFileType") Integer belongFileType, @Param("belongId") String belongId,
                                 @Param("fileCode") String fileCode, @Param("id") Long id,
                                 @Param("yunshuExecuteUnitIdPath") String yunshuExecuteUnitIdPath);
}
