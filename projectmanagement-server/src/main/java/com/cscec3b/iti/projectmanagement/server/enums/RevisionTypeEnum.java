package com.cscec3b.iti.projectmanagement.server.enums;

import com.cscec3b.iti.common.base.dictionary.IDataDictionary;
import lombok.Getter;


/**
 * 修订枚举类型
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@Getter
public enum RevisionTypeEnum implements IDataDictionary {
    /**
     * 云枢id
     */
    YUNSHU_ORG_ID(1, "修订云枢id", "yunshuOrgId"),
    /**
     * 财商信息
     */
    FINANCE_INFO(2, "修订财商信息", "financeInfo"),
    
    /**
     * 修订执行单元
     */
    EXECUTE_UNIT(3, "修订执行单位信息", "executeUnit"),

    /**
     * 修订云筑网编码
     */
    YZW_PROJECT_ID(4, "修订云筑网编码", "yzwProjectId"),

    ADDITION(5, "补录云枢id", "addition yunshu org id"),

    /**
     * 创建组织
     */
    CREATE_ORG_IN_UC(6, "创建组织", "create org in uc"),

    /**
     * 组织中心事件同步
     */
    UC_ORG_EVENT(7, "组织中心事件同步","sync_uc_org_event");

    final Integer dictCode;

    final String zhCN;

    final String enUS;

    RevisionTypeEnum(Integer dictCode, String zhCN, String enUS) {
        this.dictCode = dictCode;
        this.zhCN = zhCN;
        this.enUS = enUS;
    }

    
    /**
     * 通过code 获取
     *
     * @param code code
     * @return DeptTypeEnum
     */
    public static RevisionTypeEnum getEnumByCode(Integer code) {
        RevisionTypeEnum[] values = RevisionTypeEnum.values();
        for (RevisionTypeEnum value : values) {
            if (value.getDictCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String getDesc() {
        return "修订类型";
    }
}
