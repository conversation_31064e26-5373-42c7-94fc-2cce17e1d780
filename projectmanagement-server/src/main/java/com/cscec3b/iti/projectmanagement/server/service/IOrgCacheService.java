package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;

import java.util.List;

/**
 * 云枢组织相关服务层
 *
 * <AUTHOR>
 * @date 2023/08/12 23:09
 **/

public interface IOrgCacheService {


    /**
     * 获取云枢组织树
     *
     * @param orgId       orgId
     * @param isEntityOrg 是否仅返回实体机构
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     * <AUTHOR>
     * @date 2023/10/30
     */
    List<YunshuOrgDepartmentTreeModel> getOrgTreeFromYunshu(String orgId, boolean isEntityOrg);

    /**
     * 查询部门详情
     *
     * @param departmentId deptId
     * @return {@link YunshuOrgDepartmentEntity }
     * <AUTHOR>
     * @date 2023/10/30
     */
    YunshuOrgDepartmentEntity getDepartmentFromYunshu(String departmentId);


    // void cacheYunshuOrgTreeV2(String parentTreeId);

    // -------------------- 核心方法 --------------------
    @Lock(lockKey = "cacheYunshuOrgTree", leaseTime = 2 * 60 * 60 * 1000L)
    void cacheYunshuOrgTreeV3(String parentTreeId);

}
