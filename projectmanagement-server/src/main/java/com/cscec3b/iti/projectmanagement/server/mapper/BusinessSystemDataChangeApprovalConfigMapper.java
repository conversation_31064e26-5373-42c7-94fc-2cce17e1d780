package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataChangeConfigResp;
import com.cscec3b.iti.projectmanagement.server.entity.BusSysDataChangeApprovalConfig;

import java.util.List;

public interface BusinessSystemDataChangeApprovalConfigMapper extends BaseMapper<BusSysDataChangeApprovalConfig> {
    /**
     * 分頁查询
     *
     * @param configReq@return {@link List}<{@link BusSysDataChangeApprovalConfig}>
     */
    List<BusSysDataChangeConfigResp> pageList(BusSysDataChangeConfigPageReq configReq);
}