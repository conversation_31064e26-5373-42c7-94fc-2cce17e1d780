package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.RevisionRecordResp;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectRevisionRecordMapper;
import com.cscec3b.iti.projectmanagement.server.service.IProjectRevisionRecordService;
import com.github.pagehelper.PageHelper;

@Service
public class ProjectRevisionRecordServiceImpl implements IProjectRevisionRecordService {

    private final ProjectRevisionRecordMapper yunshuRevisionRecordMapper;

    public ProjectRevisionRecordServiceImpl(ProjectRevisionRecordMapper yunshuRevisionRecordMapper) {
        this.yunshuRevisionRecordMapper = yunshuRevisionRecordMapper;
    }
    
    /**
     * 查询修订记录
     * Xinfa
     * 2023/08/21
     *
     * @param current            页码
     * @param size               数量
     * @param projectId          项目id
     * @param revisionType       修改类型
     * @param projectFinanceCode 项目编码
     * @param projectFinanceName 项目名称
     * @param originalValue      修订前云枢记录
     * @param revisedValue       修订后云枢记录
     * @param userId             用户id
     * @param username           用户名
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @return {@link Page }<{@link RevisionRecordResp }>
     */
    @Override
    public Page<RevisionRecordResp> getRecord(int current, int size, Long projectId, Integer revisionType,
        String projectFinanceCode, String projectFinanceName, String originalValue, String revisedValue, String userId,
        String username, Long startTime, Long endTime, String remark) {
        // 分页查询
        final List<RevisionRecordResp> result;
        final Page<RevisionRecordResp> objectPage;
        try (com.github.pagehelper.Page<RevisionRecordResp> page = PageHelper.startPage(current, size).doSelectPage(
            () -> yunshuRevisionRecordMapper.getRecodrPageList(projectId, revisionType, projectFinanceCode,
                projectFinanceName, originalValue, revisedValue, userId, username, startTime, endTime, remark))) {
            result = page.getResult();
            objectPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        }
        objectPage.setRecords(result);
        return objectPage;
    }
}
