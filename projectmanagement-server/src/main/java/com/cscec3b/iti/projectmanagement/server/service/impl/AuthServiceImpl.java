package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.jwt.JWT;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.SwitchOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.YunshuOrgSyncResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.feign.IXindunApiService;
import com.cscec3b.iti.projectmanagement.server.service.IAuthService;
import com.cscec3b.iti.projectmanagement.server.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 六统一用户认证
 *
 * <AUTHOR>
 * @date 2023/10/23 18:16
 **/

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class AuthServiceImpl implements IAuthService {


    private final IXindunApiService xindunApiService;


    private final IOrgService orgService;

    private final RedisTemplate<String, Objects> redisTemplate;

    private final HttpServletRequest request;


    public AuthServiceImpl(@Lazy IXindunApiService xindunApiService, IOrgService orgService, RedisTemplate<String,
            Objects> redisTemplate, HttpServletRequest request) {
        this.xindunApiService = xindunApiService;
        this.orgService = orgService;
        this.redisTemplate = redisTemplate;
        this.request = request;
    }


    @Override
    public UserInfo getUserInfo() {
        // 去芯盾获取用户信息
        final UserInfo userInfo = xindunApiService.getUserInfo();
        if (Objects.isNull(userInfo)) {
            throw new BusinessException(8010101);
        }
        // 获取用户的组织信息，同时获取上级组织信息
        final String orgId = userInfo.getOrgId();
        // 向上获取实体信息
        final ExecuteUnitTreeDto unitTreeDto = orgService.getEntityOrgByDeptId(orgId);
        userInfo.setOrgInfo(BeanUtil.copyProperties(unitTreeDto, YunshuOrgSyncResp.class));
        return userInfo;
    }

    @Override
    public SwitchOrgResp switchOrg(String orgId) {
        SwitchOrgResp switchOrgResp = xindunApiService.switchOrg(orgId);
        if (StringUtils.isBlank(switchOrgResp.getOrgId())) {
            throw new BusinessException(80107004);
        }
        // 清除token缓存
        // token 一定存在，可不判空
        String token = request.getHeader(Constants.AUTHORIZATION);
        String jti = String.valueOf(JWT.of(token.replace(Constants.BEARER, "")).getPayload(Constants.JWT_ID));
        redisTemplate.delete(jti);
        return switchOrgResp;
    }
}
