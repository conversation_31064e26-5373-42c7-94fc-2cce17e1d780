package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.server.mapper.ProTenderMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.SyncUpdateOrgEvent;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 同步更新组织事件监听服务
 *
 * <AUTHOR>
 * @date 2024/01/26
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncUpdateOrgEventListener {

    private final ProjectMapper projectMapper;

    private final ProTenderMapper proTenderMapper;

    private final ProjectEventSubscribeMapper subscribeMapper;


    @Async("cpmTaskExecutor")
    @EventListener(SyncUpdateOrgEvent.class)
    public void syncOrg(SyncUpdateOrgEvent event) {
        log.info("同步更新组织事件监听服务开始");
        final List<ExecuteUnitTreeDto> treeDtoList = event.getTreeDtoList();
        if (CollectionUtils.isNotEmpty(treeDtoList)) {
            final List<List<ExecuteUnitTreeDto>> partitionList = Lists.partition(treeDtoList, 500);
            partitionList.forEach(partition -> {
                // 更新项目信息
                projectMapper.updateYunshuUnitByExecuteUnitId(partition);
                // 更新中标未立项表
                proTenderMapper.syncUpdateOrg(partition, "bid_approval");
                // 更新投标总结表
                proTenderMapper.syncUpdateOrg(partition, "bid_summary");
                // 更新合同定案表
                proTenderMapper.syncUpdateOrg(partition, "contract");
                // 更新局内分包合同表
                proTenderMapper.syncUpdateOrg(partition, "bureau_contract");
                // 更新局内补充协议表
                proTenderMapper.syncUpdateOrg(partition, "bureau_supplementary_agreement");
                // 更新补充协议表
                proTenderMapper.syncUpdateOrg(partition, "supplementary_agreement");
                // 更新消费业务系统表
                subscribeMapper.updateSubscriberByExecuteUnitId(partition);


            });
        }
        log.info("同步更新组织事件监听服务结束");
    }
}
