package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryYunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.UpdateYunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.YunshuStandOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunshuStandOrgResp;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuStandOrgMapping;
import com.cscec3b.iti.projectmanagement.server.mapper.YunshuStandOrgMappingMapper;
import com.cscec3b.iti.projectmanagement.server.service.YunshuStandOrgMappingService;
import com.github.pagehelper.PageHelper;
import com.odin.freyr.common.orika.BeanMapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 云枢组织与标准组织映射
 */
@Service
public class YunshuStandOrgMappingServiceImpl implements YunshuStandOrgMappingService {

    private final YunshuStandOrgMappingMapper yunshuStandOrgMappingMapper;


    public YunshuStandOrgMappingServiceImpl(YunshuStandOrgMappingMapper yunshuStandOrgMappingMapper) {
        this.yunshuStandOrgMappingMapper = yunshuStandOrgMappingMapper;
    }

    @Override
    public int insert(YunshuStandOrgMapping record) {
        return yunshuStandOrgMappingMapper.insert(record);
    }

    @Override
    public YunshuStandOrgMapping selectByPrimaryKey(Long id) {
        return yunshuStandOrgMappingMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(YunshuStandOrgMapping record) {
        return yunshuStandOrgMappingMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateBatchSelective(List<YunshuStandOrgMapping> list) {
        return yunshuStandOrgMappingMapper.updateBatchSelective(list);
    }

    @Override
    public Boolean update(UpdateYunshuStandOrgReq req) {
        if (yunshuStandOrgMappingMapper.getCountByStandOrgIdAndYunshuId(req.getStandUnitId(),
            req.getYunshuExecuteUnitId(), req.getId()) > 0) {
            throw new FrameworkException(-1, "已存在相同的映射关系");
        }
        final YunshuStandOrgMapping mapping = BeanUtil.copyProperties(req, YunshuStandOrgMapping.class);
        return yunshuStandOrgMappingMapper.updateBatchSelective(Collections.singletonList(mapping)) > 0;
    }

    @Override
    public List<YunshuStandOrgReq> batchInsert(List<YunshuStandOrgReq> list) {
        List<YunshuStandOrgReq> notInserts = new ArrayList<>();
        // 创建一个空的List，用于存放不插入的数据
        final List<YunshuStandOrgMapping> mappingList = list.stream().filter(mapping -> {
            // 获取指定的表格id
            final String standUnitId = mapping.getStandUnitId();
            // 获取指定的yunshu表格id
            final String yunshuExecuteUnitId = mapping.getYunshuExecuteUnitId();
            // 根据指定的表格id和yunshu表格id查询数据是否存在
            if (yunshuStandOrgMappingMapper.getCountByStandOrgIdAndYunshuId(standUnitId, yunshuExecuteUnitId, null) > 0) {
                // 如果存在，则将其加入到notInserts中
                notInserts.add(mapping);
                return false;
            }
            return true;
        }).map(mapping -> BeanMapUtils.map(mapping, YunshuStandOrgMapping.class)).collect(Collectors.toList());
        // 如果mappingList不为空，则批量插入数据
        if (CollectionUtils.isNotEmpty(mappingList)) {
            yunshuStandOrgMappingMapper.batchInsert(mappingList);
        }
        // 返回notInserts
        return notInserts;
    }

    @Override
    public Page<YunshuStandOrgResp> pageList(QueryYunshuStandOrgReq req) {
        final com.github.pagehelper.Page<YunshuStandOrgResp> page =
                // 开启分页查询
            PageHelper.startPage(req.getCurrent(), req.getSize()).doSelectPage(
                    // 查询数据
                () -> BeanUtil.copyToList(yunshuStandOrgMappingMapper.pageList(req), YunshuStandOrgResp.class));
        return new Page<YunshuStandOrgResp>(page.getTotal(), page.getPageNum(), page.getPageSize())
            .setRecords(page.getResult());
    }

    @Override
    public Boolean delete(Set<Long> ids) {
        return yunshuStandOrgMappingMapper.delete(ids) > 0;
    }

    @Override
    public YunshuStandOrgMapping selectByYunshuUnitId(String yunshuUnitId) {
        return yunshuStandOrgMappingMapper.selectByYunshuUnitId(yunshuUnitId);
    }

    @Override
    public YunshuStandOrgMapping selectByStandOrgCode(String standOrgCode) {
        return yunshuStandOrgMappingMapper.selectByStandOrgCode(standOrgCode);
    }
}
