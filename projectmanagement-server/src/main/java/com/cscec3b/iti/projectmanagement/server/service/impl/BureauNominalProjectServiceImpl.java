package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.BureauNominalPageParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauNominalProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectDetailResp;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.BureauNameProjectTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.service.IBureauNominalProjectService;
import com.github.pagehelper.PageHelper;

/**
 * <AUTHOR>
 * @date 2023/07/31 10:24
 **/

@Service
@Transactional(rollbackFor = Exception.class)
public class BureauNominalProjectServiceImpl implements IBureauNominalProjectService {

    private final ProjectMapper projectMapper;

    public BureauNominalProjectServiceImpl(ProjectMapper projectMapper) {
        this.projectMapper = projectMapper;
    }

    @Override
    public BureauNamedProjectRelationship getProjectRelationShip(int type, Long projectId, String financeCode) {
        return projectMapper.getBureauNominalProject(type, projectId, financeCode);
    }

    /**
     * @param projectId 项目id
     * @return
     */
    @Override
    public BureauNamedProjectRelationship changeProjectRelation(Long projectId) {
        final Project project = projectMapper.selectById(projectId);
        final Integer bureauNominalProjectType = project.getBureauNominalProjectType();
        // 总包项目转换时不能有分包项目
        if (Objects.equals(bureauNominalProjectType, BureauNameProjectTypeEnum.BUREAU_NAMED_GENERAL_CONTRACTOR.getDictCode())) {
            final BureauNamedProjectRelationship bureauNominalProject =
                    this.getProjectRelationShip(bureauNominalProjectType, projectId, null);
            if (ObjectUtils.isNotEmpty(bureauNominalProject)
                    && CollectionUtils.isNotEmpty(bureauNominalProject.getSubcontractingProjects())) {
                // 当前局名义总包项目下还存在分包项目，不允许变更类型
                return bureauNominalProject;
            }
        } else if (Objects.equals(bureauNominalProjectType, BureauNameProjectTypeEnum.BUREAU_NAME_SUBCONTRACTING.getDictCode())) {
            // 分包项目只能通过取消与总包项目的绑定关系转换非局名义项目
            throw new BusinessException(80106051);
        }
        projectMapper.changeProjectRelation(projectId);
        return null;
    }

    /**
     * 通过财务代码
     *
     * @param financeCode 财务代码
     * @return {@link ProjectDetailResp}
     */
    @Override
    public ProjectDetailResp getByFinanceCode(String financeCode) {
        try {
            return projectMapper.getByFinanceCode(financeCode);
        } catch (Exception e) {
            if (e.getCause() instanceof TooManyResultsException) {
                throw new BusinessException(80106052);
            }
            throw new BusinessException(-1);
        }
    }

    /**
     * 将项目添加到总承包项目中
     *
     * @param generalContractorId 总承包商id
     * @param projectId           项目id
     * @return {@link Boolean}
     */
    @Override
    public Boolean addProjectToGeneralContractorProject(Long generalContractorId, Long projectId) {
        // 校验总包项目
        final Project project = projectMapper.selectById(generalContractorId);
        if (ObjectUtils.isEmpty(project) || !BureauNameProjectTypeEnum.BUREAU_NAMED_GENERAL_CONTRACTOR.getDictCode()
                .equals(project.getBureauNominalProjectType())) {
            // 总包项目状态异常，请刷新页面并重试
            throw new BusinessException(80106051);
        }
        // 校验分包项目
        final Project subProject = projectMapper.selectById(projectId);
        if (ObjectUtils.isEmpty(subProject) || !BureauNameProjectTypeEnum.NON_BUREAU_NAME_PROJECTS.getDictCode()
                .equals(subProject.getBureauNominalProjectType())) {
            // 添加的分包项目异常，请刷新页面并重试；
            throw new BusinessException(80106051);
        }
        // 修改为分包状态
        return projectMapper.setToSubContractingProject(generalContractorId, projectId) == 1;
    }


    /**
     * 从总包项目中移除分包项目
     * @param subcontractingId 分包项目id
     * @return
     */
    @Override
    public Boolean removeSubcontractingProjectId(Long subcontractingId) {
        // 校验分包项目信息
        final Project project = projectMapper.selectById(subcontractingId);
        if (ObjectUtils.isEmpty(project) || !BureauNameProjectTypeEnum.BUREAU_NAME_SUBCONTRACTING.getDictCode()
                .equals(project.getBureauNominalProjectType())) {
            // 分包项目异常，请刷新页面并重试；
            throw new BusinessException(80106051);
        }
        return projectMapper.removeSubcontractingProject(subcontractingId) == 1;
    }

    /**
     * 局名义项目列表查询
     *
     * @param queryParams
     * @return Page
     * @date 2023/08/01 10:19
     * <AUTHOR>
     */
    @Override
    public Page<BureauNominalProjectPageResp> bureauNominalProjectPage(BureauNominalPageParams queryParams) {
        //分页查询局名义项目列表
        com.github.pagehelper.Page<BureauNominalProjectPageResp> page = PageHelper.startPage(queryParams.getCurrent(), queryParams.getSize())
                .doSelectPage(() -> projectMapper.getBureauNominalProjects(queryParams));
        if (CollectionUtils.isEmpty(page.getResult())) {
            return new Page<>();
        }
        //获取有总包id的项目，表示已设置为分包
        List<Long> ids = page.getResult().stream().filter(p -> p.getGeneralContractProjectId() != null)
                .map(BureauNominalProjectPageResp::getGeneralContractProjectId)
                .distinct().collect(Collectors.toList());

        List<BureauNominalProjectPageResp> results = new ArrayList<>();
        //查询映射
        if (CollectionUtils.isNotEmpty(ids)) {
            List<BureauNominalProjectPageResp> generalProjects = projectMapper.batchBureauGeneralNames(ids);
            //批量获取总包的项目名称
            Map<Long, BureauNominalProjectPageResp> filterGeneralProjects = generalProjects.stream()
                                                                                    .filter(p -> p.getProjectFinanceName() != null)
                                                                                    .collect(Collectors.toMap(BureauNominalProjectPageResp::getProjectId, p -> p));
            //设置局名义总包合同名称
            for (BureauNominalProjectPageResp project : page.getResult()) {
                if (project.getGeneralContractProjectId() != null) {
                    BureauNominalProjectPageResp general = filterGeneralProjects.get(project.getGeneralContractProjectId());
                    project.setGeneralContractProjectName(general.getProjectFinanceName());
                    project.setGeneralContractSourceSystem(general.getSourceSystem());
                }
                results.add(project);
            }
        } else {
            results = page.getResult();
        }

        Page<BureauNominalProjectPageResp> respPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        respPage.setRecords(results);
        return respPage;
    }


    /**
     * @param queryParams 查询参数
     * @return BureauNominalProjectPageResp
     */
    @Override
    public Page<BureauNominalProjectPageResp> bureauNominalCloudPivotPage(BureauNominalPageParams queryParams) {
        //分页查询局名义项目列表
        com.github.pagehelper.Page<BureauNominalProjectPageResp> page = PageHelper.startPage(queryParams.getCurrent(), queryParams.getSize())
                .doSelectPage(() -> projectMapper.getBureauNominalProjectsCloudPivot(queryParams));
        if (CollectionUtils.isEmpty(page.getResult())) {
            return new Page<>();
        }
        //获取有总包id的项目，表示已设置为分包
        List<Long> ids = page.getResult().stream().filter(p -> p.getGeneralContractProjectId() != null)
                .map(BureauNominalProjectPageResp::getGeneralContractProjectId)
                .distinct().collect(Collectors.toList());

        List<BureauNominalProjectPageResp> results = new ArrayList<>();
        //查询映射
        if (CollectionUtils.isNotEmpty(ids)) {
            List<BureauNominalProjectPageResp> generalProjects = projectMapper.batchBureauGeneralNames(ids);
            //批量获取总包的项目名称
            Map<Long, BureauNominalProjectPageResp> filterGeneralProjects = generalProjects.stream()
                    .filter(p -> p.getProjectFinanceName() != null)
                    .collect(Collectors.toMap(BureauNominalProjectPageResp::getProjectId, p -> p));
            //设置局名义总包合同名称
            for (BureauNominalProjectPageResp project : page.getResult()) {
                if (project.getGeneralContractProjectId() != null) {
                    BureauNominalProjectPageResp general = filterGeneralProjects.get(project.getGeneralContractProjectId());
                    project.setGeneralContractProjectName(general.getProjectFinanceName());
                    project.setGeneralContractSourceSystem(general.getSourceSystem());
                }
                results.add(project);
            }
        } else {
            results = page.getResult();
        }

        Page<BureauNominalProjectPageResp> respPage = new Page<>(page.getTotal(), page.getPageNum(), page.getPageSize());
        respPage.setRecords(results);
        return respPage;
    }
}
