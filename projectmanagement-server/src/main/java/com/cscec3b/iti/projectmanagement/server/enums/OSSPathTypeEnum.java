package com.cscec3b.iti.projectmanagement.server.enums;

import lombok.Getter;

/**
 * 业务类型对应oss目录
 */
@Getter
public enum OSSPathTypeEnum {

    /**
     * 通知中心
     */// 项目级别
    NOTICE_CENTER(1, "notice-center/", "notice-center/"),
    /**
     * 帮助中心
     */
    HELP_CENTER(2, "help-center/", "help-center/"),
    /**
     * 特殊项目
     */
    SPECIAL_PROJECT(3, "special-project/", "special-project/"),

    /**
     * 项目效果图
     */
    EFFECT_PICTURE(4, "effect-picture/", "effect-picture/"),

    /**
     * 项目立项注意事项
     */
    APPROVAL_STEP_NOTE(5, "approval-step-note", "approval-step-note/"),

    /**
     * 保密项目
     */
    SECRECY(6,"secrecy_civil_military","civil-military/"),

    /**
     * 军民融合项目
     */
    CIVIL_MILITARY(7, "civil_military", "civil-military/"),

    /**
     * 项目变更历史
     */
    CHANGE_HISTORY(8, "change-history", "change-history/"),

    /**
     * 其他
     */
    OTHER(99, "other/", "other/");

    private final Integer code;
    private final String name;
    private final String msg;

    OSSPathTypeEnum(Integer code, String name, String msg) {
        this.code = code;
        this.name = name;
        this.msg = msg;
    }

    /**
     *getByCode
     *
     * @param code code
     * @return {@link OSSPathTypeEnum}
     */
    public static OSSPathTypeEnum getByCode(int code) {
        for (OSSPathTypeEnum pathType : OSSPathTypeEnum.values()) {
            if (pathType.getCode() == code) {
                return pathType;
            }
        }
        return OSSPathTypeEnum.OTHER;
    }

}
