package com.cscec3b.iti.projectmanagement.server.enums;

import com.cscec3b.iti.common.web.exception.BusinessException;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum IndependTypeEnum {
    /**
     * 独立立项
     */
    INDEPENDENT(1, "Y", "独立"),

    /**
     * 非独立立项
     */
    NON_INDEPENDENT(2, "N", "非独立"),

    /**
     * 不予立项
     */
    DISABLE(3, "D", "无效"),

    /**
     * 未判断
     */
    NONE(0, "0", "未判断");


    /**
     * id
     */
    final Integer id;

    /**
     * 编码
     */
    final String code;

    /**
     * 名称
     */
    final String name;


    IndependTypeEnum(Integer id, String code, String name) {
        this.id = id;
        this.code = code;
        this.name = name;
    }

    /**
     * 通过id 获取枚举
     *
     * @param id id
     * @return {@link IndependTypeEnum}
     */
    public static IndependTypeEnum getEnumById(Integer id) {
        return Arrays.stream(IndependTypeEnum.values()).filter(e -> e.getId().equals(id)).findFirst()
                .orElseThrow(() -> new BusinessException(8010009));
    }


    /**
     * 通过id 获取枚举
     *
     * @param code code
     * @return {@link IndependTypeEnum}
     */
    public static IndependTypeEnum getEnumByCode(String code) {
        return Arrays.stream(IndependTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst()
                .orElseThrow(() -> new BusinessException(8010009));
    }

}
