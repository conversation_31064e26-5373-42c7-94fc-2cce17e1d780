package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.request.YunShuSmartConstructionOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionSaveResp;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.feign.IUcAppOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.service.YunShuSmartConstructionOrgService;
import com.g3.org.api.dto.executeForQueryDepartmentList.resp.ExecuteForQueryDepartmentListResp;
import com.g3.org.api.dto.executeForQueryDepartmentList.resp.UserDepartment;
import com.g3.org.api.dto.resp.CloudPivotResponse;
import com.g3.org.api.dto.resp.ExecuteGetChildDepartmentsResp;
import com.g3.org.api.dto.resp.ExecuteGetDepartmentResp;
import com.g3.org.api.dto.resp.dep.YunshuOrgDepartmentTreeModel;
import com.g3.org.api.dto.resp.org.YunshuOrgDepartmentEntity;
import com.odin.freyr.common.orika.BeanMapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/14 15:34
 */
@Service
@Slf4j
public class YunShuSmartConstructionOrgServiceImpl implements YunShuSmartConstructionOrgService {

    @Resource
    private IUcAppOpenApiFeign ucAppOpenApiFeign;

    @Override
    public YunShuSmartConstructionOrgResp findByDepartmentId(String departmentId) {
        final ExecuteGetDepartmentResp executeGetDepartmentResp =
                ucAppOpenApiFeign.executeGetDepartment(UcOpenApiProperties.SMART_SITE, departmentId);
        checkResponseEntity(executeGetDepartmentResp, null);
        YunshuOrgDepartmentEntity data = executeGetDepartmentResp.getData();
        if (Objects.isNull(data)){
            throw new FrameworkException(80107010, "获取部门信息失败");
        }
        YunShuSmartConstructionOrgResp constructionOrgResp = BeanUtil.copyProperties(data, YunShuSmartConstructionOrgResp.class);
        if (StringUtils.isEmpty(constructionOrgResp.getParentId())){
            return constructionOrgResp;
        }
        ExecuteGetDepartmentResp parentOrgDepartmentResponse =
                ucAppOpenApiFeign.executeGetDepartment(UcOpenApiProperties.SMART_SITE, departmentId);
        checkResponseEntity(parentOrgDepartmentResponse, null);
        if (Objects.isNull(parentOrgDepartmentResponse.getData())){
            throw new FrameworkException(80107010, "获取部门信息失败");
        }
        YunShuSmartConstructionOrgResp parentConstructionOrgResp = BeanUtil.copyProperties(parentOrgDepartmentResponse.getData(), YunShuSmartConstructionOrgResp.class);
//        final String parentTreeId = parentConstructionOrgResp.getTreeId();
//        final OrgDepartmentListResponse childDepartments = yunShuClient.executeGetChildDepartments(parentTreeId);
//        final List<OrgDepartmentTreeModel> treeModels = childDepartments.getData();
//        // treeModels 不会为空
//        // queryCode
//        final String queryCode =
//                treeModels.stream().filter(treeModel -> treeModel.getDepartmentId().equals(departmentId)).findFirst()
//                .map(OrgDepartmentTreeModel::getQueryCode).orElse("");
//        constructionOrgResp.setQueryCode(queryCode);
        parentConstructionOrgResp.setChild(constructionOrgResp);
        return parentConstructionOrgResp;
    }

    /**
     * 获取deptinfo
     *
     * @param departmentId 部门id
     * @return {@link YunShuSmartConstructionOrgResp }
     * <AUTHOR>
     * @date 2023/09/28
     */
    @Override
    public YunShuSmartConstructionOrgResp getDeptEntity(String departmentId) {
        if (StringUtils.isBlank(departmentId)) {
            throw new FrameworkException(-1, "departmentId is null");
        }
        final YunshuOrgDepartmentEntity departmentEntity = this.getDepartmentEntity(UcOpenApiProperties.SMART_SITE, departmentId);
        return BeanMapUtils.map(departmentEntity, YunShuSmartConstructionOrgResp.class);
    }

    @Override
    public UserDepartment getDeptInfo(String departmentId) {
        if (StringUtils.isBlank(departmentId)) {
            throw new FrameworkException(-1, "departmentId is null");
        }
        return this.getDepartmentInfo(UcOpenApiProperties.SMART_SITE, departmentId);
    }

    /**
     * 获取treebyparenttreeid
     *
     * @param treeParentId parentTreeId
     * @return {@link List }<{@link YunshuOrgDepartmentTreeModel }>
     * <AUTHOR>
     * @date 2023/09/28
     */
    @Override
    public List<YunshuOrgDepartmentTreeModel> getTreeByParentTreeId(String treeParentId) {
        if (StringUtils.isBlank(treeParentId)) {
            throw new FrameworkException(-1, "treeParentId is null");
        }
        final ExecuteGetChildDepartmentsResp listResponse =
                ucAppOpenApiFeign.getChildDepartments(UcOpenApiProperties.SMART_SITE, treeParentId);
        checkResponseEntity(listResponse, null);
        return listResponse.getData();
    }

    /**
     * 获取当前部门信息，queryCode，上级部门信息
     * @param departmentId 部门id
     * @return {@link YunShuSmartConstructionOrgResp }
     * <AUTHOR>
     * @date 2023/09/28
     */
    @Override
    public YunShuSmartConstructionOrgResp findDeptAndParentAndTreeInfo(String departmentId) {

        final YunShuSmartConstructionOrgResp deptEntity = this.getDeptEntity(departmentId);
        final String parentId = deptEntity.getParentId();
        final YunShuSmartConstructionOrgResp parentDeptInfo = this.getDeptEntity(parentId);
        final UserDepartment deptInfo = this.getDeptInfo(departmentId);
        deptEntity.setQueryCode(deptInfo.getQueryCode());
        parentDeptInfo.setChild(deptEntity);

        // // 获取当前部门信息
        // final YunShuSmartConstructionOrgResp deptResp = getDeptEntity(departmentId);
        // // 上级treeId
        // final String treeParentId = deptResp.getTreeParentId();
        // // 上级部门id
        // final String parentId = deptResp.getParentId();
        // final YunShuSmartConstructionOrgResp parentDept = getDeptEntity(parentId);
        // parentDept.setChild(deptResp);
        // final List<YunshuOrgDepartmentTreeModel> treeModelList = getTreeByParentTreeId(treeParentId);
        // if (CollectionUtils.isNotEmpty(treeModelList)) {
        //     // queryCode
        //     final String queryCode =
        //         treeModelList.stream().filter(treemodel -> treemodel.getDepartmentId().equals(departmentId)).findFirst()
        //                 .map(YunshuOrgDepartmentTreeModel::getQueryCode).orElse("");
        //     deptResp.setQueryCode(queryCode);
        // }
        return parentDeptInfo;
    }


    @Override
    public YunShuSmartConstructionSaveResp smartSave(YunShuSmartConstructionOrgReq req) {
        // OrgDepartmentResponse orgDepartmentResponse =
        //         new G3OrgService().executeForAddDepartment(req.getDepartmentName(),
        //         req.getDepartmentCode(), req.getTreeParentId());
        // checkResponseEntity(orgDepartmentResponse);
        // OrgDepartmentEntity data = orgDepartmentResponse.getData();
        // if (Objects.isNull(data)){
        //     throw new FrameworkException(80107011, "组织下新增项目部失败");
        // }
        // return BeanUtil.copyProperties(data, YunShuSmartConstructionSaveResp.class);
        return new YunShuSmartConstructionSaveResp();
    }

    // /**
    //  * 检查响应
    //  *
    //  * @param response 响应
    //  * <AUTHOR>
    //  * @date 2023/08/23
    //  */
    // private void checkResponseEntity(CloudPivotResponse<?> response) {
    //     if (ObjectUtil.isNull(response) || !response.isSuccess()) {
    //         log.error("响应失败:{}", response.toString());
    //         throw new FrameworkException(80107000, JsonUtils.toJsonStr(response));
    //
    //     }
    // }
    private void checkResponseEntity(CloudPivotResponse<?> response, String errorMsg) {
        if (ObjectUtils.allNotNull(response, response.getErrcode()) && !response.getErrcode().equals(0L)) {
            log.error("响应失败:{}", response);
            throw new FrameworkException(-1, errorMsg + ": " + JsonUtils.toJsonStr(response));
        }
    }

    /**
     * 获取组织详情
     *
     * @param treeType     组织树类型
     * @param departmentId 组织ID
     * @return {@link YunshuOrgDepartmentEntity }
     */
    private YunshuOrgDepartmentEntity getDepartmentEntity(String treeType, String departmentId) {
        final ExecuteGetDepartmentResp smartSiteDepartment =
                ucAppOpenApiFeign.executeGetDepartment(treeType, departmentId);
        checkResponseEntity(smartSiteDepartment, "调用UC接口获取组织信息失败");
        final YunshuOrgDepartmentEntity departmentEntity = smartSiteDepartment.getData();
        Objects.requireNonNull(departmentEntity, String.format("详情为空：请检查组织ID: %s 是否正确", departmentId));
        return smartSiteDepartment.getData();
    }

    /**
     * 获取组织 树详情
     *
     * @param treeType     组织树类型
     * @param departmentId 组织ID
     * @return {@link YunshuOrgDepartmentEntity }
     */
    private UserDepartment getDepartmentInfo(String treeType, String departmentId) {
        final ExecuteForQueryDepartmentListResp smartSiteDepartment =
                ucAppOpenApiFeign.executeGetDepartmentInfo(treeType, departmentId);
        checkResponseEntity(smartSiteDepartment, "调用UC接口获取组织信息失败");
        final List<UserDepartment> departmentEntity = smartSiteDepartment.getData();
        Objects.requireNonNull(departmentEntity, String.format("详情为空：请检查组织ID: %s 是否正确", departmentId));
        return CollectionUtils.isEmpty(departmentEntity) ? null : departmentEntity.get(0);
    }

}
