package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.api.dto.request.event.ProjectEventFlowReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowEventLogResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @description IProjectEventPushRecordService
 * @date 2023/09/26 17:45
 */
public interface IProjectEventPushRecordService {

    /**
     * 插入记录
     * @param record
     * @return int
     */
    int insert(ProjectEventPushRecord record);

    /**
     * @param req
     * @return {@link List}<{@link ProjectEventPushRecord}>
     */
    List<ProjectEventPushRecord> queryPushRecords(ProjectEventPushRecord req);

    /**
     * @return int
     */
    int clearPushRecords();

    /**
     * 查询推送记录
     * @param flowReq 查询条件
     * @return {@link List}<{@link ProjectFlowEventLogResp}>
     */
    List<ProjectFlowEventLogResp> queryEventAndPushRecords(ProjectEventFlowReq flowReq);

    /**
     * 更新推送记录
     *
     * @param pushRecord
     * @return int
     */
    int upadte(ProjectEventPushRecord pushRecord);
}
