package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.IContractEntryApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.investment.InvestmentFileReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.InvestmentFileService;
import com.cscec3b.iti.projectmanagement.server.service.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 合同录入
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(IContractEntryApi.PATH)
@Api(tags = "合同录入")
@Slf4j
public class ContractEntryController implements IContractEntryApi {

    @Autowired
    private ProTenderService proTenderService;

    @Resource
    private BureauSupplementaryAgreementService bureauSupplementaryAgreementService;

    @Resource
    private BureauContractService bureauContractService;

    @Resource
    private SupplementaryAgreementService supplementaryAgreementService;

    @Resource
    private IMarketProjectHookService marketProjectHookService;

    @Resource
    private InvestmentFileService investmentFileService;
    
    /**
     * 合同定案录入API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> contractEntry(MarketProReq<ContractEntryReq> request) {
        return new GenericityResponse<>(proTenderService.contractEntry(request));
    }
    
    /**
     * 局内补充协议录入API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> bureauSupplementaryAgreementEntry(MarketProReq<BureauSupplementaryAgreementReq> request) {
        return new GenericityResponse<>(bureauSupplementaryAgreementService.entry(request));
    }
    
    /**
     * 局内分包合同录入API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> bureauContractEntry(MarketProReq<BureauContractReq> request) {
        return new GenericityResponse<>(bureauContractService.entry(request));
    }
    
    /**
     * 补充协议录入API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> supplementaryAgreementEntry(MarketProReq<SupplementaryAgreementReq> request) {
        return new GenericityResponse<>(supplementaryAgreementService.entrySupplementaryAgreement(request));
    }
    
    /**
     * 投标总结录入API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    @Lock(lockKey = "#request.data.belongId", waitTime = 3000)
    public GenericityResponse<Boolean> tenderEntry(MarketProReq<BidSummaryReq> request) {
        return new GenericityResponse<>(proTenderService.tenderEntry(request));
    }
    
    /**
     * 局内分包合同项目挂接
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> bureauContractHook(MarketProHookReq<BureauContractReq> request) {
        return ResponseBuilder.fromData(marketProjectHookService.bureauContractHook(request));
    }
    
    /**
     * 投标总结项目挂接
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> tenderHook(MarketProHookReq<BidSummaryReq> request) {
        return ResponseBuilder.fromData(marketProjectHookService.tenderHook(request));
    }
    
    /**
     * 补充协议项目挂接
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> supplementaryAgreementHook(MarketProHookReq<SupplementaryAgreementReq> request) {
        return ResponseBuilder.fromData(marketProjectHookService.supplementaryAgreementHook(request));
    }

    @Override
    @Logger
    @ShenyuSpringMvcClient
    public GenericityResponse<String> approvalByInvestment(InvestmentFileReq param) {
        return ResponseBuilder.fromData(investmentFileService.createInvestFile(param));
    }
}
