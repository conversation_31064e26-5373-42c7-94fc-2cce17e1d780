package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec.api.data.out.DictionaryInfo;
import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.model.req.OpenProjectSyncReq;
import com.cscec3b.iti.model.req.ProjectEventCallBackReq;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import com.cscec3b.iti.projectmanagement.api.IExternalOpenApi;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.OrgCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.CompanyViewEngineerProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenByFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenEventNoticeReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenHookQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictDataResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventCallBackResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenByFileResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenHookQueryResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IEventCallbackService;
import com.cscec3b.iti.projectmanagement.server.service.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 对外开放API
 */
@Slf4j
@RestController
@Api(tags = "对外开发API")
@Validated
@RequestMapping(IExternalOpenApi.PATH)
public class ExternalOpenController implements IExternalOpenApi {

    /**
     * 项目服务类
     */
    @Resource
    private ProjectService projectService;

    /**
     * 事件回调服务类
     */
    @Resource
    private IEventCallbackService eventCallbackService;

    @Resource
    private IBureauNominalProjectService relationshipService;

    /**
     * 中标未立项服务类
     */
    @Resource
    private IBidApprovalService bidApprovalService;

    @Resource
    private IProjectSyncService projectSyncService;

    @Resource
    private SysDictDataService dictDataService;

    @Resource
    private IOMService omService;

    @Resource
    private IEventNoticeService hrNoticeService;

    @Resource
    private IEngineeringProjectService engineeringProjectService;

    /**
     * 项目对外信息获取API
     * @param projectOpenReq
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<List<ProjectOpenResp>> externalOpenProject(ProjectOpenReq projectOpenReq) {
        return new GenericityResponse<>(projectService.externalOpenProject(projectOpenReq));
    }

    /**
     * 通过合同信息反查项目信息
     * @param openByFileReq
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<List<ProjectOpenByFileResp>> externalOpenProjectByFiles(
        List<ProjectOpenByFileReq> openByFileReq) {
        return ResponseBuilder.fromData(projectService.externalOpenProjectByFile(openByFileReq));
    }

    /**
     * 工地事件回调接口(根据项目id)
     * @param projectId
     * @param eventId
     * @param eventCode
     * @return
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<ProjectEventCallBackResp> externalOpenByProjectId(int projectId, Integer eventId, String eventCode) {
        return ResponseBuilder.fromData(eventCallbackService.eventCallback(eventId, projectId, eventCode));
    }

    /**
     * 项目分发事件回调接口
     * @param callBackReq
     * @return {@link GenericityResponse }<{@link ProjectArchiveResp }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @Override
    @Logger
    @ShenyuSpringMvcClient
    public GenericityResponse<ProjectArchiveResp> externalOpenForEventCallback(ProjectEventCallBackReq callBackReq) {
        return ResponseBuilder.fromData(eventCallbackService.flowEventCallback(callBackReq));
    }

    /**
     * 接收外部系统事件通知(工地)
     *
     * @param noticeReq
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Boolean> eventNotice(ProjectOpenEventNoticeReq noticeReq) {
        return ResponseBuilder.fromData(eventCallbackService.openEventNotice(noticeReq));
    }

    /**
     * 市场营销中标未立项文件挂接项目列表
     *
     * @param hookReqReq
     * @return {@link GenericityResponse }<{@link Page }<{@link ProjectOpenHookQueryResp }>>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Page<ProjectOpenHookQueryResp>> hookProjectPageList(ProjectOpenHookQueryReq hookReqReq) {
        return ResponseBuilder.fromData(projectService.externalOpenHookProject(hookReqReq));
    }

    /**
     * 通过财商编码查询局名义总包与分包项目信息
     *
     * @param type
     * @param financeCode
     * @return {@link GenericityResponse }<{@link BureauNamedProjectRelationship }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<BureauNamedProjectRelationship> getBureauNominalProjectByFinanceCode(int type, String financeCode) {
        return ResponseBuilder.fromData(relationshipService.getProjectRelationShip(type, null, financeCode));
    }

    /**
     * 分页查询项目列表
     *
     * @param openApiKey
     * @param projectPageReq 分页查询参数
     * @return {@link GenericityResponse}<{@link OpenProjectPageResp}>
     */
    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<OpenProjectPageResp>> getProjectPage(String openApiKey,
            OpenProjectPageReq projectPageReq) {
        return ResponseBuilder.fromData(projectService.getOpenProjectPage(openApiKey, projectPageReq));
    }


    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<BidApprovalPageResp>> getBidPage(String openApiKey, OpenProjectBidPageReq pageReq) {
        return ResponseBuilder.fromData(bidApprovalService.getOpenBidPage(openApiKey, pageReq));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Boolean> syncProjectInfo(OpenProjectSyncReq syncReq) {
        return ResponseBuilder.fromData(projectSyncService.syncProject(syncReq.getCpmProjectKey(),
                syncReq.getYunshuOrgId(), syncReq.getSegment(), syncReq.getData()));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<SysDictDataResp> getDictDataByCode(String dictValue) {
        return ResponseBuilder.fromData(dictDataService.selectDictByValue(dictValue));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<List<DictionaryInfo>> proxyInfoByCode(List<String> codes) {
        return ResponseBuilder.fromData(dictDataService.proxyInfoByCode(codes));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<List<ProjectOpenMappingResp>> getMappingInfo(List<ProjectYzwMappingReq> mappingReqs) {
        return ResponseBuilder.fromData(projectService.getMappingInfo(mappingReqs));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<ProjectOpenResp>> getProjectPageByYunshuIds(String appKey,
            OpenProjectPageByYunshuIdsReq req) {
        return ResponseBuilder.fromData(projectService.getProjectPageByYunshuIds(appKey, req));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<ProjectResp>> getProjectPageByExecuteUnit(String appKey,
            ProjectQueryByExecuteReq queryParams) {
        return ResponseBuilder.fromData(projectService.getProjectPageByExecuteUnit(appKey, queryParams));
    }

    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Map<String, Object>> createProject(OrgCreateReq orgCreateReq) {
        return ResponseBuilder.fromData(omService.createOrgProxy(orgCreateReq));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<?> getFileDetailList(String appKey, ContractFileDetailReq param) {
        return ResponseBuilder.fromData(
                bidApprovalService.getFileDetail(appKey, param.getBelongFileType(), param.getFileCode(), param.getBelongId()));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<OpenProjectArchiveResp>> getProjectArchivedPage(String openApiKey,
                                                                                   OpenProjectArchiveReq pageReq) {
        return ResponseBuilder.fromData(projectService.getOpenProjectArchivedPage(openApiKey, pageReq));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<OpenEngineeringArchiveResp>> getEngineeringArchivedPage(String openApiKey, OpenEngineeringArchiveReq pageReq) {
        return ResponseBuilder.fromData(engineeringProjectService.getEngineeringArchivedPage(openApiKey, pageReq));
    }

    @Override
    public GenericityResponse<Page<OpenEngineeringProjectMappingResp>> getEnginAndStandardProjectMappingInfo(String openApiKey, OpenEngineeringMappingArchiveReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.getEnginAndStandardProjectMappingInfo(openApiKey, req));
    }

    @Override
    @CrossOrigin(origins = "http://localhost")
    @ShenyuSpringMvcClient
    public GenericityResponse<Boolean> sendNoticeToHr(ProjectFlowEventNoticeReq projectEventNoticeReq)
        throws Exception {
        return ResponseBuilder.fromData(hrNoticeService.sendNoticeToHr(projectEventNoticeReq));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<Page<OpenEngineeringProjectPageResp>> getEngineeProjectPage(String openApiKey,
        OpenEngineerProjectPageReq pageReq) {
        return ResponseBuilder.fromData(engineeringProjectService.getOpenEngineeProjectPage(openApiKey, pageReq));
    }

    @Override
    @ShenyuSpringMvcClient
    public GenericityResponse<OpenEngineerProjectTreeResp> getEngineeProjectTreeByKey(String openApiKey, String key) {
        return ResponseBuilder.fromData(engineeringProjectService.getOpenEngineerProjectTreeByKey(openApiKey, key));
    }

    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<OpenProjectFinanceResp> getFinanceInfoByOrgIds(String appKey, String instanceId) {
        return ResponseBuilder.fromData(projectService.getFinanceInfoByOrgId(instanceId));
    }

    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<Page<CompanyViewEngineeringProjectResp>> getCompanyViewEngineeringProjectPage(String openApiKey, CompanyViewEngineerProjectReq req) {
        return ResponseBuilder.fromData(engineeringProjectService.getCompanyViewEngineeringProjectPage(openApiKey, req));
    }

    @Override
    @ShenyuSpringMvcClient
    @Logger
    public GenericityResponse<List<CompanyViewEngineeringProjectTreeResp>> getCompanyViewEngineeringProjectTreeOpen(String openApiKey, Long engineerId) {
        return ResponseBuilder.fromData(engineeringProjectService.getCompanyViewEngineeringProjectTreeOpen(openApiKey, engineerId));
    }
}
