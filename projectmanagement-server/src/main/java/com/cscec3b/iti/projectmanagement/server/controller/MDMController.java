package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.response.GenericityResponse;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;
import com.cscec3b.iti.projectmanagement.server.service.MDMService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MDM集成控制器
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Slf4j
@RestController
@RequestMapping("/mdm")
@Api(tags = "MDM集成管理")
@RequiredArgsConstructor
public class MDMController {

    private final MDMService mdmService;

    @PostMapping("/validate/{projectId}")
    @ApiOperation(value = "校验工程项目数据", notes = "向MDM发送工程项目数据进行校验")
    public GenericityResponse<MDMApiResponse> validateEngineProject(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        log.info("开始校验工程项目数据，项目ID: {}", projectId);
        MDMApiResponse response = mdmService.validateEngineProjectById(projectId);
        return GenericityResponse.success(response);
    }

    @PostMapping("/push/{projectId}")
    @ApiOperation(value = "推送工程项目数据", notes = "向MDM推送工程项目数据")
    public GenericityResponse<MDMApiResponse> pushEngineProject(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        log.info("开始推送工程项目数据，项目ID: {}", projectId);
        MDMApiResponse response = mdmService.pushEngineProjectById(projectId);
        return GenericityResponse.success(response);
    }

    @PostMapping("/distribute/{projectId}")
    @ApiOperation(value = "分发工程项目数据", notes = "分发工程项目数据到其他系统")
    public GenericityResponse<MDMApiResponse> distributeEngineProject(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        log.info("开始分发工程项目数据，项目ID: {}", projectId);
        MDMApiResponse response = mdmService.distributeEngineProjectById(projectId);
        return GenericityResponse.success(response);
    }

    @PostMapping("/sync/{projectId}")
    @ApiOperation(value = "同步工程项目数据", notes = "先校验再推送工程项目数据到MDM")
    public GenericityResponse<MDMApiResponse> syncEngineProject(
            @ApiParam(value = "项目ID", required = true) @PathVariable Long projectId) {
        log.info("开始同步工程项目数据，项目ID: {}", projectId);
        
        // 先校验
        MDMApiResponse validateResponse = mdmService.validateEngineProjectById(projectId);
        if (validateResponse.isFailed()) {
            log.warn("工程项目数据校验失败，项目ID: {}, 错误信息: {}", projectId, validateResponse.getMessage());
            return GenericityResponse.success(validateResponse);
        }
        
        // 校验成功后推送
        MDMApiResponse pushResponse = mdmService.pushEngineProjectById(projectId);
        log.info("工程项目数据同步完成，项目ID: {}", projectId);
        return GenericityResponse.success(pushResponse);
    }
}
