package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;

/**
 * MDM集成服务接口
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
public interface MDMService {

    /**
     * 校验工程项目数据（基于BidApproval）
     *
     * @param bidApproval 中标未立项实体
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProject(BidApproval bidApproval);

    /**
     * 推送工程项目数据到MDM（基于BidApproval）
     *
     * @param bidApproval 中标未立项实体
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProject(BidApproval bidApproval);

    /**
     * 分发工程项目数据（基于BidApproval）
     *
     * @param bidApproval 中标未立项实体
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProject(BidApproval bidApproval);

    /**
     * 根据BidApproval ID校验工程项目数据
     *
     * @param bidApprovalId 中标未立项ID
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProjectByBidApprovalId(Long bidApprovalId);

    /**
     * 根据BidApproval ID推送工程项目数据到MDM
     *
     * @param bidApprovalId 中标未立项ID
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProjectByBidApprovalId(Long bidApprovalId);

    /**
     * 根据BidApproval ID分发工程项目数据
     *
     * @param bidApprovalId 中标未立项ID
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProjectByBidApprovalId(Long bidApprovalId);

    /**
     * 校验工程项目数据（基于Project，保留兼容性）
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProject(Project project);

    /**
     * 推送工程项目数据到MDM（基于Project，保留兼容性）
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProject(Project project);

    /**
     * 分发工程项目数据（基于Project，保留兼容性）
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProject(Project project);

    /**
     * 根据项目ID校验工程项目数据（保留兼容性）
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProjectById(Long projectId);

    /**
     * 根据项目ID推送工程项目数据到MDM（保留兼容性）
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProjectById(Long projectId);

    /**
     * 根据项目ID分发工程项目数据（保留兼容性）
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProjectById(Long projectId);
}
