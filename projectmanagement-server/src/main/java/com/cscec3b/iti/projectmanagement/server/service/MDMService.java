package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.feign.entity.dto.MDMApiResponse;

/**
 * MDM集成服务接口
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
public interface MDMService {

    /**
     * 校验工程项目数据
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProject(Project project);

    /**
     * 推送工程项目数据到MDM
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProject(Project project);

    /**
     * 分发工程项目数据
     *
     * @param project 项目实体
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProject(Project project);

    /**
     * 根据项目ID校验工程项目数据
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse validateEngineProjectById(Long projectId);

    /**
     * 根据项目ID推送工程项目数据到MDM
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse pushEngineProjectById(Long projectId);

    /**
     * 根据项目ID分发工程项目数据
     *
     * @param projectId 项目ID
     * @return MDM响应结果
     */
    MDMApiResponse distributeEngineProjectById(Long projectId);
}
