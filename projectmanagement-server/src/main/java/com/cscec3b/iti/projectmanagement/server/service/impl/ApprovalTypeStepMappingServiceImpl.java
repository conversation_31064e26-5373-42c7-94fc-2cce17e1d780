package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeStepMappingResp;
import com.cscec3b.iti.projectmanagement.server.entity.ApprovalTypeStepMapping;
import com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeStepMappingMapper;
import com.cscec3b.iti.projectmanagement.server.service.ApprovalTypeStepMappingService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class ApprovalTypeStepMappingServiceImpl extends ServiceImpl<ApprovalTypeStepMappingMapper, ApprovalTypeStepMapping> implements ApprovalTypeStepMappingService{

    @Override
    public List<ApprovalTypeStepMappingResp> lastListByType(Long typeId, Long lastUpdateTime) {
        return this.baseMapper.lastListByTypeId(typeId, lastUpdateTime);
    }

    @Override
    public Integer maxVersionByTypeId(Long typeId) {
        Integer version = this.baseMapper.maxVersionByTypeId(typeId);
        return Objects.isNull(version) ? 0 : version;
    }
}
