## 重试starter

![业务流程](\2.png)
![技术方案](\retry.png)

### 使用方法

> <strong><del>建议使用在不依赖返回结果进行下一步处理的方法上</del></strong>

#### 1. 引入依赖

```
<dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>retry-spring-boot-starter</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
```

#### 2. 启动项配置

##### 2.1 @MappperScan原有的扫描路径中添加 com.cscec3b.iti.retry.mapper, 如项目没有配置MapperScan 则需要添加原mapper路径及retry 的mapper路径

##### 2.2 添加 @EnableScheduling 开启定时任务

> mapperScan 扫描路径要指向mapper包

```
@SpringBootApplication(scanBasePackages = "com.cscec3b.iti")
@EnableFeignClients(basePackages = {"com.cscec3b.iti"})
@EnableDiscoveryClient
@MapperScan({"com.cscec3b.iti.retry.mapper","com.cscec3b.iti.projectmanagement.server.mapper"})
@EnableScheduling
@EnableAsync
public class ProjectManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(ProjectManagementApplication.class, args);
    }
}
```

#### 3 application.yml配置 （非必需）

```yaml

mybatis:
  mapper-locations: classpath*:/mapper/*Mapper.xml # mapper扫描

#以下设置无需在yaml配置，可直接在注解中设置
cscec:
  retry:
    # 重试间隔时间,默认毫秒,
    interval-time: 2000
    # 重试延迟的倍数，比如设置interval-time=3000，multiplier=2时，第一次重试为3秒后，第二次为6(3x2)秒，第三次为12(5x2)秒,但最长延迟时间不会超出 maxInterval,最大延迟次数不会超出maxAtt
    multiplier: 3
    # 最大延迟间隔时间，默认30秒，避免multiplier过大引起无限期等待
    max-interval: 120000
    # 最大重试次数
    max-attempts: 3
```

#### 4 sql建表

```sql
create table api_retry_call
(
    id                 varchar(64)       not null comment '主键id'
        primary key,
    class_name         varchar(512)      not null comment '类名或接口名称',
    method_name        varchar(64)       not null comment '方法名',
    param_type         varchar(512)      not null comment '方法参数类型',
    param              varchar(4096)     null comment '方法参数值',
    result             varchar(2048)     null comment 'api返回结果 ',
    success            tinyint default 0 null comment '请求结果 1:成功; 2:失败',
    try_result         varchar(1024)     null comment '定时任务重试结果',
    human_intervention tinyint default 0 null comment '是否需要人工干预 0:否; 1:是;',
    create_at          bigint            not null comment '创建时间',
    update_at          bigint            not null comment '更新时间'
) comment 'API异常回调';


```

#### 5 应用

```xml
# 重试间隔时间,默认毫秒,
        interval-time: 2000
        # 重试延迟的倍数，比如设置interval-time=3000，multiplier=2时，第一次重试为3秒后，第二次为6(3x2)秒，第三次为12(6x2)秒,但最长延迟时间不会超出 maxInterval,最大延迟次数不会超出maxAtt
        multiplier: 2
        # 最大延迟间隔时间，默认30秒，避免multiplier过大引起无限期等待
        max-interval: 600000
        # 重试次数
        max-attempts: 6
```

1. 对于不依赖调用方法返回值的方法上添加 @PmRetry 即可, 该方法会在重试失败后入库并抛出异常，走定时任务再次重试;
2. 对于依赖返回结果的方法可使用 @PmRetry(isRecover = false), 该方法不会在重试失败后入库，不会走定时任务，失败后会抛出异常;

#### 6 示例

```java
    @PmReTry(maxIntrval = 30 * 1000, maxAttempts = 5, intervarTime = 2000, multiplier = 1.5)
public boolean test(){
        long l=RandomUtils.nextLong(0,100);
        if(l>94){
        return true;
        }
        throw new BusinessException(-1);
        }
```

效果:

```xml
2022-12-13 09:40:02.741 [scheduling-1] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 66 | Retry for the 1 time
        2022-12-13 09:40:04.743 [scheduling-1] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 66 | Retry for the 2 time
        2022-12-13 09:40:07.747 [scheduling-1] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 66 | Retry for the 3 time
        2022-12-13 09:40:12.254 [scheduling-1] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 66 | Retry for the 4 time
        2022-12-13 09:40:19.015 [scheduling-1] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 66 | Retry for the 5 time
```

说明 ：<br>
第一次请求为初始请求，<br>
第二次请求为2s后，intervarTime = 2000
第三次请求为3s后，为上一次请求间隔时间 * multiplier = 1.5
第四次请求为4.5s后，为上一次请求间隔时间 * multiplier = 1.5
...
最多请求不超过5次，最大间隔时间不超出30s

```log
  retry:
    # 重试间隔时间,默认毫秒,
    interval-time: 2000
    # 重试延迟的倍数，比如设置interval-time=3000，multiplier=2时，第一次重试为3秒后，第二次为6(3x2)秒，第三次为12(6x2)秒,但最长延迟时间不会超出 maxInterval,最大延迟次数不会超出maxAtt
    multiplier: 3
    # 最大延迟间隔时间，默认30秒，避免multiplier过大引起无限期等待
    max-interval: 120000
    # 重试次数
    max-attempts: 6

2022-12-06 09:57:02.861 [http-nio-8010-exec-2] DEBUG com.cscec3b.iti.projectmanagement.server.config.CusAuthByTokenHandler | 69 | enter the token interceptor : GET /cpm/common-data/retry
2022-12-06 09:57:02.862 [http-nio-8010-exec-2] DEBUG com.cscec3b.iti.projectmanagement.server.config.CusAuthByTokenHandler | 73 | this api is custom exclude path
2022-12-06 09:57:02.875 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 1 time -------------------->init
2022-12-06 09:57:04.885 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 2 time -------------------->2s
2022-12-06 09:57:10.897 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 3 time -------------------->2s*3x
2022-12-06 09:57:28.901 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 4 time -------------------->6s*3x
2022-12-06 09:58:22.905 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 5 time -------------------->18s*3x
2022-12-06 10:00:22.911 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 46 | Retry for the 6 time  -------------------->54s*3x >120s 则== 120s

@PmReTry(maxIntrval = 30, maxAttempts = 5, intervarTime = 2000, multiplier = 1.5)
2022-12-09 16:06:11.936 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 67 | Retry for the 1 time  --> init
2022-12-09 16:06:13.943 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 67 | Retry for the 2 time  --> 2s
2022-12-09 16:06:16.946 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 67 | Retry for the 3 time  -->2s*1.5
2022-12-09 16:06:21.449 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 67 | Retry for the 4 time --> 3s*1.5
2022-12-09 16:06:28.214 [http-nio-8010-exec-2] WARN  com.cscec3b.iti.retry.aop.RetryRecoverAspect | 67 | Retry for the 5 time --> 4.5s*1.5
```