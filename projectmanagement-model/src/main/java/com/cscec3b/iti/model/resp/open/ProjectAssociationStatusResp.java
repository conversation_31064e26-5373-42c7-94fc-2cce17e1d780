package com.cscec3b.iti.model.resp.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 项目关联状态 RESP
 *
 * <AUTHOR>
 * @date 2024/12/20
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectAssociationStatusResp", description = "项目关联状态 RESP")
public class ProjectAssociationStatusResp implements Serializable {

    /**
     * 业务系统名称
     */
    @ApiModelProperty(value = "业务系统名称")
    private String subName;

    /**
     * 业务系统id
     */
    @ApiModelProperty(value = "业务系统id")
    private Long subId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String cmpProjectKey;
    /**
     * 项目名称(财商系统)
     */
    @ApiModelProperty(value = "项目名称(财商系统)")
    private String projectFinanceName;
    /**
     * 项目编码(财商)
     */
    @ApiModelProperty(value = "项目编码(财商)")
    private String projectFinanceCode;
    /**
     * 云枢 id
     */
    @ApiModelProperty(value = "云枢 id")
    private String yunshuOrgId;

    /**
     * 业务状态 -1: 废弃/弃用/删除 ；1 正常-无业务数据； 2 正常-有业务数据
     */
    @ApiModelProperty(value = "业务状态")
    private Integer businessStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
