package com.cscec3b.iti.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 项目档案响应值
 * <AUTHOR>
 * @date 2023/09/15 15:17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectArchiveResp", description = "项目档案响应值")
public class ProjectArchiveResp extends ProjectBaseArchive implements Serializable {


    /**
     * 项目中心基本信息
     */
    @ApiModelProperty(value = "项目中心基本信息")
    private CpmSegment cpmSegment;

    /**
     * 市场营销版块信息
     */
    @ApiModelProperty(value = "市场营销版块信息")
    private MarketingSegment marketingSegment;

    /**
     * 智慧工地版块信息
     */
    @ApiModelProperty(value = "智慧工地版块信息")
    private SmartSiteSegment smartSiteSegment;

    /**
     * 财商版块信息
     */
    @ApiModelProperty(value = "财商版块信息")
    private FinanceSegment financeSegment;

    /**
     * 商务版块信息
     */
    @ApiModelProperty(value = "商务版块信息")
    private BusinessSegment businessSegment;

    /**
     * 供应链版块信息
     */
    @ApiModelProperty(value = "供应链版块信息")
    private SupplySegment supplySegment;

    /**
     * 工程项目版块信息
     */
    @ApiModelProperty(value = "工程项目版块信息")
    private ProjectBindingSegment engineProjectSegment;

    public ProjectArchiveResp() {
    }
    
}
