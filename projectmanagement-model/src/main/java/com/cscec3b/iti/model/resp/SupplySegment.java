package com.cscec3b.iti.model.resp;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description SupplySegment
 * @date 2023/09/16 14:47
 */
@Data
@ApiModel(value = "SupplySegment", description = "项目信息分发-供应链数据部分")
public class SupplySegment implements Serializable {

    /**
     * 云筑网（集采）项目编码字段
     */
    @ApiModelProperty( value = "云筑网（集采）项目编码字段")
    private String yzwProjectId;

}
