package com.cscec3b.iti.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;

/**
 * 施工项目换绑事件数据载体
 *
 * <AUTHOR>
 * @date 2025/01/20
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectTransferSegment", description = "施工项目换绑事件数据载体")
public class ProjectBindingSegment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 施工项目ID
     */
    @ApiModelProperty(value = "施工项目ID")
    private Long projectId;

    /**
     * 施工项目标识
     */
    @ApiModelProperty(value = "施工项目标识")
    private String cpmProjectKey;

    /**
     * 施工项目名称
     */
    @ApiModelProperty(value = "施工项目名称")
    private String cpmProjectName;

    /**
     * 财商项目编码
     */
    @ApiModelProperty(value = "财商项目编码")
    private String projectFinanceCode;

    /**
     * 是否主施工项目
     */
    @ApiModelProperty(value = "是否主施工项目")
    private Integer mainStandardProject;

    /**
     * 原工程项目ID
     */
    @ApiModelProperty(value = "原工程项目ID")
    private Long sourceEngineeringProjectId;

    /**
     * 原工程项目标识
     */
    @ApiModelProperty(value = "原工程项目标识")
    private String sourceEngineeringKey;

    /**
     * 原工程项目名称
     */
    @ApiModelProperty(value = "原工程项目名称")
    private String sourceEngineeringName;

    /**
     * 原工程项目编码
     */
    @ApiModelProperty(value = "原工程项目编码")
    private String sourceEngineeringCode;

    /**
     * 目标工程项目ID
     */
    @ApiModelProperty(value = "目标工程项目ID")
    private Long targetEngineeringProjectId;

    /**
     * 目标工程项目标识
     */
    @ApiModelProperty(value = "目标工程项目标识")
    private String targetEngineeringKey;

    /**
     * 目标工程项目编码
     */
    @ApiModelProperty(value = "目标工程项目编码")
    private String targetEngineeringCode;

    /**
     * 目标工程项目名称
     */
    @ApiModelProperty(value = "目标工程项目名称")
    private String targetEngineeringName;

    /**
     * 换绑操作时间戳
     */
    @ApiModelProperty(value = "换绑操作时间戳")
    private Long transferTimestamp = Instant.now().toEpochMilli();

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String operatorName;
}
