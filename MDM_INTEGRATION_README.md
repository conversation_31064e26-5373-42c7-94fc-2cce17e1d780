# MDM集成标准实现文档

## 概述

本文档描述了项目管理系统与MDM（主数据管理）系统的集成实现，基于中建三局MDM集成标准。

## 集成接口

根据MDM集成标准，实现了以下三个接口：

### 1. 工程项目校验接口 (GCXM_JS_ZJSJ_JY)
- **接口地址**: `/esb-api/GCXMZSJ/GCXM_JS_ZJSJ_JY`
- **功能**: 校验工程项目主数据
- **方法**: POST
- **用途**: 在推送数据前进行数据校验

### 2. 工程项目推送接口 (GCXM_JS_ZJSJ)
- **接口地址**: `/esb-api/GCXMZSJ/GCXM_JS_ZJSJ`
- **功能**: 推送工程项目主数据到MDM
- **方法**: POST
- **用途**: 新增或更新工程项目数据

### 3. 工程项目分发接口 (GCXM_ESB_010048)
- **接口地址**: `/esb-api/GCXMZSJ/GCXM_ESB_010048`
- **功能**: 分发工程项目主数据到其他系统
- **方法**: POST
- **用途**: 将MDM数据分发到业务系统

## 核心组件

### 1. 配置类
- **MDMOpenApiFeignConfig**: Feign客户端配置，包含请求头、重试策略等
- **MDMProperties**: MDM配置属性，支持动态刷新

### 2. 实体类
- **MdmPushEntity**: MDM推送数据实体，包含所有必需字段
- **MDMEngineProjectPushDto**: MDM推送请求DTO
- **MDMApiResponse**: MDM响应结果DTO

### 3. 服务类
- **MDMOpenApiFeign**: Feign客户端接口
- **MDMService**: MDM业务服务接口
- **MDMServiceImpl**: MDM业务服务实现

### 4. 工具类
- **MDMDataConverter**: 数据转换工具，支持Project和BidApproval转换

### 5. 控制器
- **MDMController**: 提供REST API接口

## 使用方法

### 1. 配置文件设置

```yaml
cscec:
  mdm:
    push: http://mdm-uat.cscec.com/gateway
    enabled: true
    connectTimeout: 30000
    readTimeout: 60000
    retryCount: 3
    retryInterval: 2000
    validateEnabled: true
    pushEnabled: true
    distributeEnabled: true
```

### 2. API调用示例

#### 校验工程项目数据
```http
POST /mdm/validate/{projectId}
```

#### 推送工程项目数据
```http
POST /mdm/push/{projectId}
```

#### 分发工程项目数据
```http
POST /mdm/distribute/{projectId}
```

#### 同步工程项目数据（校验+推送）
```http
POST /mdm/sync/{projectId}
```

### 3. 编程方式调用

```java
@Autowired
private MDMService mdmService;

// 校验项目数据
MDMApiResponse validateResponse = mdmService.validateEngineProjectById(projectId);

// 推送项目数据
MDMApiResponse pushResponse = mdmService.pushEngineProjectById(projectId);

// 分发项目数据
MDMApiResponse distributeResponse = mdmService.distributeEngineProjectById(projectId);
```

## 数据映射

### 项目实体字段映射

| 项目字段 | MDM字段 | 说明 |
|---------|---------|------|
| cpmProjectKey | code | 项目唯一标识 |
| cpmProjectName | name | 项目名称 |
| cpmProjectAbbreviation | stname | 项目简称 |
| regionCode | offRegion | 行政区域 |
| projectContractorType | ownerType | 项目承接主体类型 |
| projectCategory | type | 项目分类 |
| fabricated | isAssType | 是否装配式 |
| contractMode | conType | 承建模式 |
| workerBeginTime | planStartDate | 计划开工日期 |
| workerEndTime | planEndDate | 计划竣工日期 |
| lng | longitude | 经度 |
| lat | latitude | 纬度 |

### 响应状态码

| 状态码 | 说明 |
|-------|------|
| 0 | 成功 |
| 1 | 失败 |

## 错误处理

系统提供完整的错误处理机制：

1. **网络异常**: 自动重试机制
2. **数据校验失败**: 返回详细错误信息
3. **业务异常**: 抛出FrameworkException

## 日志记录

所有MDM接口调用都会记录详细日志：
- 请求参数
- 响应结果
- 错误信息
- 执行时间

## 注意事项

1. **数据完整性**: 确保必填字段不为空
2. **日期格式**: 统一使用yyyy-MM-dd格式
3. **编码规范**: 使用UTF-8编码
4. **超时设置**: 合理设置连接和读取超时时间
5. **重试机制**: 网络异常时自动重试

## 扩展说明

系统设计支持以下扩展：

1. **多数据源**: 支持Project和BidApproval两种数据源
2. **配置化**: 所有配置项支持动态刷新
3. **监控集成**: 可集成监控系统进行性能监控
4. **异步处理**: 可扩展为异步推送模式

## 测试建议

1. **单元测试**: 测试数据转换逻辑
2. **集成测试**: 测试与MDM系统的连通性
3. **性能测试**: 测试大批量数据推送性能
4. **异常测试**: 测试各种异常场景的处理
