package com.cscec3b.iti.projectmanagement.api;

import java.util.List;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.QueryUsersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.UcUserByRoleReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.org.UserAndOrgDto;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.ExecuteUnitTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.org.OrgLabelResp;
import com.g3.org.api.dto.PageDTO;
import com.g3.org.api.dto.uc.UcUserDTO;
import com.g3.role.respose.RoleResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 云枢组织相关控制器
 *
 * <AUTHOR>
 * @date 2023/08/12 22:56
 **/

public interface IOrgController {

	/**
	 * 路径
	 */
	String PATH = "/lean-build/org";


	/**
	 * 钻取方式获取组织树
	 *
	 * @param parentId    parentid 父节点id
	 * @param isEntities 是否实体组织
	 * @return {@link GenericityResponse }<{@link List }<{@link ExecuteUnitTreeResp }>>
	 * <AUTHOR>
	 * @date 2023/08/25
	 */
	@GetMapping("/tree")
	@ApiOperation(value = "钻取方式获取组织树")
	GenericityResponse<List<ExecuteUnitTreeResp>> getTree(
			@ApiParam(value = "根节点id ") @RequestParam(required = false) String parentId,
			@ApiParam(value = "是否实体组织") @RequestParam(defaultValue = "false") boolean isEntities);


	/**
	 * 修订时钻取方式获取组织树
	 *
	 * @param parentId   父节点id
	 * @param isEntities 是否实体组织
	 * @return {@link GenericityResponse }<{@link List }<{@link ExecuteUnitTreeResp }>>
	 * <AUTHOR>
	 * @date 2023/08/21
	 */
	@GetMapping("/tree/revision")
	@ApiOperation(value = "修订时钻取方式获取组织树")
	GenericityResponse<List<ExecuteUnitTreeResp>> getAllTree(
			@ApiParam(value = "根节点id ") @RequestParam(required = false) String parentId,
			@ApiParam(value = "是否实体组织") @RequestParam(defaultValue = "false") boolean isEntities);


	/**
	 * 分域模糊搜索组织
	 *
	 * @param parentIdPath 父idPath
	 * @param name         名字
	 * @param abbreviation 缩写
	 * @param isEntities   是否实体
	 * @return {@link GenericityResponse }<{@link List }<{@link ExecuteUnitTreeResp }>>
	 * <AUTHOR>
	 * @date 2023/08/25
	 */
	@GetMapping("/fuzzy-search/org")
	@ApiOperation(value = "分域模糊搜索组织", notes = "项目列表——分域模糊搜索组织接口")
	GenericityResponse<List<ExecuteUnitTreeResp>> fuzzySearchOrg(
			@ApiParam(value = "组织idPath ") @RequestParam String parentIdPath,
			@ApiParam(value = "组织全称名称 ") @RequestParam(required = false) String name,
			@ApiParam(value = "组织简称名称 ") @RequestParam(required = false) String abbreviation,
			@ApiParam(value = "是否实体组织") @RequestParam(defaultValue = "false") boolean isEntities);

    /**
	 * 实时从云柩获取智慧工地组织树
     *
     * @param parentId   上级节点
     * @param isEntities 是否实体组织
     * @return {@link GenericityResponse}<{@link List}<{@link ExecuteUnitTreeResp}>>
     */
    @GetMapping("/tree/revision/real-time")
	@ApiOperation(value = "实时获取组织树")
    GenericityResponse<List<ExecuteUnitTreeResp>> getTreeRevisionRealTime(
            @ApiParam(value = "根节点id ") @RequestParam(required = false) String parentId,
            @ApiParam(value = "是否实体组织") @RequestParam(defaultValue = "false") boolean isEntities);


	@GetMapping("/tree-node/{orgId}")
	@ApiOperation(value = "通过组织id获取本下树节点")
	GenericityResponse<ExecuteUnitTreeResp> getTreeNodeByOrgId(
			@ApiParam(value = "组织id ") @PathVariable String orgId);

	@GetMapping("/role/page")
	@ApiOperation(value = "获取角色分页列表")
	GenericityResponse<PageDTO<RoleResp>> executeForPageRole(Integer roleType, Integer page, Integer size);

	@PostMapping("/role/users")
	@ApiOperation(value = "获取组织下用户列表")
    GenericityResponse<Page<UcUserDTO>> getUserByOrgAndRoles(@RequestBody @Validated UcUserByRoleReq roleReq);

    @PostMapping("/user/page")
    @ApiOperation(value = "获取部门下人员信息")
    GenericityResponse<Page<UserAndOrgDto>>
        getUserListByDepartment(@ApiParam("部门id及关键字查询参数 ") @Validated @RequestBody QueryUsersReq req);

    @GetMapping("/labels")
    @ApiOperation(value = "获取组织标签")
    GenericityResponse<List<OrgLabelResp>> getLabels();
}
