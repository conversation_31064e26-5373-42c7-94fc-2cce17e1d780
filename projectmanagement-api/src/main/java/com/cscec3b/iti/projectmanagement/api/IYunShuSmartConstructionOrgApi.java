package com.cscec3b.iti.projectmanagement.api;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.request.YunShuSmartConstructionOrgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.YunShuSmartConstructionSaveResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;


/**
 * 云枢与智慧工地组织树
 * <AUTHOR>
 * @date 2023/9/14 15:18
 */
public interface IYunShuSmartConstructionOrgApi {

    String PATH = "/yunshu-smart-construction-org";

    /**
     * 根据部门id查询部门信息
     * @param departmentId 
     * @return
     * @date 2023/10/8
     */
    @PostMapping("/findByDepartmentId")
    @ApiOperation(value = "根据部门id查询部门信息")
    GenericityResponse<YunShuSmartConstructionOrgResp> findByDepartmentId(@ApiParam("根据部门id查询部门信息") @RequestParam(value = "departmentId")  String departmentId);

    /**
     * 新增云枢与标准组织映射关系
     * @param req 
     * @return
     * @date 2023/10/8
     */
    @PutMapping("/smartSave")
    @ApiOperation(value = "新增云枢与标准组织映射关系")
    GenericityResponse<YunShuSmartConstructionSaveResp> smartSave(@ApiParam("新增云枢与标准组织映射关系") @RequestBody YunShuSmartConstructionOrgReq req);

}
