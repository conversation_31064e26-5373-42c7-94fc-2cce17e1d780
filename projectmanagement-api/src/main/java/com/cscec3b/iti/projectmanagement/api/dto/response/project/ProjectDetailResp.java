package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 项目详情——项目信息
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/31 14:45
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectDetailResp", description = "项目详情信息")
public class ProjectDetailResp {

    @ApiModelProperty(value = "项目主键")
    private Long id;

    /**
     * 所属部门id（项目部/指挥部），长度与类型与UC保持一致
     */
    @ApiModelProperty(value = "所属部门id（项目部/指挥部），长度与类型与UC保持一致", position = 1)
    private String projectDeptId;

    /**
     * 所属部门类型（项目部/指挥部），长度与类型与UC保持一致
     */
    @ApiModelProperty(value = "所属部门类型（项目部/指挥部），长度与类型与UC保持一致", position = 2)
    private Integer projectDeptType;

    /**
     * 所属部门名称
     */
    @ApiModelProperty(value = "所属部门名称，长度与类型与UC保持一致", position = 3)
    private String projectDeptName;

    /**
     * 项目所属项目部/指挥部的idPath
     */
    @ApiModelProperty(value = "项目所属项目部/指挥部的idPath")
    private String projectDeptIdPath;

    /**
     * 0：立项中 1：完成立项
     */
    @ApiModelProperty(value = "0：立项中 1：完成立项", position = 4)
    private Integer projectStatus;

    /**
     * 独立合同ID
     */
    @ApiModelProperty(value = "独立合同ID", position = 5)
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2补充协议；3局内分包合同
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同", position = 6)
    private Integer independentContractType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 7)
    private Long createAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", position = 8)
    private Long updateAt;

    /**
     * 项目备注
     */
    @ApiModelProperty(value = "项目备注", position = 9)
    private String projectRemark;

    /**
     * 是否生态敏感区项目
     */
    @ApiModelProperty(value = "是否生态敏感区项目", position = 10)
    private Boolean isEcologySensitive;

    /**
     * 是否边小远散项目
     */
    @ApiModelProperty(value = "是否边小远散项目", position = 11)
    private Boolean isEdgeSmall;

    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别", position = 12)
    private String projectLevel;

    /**
     * 实际进场日期
     */
    @ApiModelProperty(value = "实际进场日期", position = 13)
    private Long realEnterTime;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期", position = 14)
    private Long workEndTime;

    /**
     * 五方主体验收日期（实际通车时间）
     */
    @ApiModelProperty(value = "五方主体验收日期（实际通车时间）", position = 15)
    private Long realOpenTrafficTime;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 16)
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话 项目中心
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话 项目中心", position = 17)
    private String contactPersonMobile;

    /**
     * 现场业主代表姓名
     */
    @ApiModelProperty(value = "现场业主代表姓名", position = 18)
    private String sceneOwnerRepresentName;

    /**
     * 现场业主代表职务
     */
    @ApiModelProperty(value = "现场业主代表职务", position = 19)
    private String sceneOwnerRepresentDuty;

    /**
     * 现场业主代表联系电话
     */
    @ApiModelProperty(value = "现场业主代表联系电话", position = 20)
    private String sceneOwnerRepresentPhone;

    /**
     * 工程参数json
     */
    @ApiModelProperty(value = "工程参数json", position = 21)
    private String engineerParameter;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号", position = 22)
    private String projectFinanceCode;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称", position = 23)
    private String projectFinanceName;

    /**
     * 财商立项项目简称（中文）
     */
    @ApiModelProperty(value = "财商立项项目简称（中文）", position = 24)
    private String projectFinanceAbbreviation;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 25)
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 26)
    private String marketProjectType;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）", position = 27)
    private String projectType;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 28)
    private String contractMode;

    /**
     * 行政区域（地理位置）
     */
    @ApiModelProperty(value = "行政区域（地理位置）", position = 29)
    private String region;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 30)
    private String projectAddress;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 31)
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 32)
    private String investors;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 33)
    private String businessType;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额（元）", position = 34)
    private BigDecimal contractAmount;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 35)
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 36)
    private String supervisor;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理", position = 37)
    private String projectManager;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 38)
    private String customerLevel;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 39)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 40)
    private Long workerEndTime;

    /**
     * 总工期
     */
    @ApiModelProperty(value = "总工期", position = 41)
    private Integer countDays;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 42)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 43)
    private Long predictWorkEndTime;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 44)
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 45)
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 46)
    private String enterpriseType;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 47)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 48)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 49)
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体code", position = 49)
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 50)
    private String doUnit;

    /**
     * 含税合同总价（RMB）
     */
    @ApiModelProperty(value = "含税合同总价（RMB）", position = 51)
    private BigDecimal totalAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额", position = 52)
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 53)
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 54)
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 55)
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 56)
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 57)
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他
     */
    @ApiModelProperty(value = "其他", position = 58)
    private BigDecimal selfOtherAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 59)
    private BigDecimal projectTaxAmount;

    /**
     * 暂列金或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金或甲指分包金额", position = 60)
    private BigDecimal subcontractAmount;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 61)
    private String workerDateRewardPunish;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 62)
    private String workerRewardPunishAppoint;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 63)
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 64)
    private String issuerProject;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 65)
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 66)
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 67)
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 68)
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 69)
    private String safetyRewardPunishTerms;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 70)
    private String advancesFlag;

    /**
     * 进度款支付方式
     */
    @ApiModelProperty(value = "进度款支付方式", position = 71)
    private String advancesWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 72)
    private String payTypeNew;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 73)
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 74)
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 75)
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 76)
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 77)
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 78)
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 79)
    private String warrantyPremiumWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 80)
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 81)
    private String guaranteeWay;

    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 82)
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 83)
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于80%
     */
    @ApiModelProperty(value = "付款比例是否低于80%", position = 84)
    private String payRateLessEightyFlag;

    /**
     * 支付节点是否超2个月
     */
    @ApiModelProperty(value = "支付节点是否超2个月", position = 85)
    private String nodeMoreTwoMonthFlag;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号")
    private String projectCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称")
    private String projectAbbreviation;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地")
    private String projectBelong;

    /**
     * A8项目编码
     */
    @ApiModelProperty(value = "工程属地")
    private String a8ProjectCode;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位")
    private String executeUnit;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath")
    private String executeUnitIdPath;

    /**
     * 是否创建指挥部:Y是 N否
     */
    @ApiModelProperty(value = "是否创建指挥部:Y是 N否")
    private String isCreateHead;

    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统，1:市场营销；2:特殊立项")
    private Integer sourceSystem;

    @ApiModelProperty(value = "所属部门名称简称")
    private String projectDeptAbbreviation;

    @ApiModelProperty(value = "执行单位简称")
    private String executeUnitAbbreviation;

    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;
     */
    @ApiModelProperty(value = "施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;

    /**
     * 施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;
     */
    @ApiModelProperty(value = "施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    /**
     * 施工项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "施工项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;

    @ApiModelProperty(value = "项目地址")
    private String smartProjectAddress;

    @ApiModelProperty(value = "项目规模")
    private String projectScale;

    @ApiModelProperty(value = "承包模式（智慧工地）")
    private String smartContractModel;

    @ApiModelProperty(value = "经度")
    private String lng;

    @ApiModelProperty(value = "纬度")
    private String lat;

    @ApiModelProperty(value = "质量目标")
    private String qualityTask;

    @ApiModelProperty(value = "安全目标")
    private String securityTask;

    @ApiModelProperty(value = "项目唯一标识 含义：接收市场营销立项通知或特殊立项发起后生成.P+年月日+四位流水号")
    private String cpmProjectKey;

    @ApiModelProperty(value = "云筑网（集采）项目编码")
    private String yzwProjectId;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty(value = "局名义项目类型")
    private Integer bureauNominalProjectType;

    @ApiModelProperty(value = "总包项目id")
    private Long generalContractProjectId;

    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    @ApiModelProperty(value = "战新业务类型")
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "财商业务板块")
    private String financialBusinessSegment;
    /**
     * 竣工备案日期
     */
    @ApiModelProperty(value = "竣工备案日期")
    private Long recordDate;

    /**
     * 预付款比例
     */
    @ApiModelProperty(value = "预付款比例")
    private String advancesRate;

    /**
     * 标准项目名称，含义：工程名称更新和财商名称更新均更新此字段（工程名称仅在首次新增时使用）
     */
    @ApiModelProperty("项目名称，含义：工程名称更新和财商名称更新均更新此字段（工程名称仅在首次新增时使用）")
    private String cpmProjectName;

    /**
     * 项目简称: 财商未立项时取工程简称，财商立项后为财商简称
     */
    @ApiModelProperty(value = "项目简称: 财商未立项时取工程简称，财商立项后为财商简称")
    private String cpmProjectAbbreviation;

    /**
     * 智慧工地项目云枢组织TreeID
     */
    @ApiModelProperty(value = "智慧工地项目云枢组织TreeID")
    private String yunshuTreeId;

    /**
     * 智慧工地项目云枢组织queryCode
     */
    @ApiModelProperty(value = "智慧工地项目云枢组织queryCode")
    private String yunshuQueryCode;

    /**
     * 智慧工地项目直接上级云枢组织ID
     */
    @ApiModelProperty(value = "智慧工地项目直接上级云枢组织ID")
    private String yunshuParentOrgId;

    /**
     * 智慧工地项目直接上级全称
     */
    @ApiModelProperty(value = "智慧工地项目直接上级全称")
    private String yunshuParentOrgName;

    /**
     * 智慧工地项目直接上级treeID
     */
    @ApiModelProperty(value = "智慧工地项目直接上级treeID")
    private String yunshuParentTreeId;

    /**
     * 项目效果图
     */
    @ApiModelProperty(value = "项目效果图")
    private String effectPic;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financeBusinessSegment;

    @ApiModelProperty(value = "市场业务板块")
    private String marketingBusinessSegment;


    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "市场业务板块codePath")
    private String marketingBusinessSegmentCodePath;


    /**
     * 财商业务板块codepath
     */
    @ApiModelProperty(value = "财商业务板块codepath")
    private String financeBusinessSegmentCodePath;

    /**
     * CPM 业务板块代码路径
     */
    @ApiModelProperty(value = "项目中心业务板块", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegmentCodePath;

    /**
     * CPM 业务部门
     */
    @ApiModelProperty(value = "项目中心业务板块名称", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegment;

    /**
     * 财商承包方式 11 - 外部总承包/独立承包 12 - 外部总承包/联合体 2 - 外部分包 3 - 内部总承包 4 - 内部分包
     */
    @ApiModelProperty(value = "承包方式: 11 - 外部总承包/独立承包 12 - 外部总承包/联合体 2 - 外部分包 3 - 内部总承包 4 - 内部分包")
    private String financeProjectSource;

    /**
     * 标准项目主收入合同签约组织MDM编码
     */
    @ApiModelProperty(value = "标准项目主收入合同签约组织MDM编码")
    private String financeSignOrgCode;

    /**
     * 标准项目主收入合同签约组织机构
     */
    @ApiModelProperty(value = "标准项目主收入合同签约组织机构")
    private String financeSignOrgName;
}
