package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 项目基础信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@ApiModel(value = "ProjectBasicInfo对象", description = "项目基础信息表")
public class ProjectBasicInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 立项id
     */
    @ApiModelProperty(value = "立项id")
    private Integer projectId;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String projectAbbreviation;

    /**
     * a8no
     */
    @ApiModelProperty(value = "a8no")
    private String projectA8no;

    /**
     * 工程地点
     */
    @ApiModelProperty(value = "工程地点")
    private String projectAddress;

    /**
     * 工程地点中文
     */
    @ApiModelProperty(value = "工程地点中文名称")
    private String projectAddressCn;

    /**
     * 工程分类
     */
    @ApiModelProperty(value = "工程分类")
    private String projectType;

    /**
     * 工程结构
     */
    @ApiModelProperty(value = "工程结构")
    private String projectStructure;

    /**
     * 施工范围
     */
    @ApiModelProperty(value = "施工范围")
    private String projectRange;

    /**
     * 施工单位
     */
    @ApiModelProperty(value = "施工单位")
    private String projectCompany;

    /**
     * 项目概述
     */
    @ApiModelProperty(value = "项目概述")
    private String projectDescription;

    /**
     * 劳务模式
     */
    @ApiModelProperty(value = "劳务模式")
    private String serviceModel;

    /**
     * 项目规模
     */
    @ApiModelProperty(value = "项目规模")
    private String projectScale;

    /**
     * 施工参数
     */
    @ApiModelProperty(value = "施工参数")
    private String projectParameter;

    /**
     * 项目特征
     */
    @ApiModelProperty(value = "项目特征")
    private String projectFeature;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式")
    private String contractModel;

    /**
     * 装配内容
     */
    @ApiModelProperty(value = "装配内容")
    private String assembledInfo;

    /**
     * 装配率
     */
    @ApiModelProperty(value = "装配率")
    private String assembledRatio;

    /**
     * 是否有装配项
     */
    @ApiModelProperty(value = "是否有装配项")
    private Byte hasAssembled;

    /**
     * 装配面积
     */
    @ApiModelProperty(value = "装配面积")
    private String assembledArea;

    @ApiModelProperty(value = "经度")
    private String lng;

    @ApiModelProperty(value = "纬度")
    private String lat;
    /**
     * 是否局重点
     */
    @ApiModelProperty(value = "是否局重点")
    private Byte isAdministrationCore;

    /**
     * 是否公司重点
     */
    @ApiModelProperty(value = "是否公司重点")
    private Byte isCompanyCore;

    /**
     * 是否区域标杆项目
     */
    @ApiModelProperty(value = "是否区域标杆项目")
    private Byte isCoreArea;

    /**
     * 是否边远散小项目
     */
    @ApiModelProperty(value = "是否边远散小项目")
    private Byte isEdgeArea;

    /**
     * 是否生态敏感区
     */
    @ApiModelProperty(value = "是否生态敏感区")
    private Byte isSensitiveArea;

    /**
     * 是否履约困难项目
     */
    @ApiModelProperty(value = "是否履约困难项目")
    private Byte isDeadbeat;

    /**
     * 是否是创新项目
     */
    @ApiModelProperty(value = "是否是创新项目")
    private Byte isInnovation;

    /**
     * 是否bm项目
     */
    @ApiModelProperty(value = "是否bm项目")
    private Byte isBm;

    /**
     * 是否jr项目
     */
    @ApiModelProperty(value = "是否jr项目")
    private Byte isJr;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}
