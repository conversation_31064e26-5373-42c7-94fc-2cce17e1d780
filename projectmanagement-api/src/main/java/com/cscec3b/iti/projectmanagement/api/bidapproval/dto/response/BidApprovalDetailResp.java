package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableLogic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 中标未立项详情
 *
 * <AUTHOR>
 * @date 2024/01/12
 */
@Data
@ApiModel("中标未立项详情")
public class BidApprovalDetailResp implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 原文件id
     */
    @ApiModelProperty(value = "原文件id")
    private Long belongId;

    /**
     * 立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation：局内部合同定案，internal_agreement
     * ：局内部补充协议
     */
    @ApiModelProperty(value = "立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation" +
            "：局内部合同定案，internal_agreement：局内部补充协议")
    private String type;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    /**
     * 局内部合同定案编号
     */
    @ApiModelProperty(value = "局内部合同定案编号")
    private String presentationCode;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号")
    private String projectCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 业主名称
     */
    @ApiModelProperty(value = "业主名称")
    private String customerName;

    /**
     * Y:国内，N：海外
     */
    @ApiModelProperty(value = "Y:国内，N：海外")
    private String projectBelong;

    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    private String region;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国别")
    private String country;

    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址")
    private String address;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司市场口径）2
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）2")
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4")
    private String projectType4;

    /**
     * 是否创建指挥部,Y:是，N：否
     */
    @ApiModelProperty(value = "是否创建指挥部,Y:是，N：否")
    private String createHead;

    /**
     * 独立性判断，Y：是，N：否，D：不予立项
     */
    @ApiModelProperty(value = "独立性判断，Y：是，N：否，D：不予立项")
    private String independentProject;


    /**
     * 是否是工程项目
     * Y：是，N：否
     */
    @ApiModelProperty(value = "独立性判断，Y：是，N：否，D：不予立项")
    private String engineeringProject;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人")
    private String submitPerson;

    /**
     * 发起人姓名
     */
    @ApiModelProperty(value = "发起人姓名")
    private String submitPersonName;

    /**
     * 复核人
     */
    @ApiModelProperty(value = "复核人")
    private String approvalPerson;

    /**
     * 复核人姓名
     */
    @ApiModelProperty(value = "复核人姓名")
    private String approvalPersonName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 挂接id
     */
    @ApiModelProperty(value = "挂接id")
    private Long associatedId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 是否删除：0： 未删除； 时间戳为删除时间
     */
    @ApiModelProperty(value = "是否删除：0： 未删除； 时间戳为删除时间")
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP() * 1000")
    private Long deleted;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段")
    private String extension;

    /**
     * 云枢执行单位id
     */
    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位code
     */
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位
     */
    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位idPath
     */
    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 项目中心id
     */
    @ApiModelProperty(value = "项目中心id")
    private Long cpmProjectId;

    /**
     * 前置文件id
     */
    @ApiModelProperty(value = "前置文件id")
    private Long preFileId;

    /**
     * 立项步骤列表
     */
    @ApiModelProperty(value = "立项步骤列表")
    private String stepList;

    /**
     * 立项项目类型
     */
    @ApiModelProperty(value = "立项项目类型")
    private Long approvalTypeId;


    /**
     * 当前步骤id
     */
    @ApiModelProperty(value = "当前步骤id")
    private Integer currentStepNo;

    /**
     * 项目中心项目名称
     */
    @ApiModelProperty("项目中心项目名称")
    private String cpmProjectName;

    /**
     * 项目中心项目标识
     */
    @ApiModelProperty("项目中心项目标识")
    private String cpmProjectKey;

    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String procInstId;

    /**
     * 流程发起人
     */
    @ApiModelProperty("流程发起人")
    private String workflowStarter;

    /**
     * 单据状态
     */
    @ApiModelProperty("单据状态: free: 自由态；approving: 审批中；approved: 审批通过；nopass:审批不通过；commit: 提交态；")
    private String billState;

    /**
     * 项目部名称
     */
    @ApiModelProperty("项目部名称")
    private String deptName;

    /**
     * 项目部简称
     */
    @ApiModelProperty("项目部简称")
    private String deptAbbr;

    /**
     * 上级组织id
     */
    @ApiModelProperty("上级组织id")
    private String parentDeptId;

    /**
     * 上级组织名称
     */
    @ApiModelProperty("上级组织名称")
    private String parentDeptName;

    /**
     * 项目部创建类型
     */
    @ApiModelProperty("项目部创建类型 1：新增；0：挂接")
    private Integer deptCreateType;

    /**
     * 挂接组织id
     */
    @ApiModelProperty("挂接组织id")
    private String deptId;

    /**
     * 部门创建者
     */
    @ApiModelProperty("项目部创建者")
    private String deptCreateBy;
    
}
