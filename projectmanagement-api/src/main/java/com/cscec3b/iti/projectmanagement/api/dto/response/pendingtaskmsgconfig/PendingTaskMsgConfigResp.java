package com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.MsgNoticeTargetUserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("待办消息配置")
public class PendingTaskMsgConfigResp implements Serializable {

    /**
     * 组织 ID
     */
    @ApiModelProperty("组织 ID")
    private String orgId;

    /**
     * 云枢执行单元 ID
     */
    @ApiModelProperty("云枢执行单元 ID")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单元
     */
    @ApiModelProperty("云枢执行单元")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单元路径
     */
    @ApiModelProperty("云枢执行单元路径")
    private String yunshuExecuteUnitIdPath;

    /**
     * 云枢执行单元路径名
     */
    @ApiModelProperty("云枢执行单元路径名")
    private String yunshuExecuteUnitIdPathName;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 类型代码
     */
    @ApiModelProperty("业务场景类型代码")
    private String typeCode;

    /**
     * UC MSG 配置代码
     */
    @ApiModelProperty("UC MSG 配置代码")
    private String ucMsgConfigCode;

    /**
     * 消息变量
     */
    @ApiModelProperty("消息变量")
    private Map<String, String> payload;

    /**
     * 选择用户类型
     */
    @ApiModelProperty("选择用户类型")
    private Integer userChoseType;

    /**
     * 目标用户
     */
    @ApiModelProperty("目标用户")
    private List<MsgNoticeTargetUserInfo> targetUsers;

    /**
     * 应用链接
     */
    @ApiModelProperty("应用链接")
    private String appLink;

    /**
     * Web 链接
     */
    @ApiModelProperty("Web 链接")
    private String webLink;

    /**
     * 业务类型
     */
    @ApiModelProperty("业务类型")
    private String billType;

    /**
     * 业务类型名称
     */
    @ApiModelProperty("业务类型名称")
    private String billTypeName;

    /**
     * 重试周期
     */
    @ApiModelProperty("通知周期")
    private String retryCycle;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createAt;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Long updateAt;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

}
