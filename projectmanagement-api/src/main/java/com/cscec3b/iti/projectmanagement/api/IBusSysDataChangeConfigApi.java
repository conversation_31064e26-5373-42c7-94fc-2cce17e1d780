package com.cscec3b.iti.projectmanagement.api;


import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataChangeConfigResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataListResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业务系统数据
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
public interface IBusSysDataChangeConfigApi {

    String PATH = "/data-change-config";

    /**
     * 添加业务数据变更配置
     *
     * @param configReq {@link BusSysDataChangeConfigReq}
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @PostMapping
    @ApiOperation(value = "添加业务数据变更配置")
    GenericityResponse<Boolean> addDataChangeConfig(
            @ApiParam("业务数据变更配置") @RequestBody @Validated BusSysDataChangeConfigReq configReq);

    /**
     * 分页列表查询
     *
     * @param configReq 查询参数
     * @return {@link GenericityResponse}<{@link Page}<{@link BusSysDataChangeConfigResp}>>
     */
    @PostMapping("/list/page")
    @ApiOperation(value = "分页查询业务数据变更配置")
    GenericityResponse<Page<BusSysDataChangeConfigResp>> listPage(
            @ApiParam("业务数据变更配置") @RequestBody @Validated BusSysDataChangeConfigPageReq configReq);

    /**
     * 编辑
     *
     * @param editReq 配置信息
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @PutMapping
    @ApiOperation(value = "编辑业务数据变更配置")
    GenericityResponse<Boolean> editDataChangeConfig(@ApiParam(value = "业务数据变更配置") @RequestBody
    @Validated BusSysDataChangeConfigEditReq editReq);

    /**
     * 获取业务板块及字段信息
     *
     * @return {@link GenericityResponse}<{@link List}<{@link BusSysDataListResp}>>
     */
    @GetMapping("/bus-sys-data")
    @ApiOperation(value = "获取业务板块及字段信息")
    GenericityResponse<List<BusSysDataListResp>> getBusinessSystemData();

    /**
     * 删除业务数据变更配置
     *
     * @param id 业务数据变更配置ID
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除业务数据变更配置")
    GenericityResponse<Boolean> deleteDataChangeConfig(@PathVariable("id") Long id);
}
