package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import com.cscec3b.iti.projectmanagement.api.validation.annotation.EnumVal;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("中标未立项文件列表请求参数")
public class OpenProjectBidPageReq extends BasePage implements Serializable {

    /**
     * 执行单位名称
     */
    @JsonProperty("OBOrgID")
    @ApiModelProperty("执行单位组织id")
    @Length(min = 1, max = 64, message = "执行单位组织id长度必须在1-64之间")
    private String obOrgId;

    /**
     * 独立性状态
     * @deprecated 参考 {@link com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenProjectBidPageReq#projectApprovalStatus}
     */
    @Deprecated
    @ApiModelProperty(value = "独立性状态：1:独立,2:非独立,3:不予立项,0:未判断", notes = "1:独立,2:非独立,3:不予立项,0:未判断")
    @EnumVal(message = "独立性状态值不正确,要求值为{intValues}", intValues = {1, 2, 3, 0})
    private Integer status;

    /**
     * 项目立项状态 ( "0": 未判断；"D":不予立项;"N":挂接完成;"Y0":独立性判断完成;"Y1":立项完成;)
     */
    @ApiModelProperty(value = "项目立项状态 ( '0': 未判断；'D':不予立项;'N':挂接完成;'Y0':独立性判断完成;'Y1':立项完成;)",
            notes = "'0': 未判断；'D':不予立项;'N':挂接完成;'Y0':独立性判断完成;'Y1':立项")
    private Set<String> projectApprovalStatus;

    /**
     * 工程名称
     */
    @ApiModelProperty("工程名称")
    private String projectName;

    /**
     * 业主名称
     */
    @ApiModelProperty("业主名称")
    private String customerName;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty("是否创建指挥部: Y:是,N:否")
    @EnumVal(message = "是否创建指挥部值不正确，要求值为{staValues}", strValues = {"Y", "N"})
    private String createHead;

    /**
     * 省份
     */
    @ApiModelProperty("省份, 如:湖北")
    private String province;

    /**
     * 城市
     */
    @ApiModelProperty("城市, 如:武汉")
    private String city;

    /**
     * 排序规则： asc 升序 desc 降序
     */
    @ApiModelProperty(value = "入库时间(createAt)排序规则： asc 升序; desc 降序,默认desc ")
    @EnumVal(message = "排序规则值不正确,要求值为{strValues}", strValues = {"asc", "desc"})
    private String orderBy = "desc";


    /**
     * 入库开始时间
     */
    @ApiModelProperty(value = "入库时间范围-开始,", notes = "与入库时间范围-结束同时使用生效, 不得大于createAtEnd值 ")
    private Long createAtStart;

    /**
     * 入库结束时间
     */
    @ApiModelProperty(value = "入库时间范围-结束", notes = "与入库时间范围-开始同时使用生效, 不得小于createAtCreate值 ")
    private Long createAtEnd;

    /**
     * 文件类型 ( "1": 投标总结; "8": 补充协议 ; "3": 补充协议定案; "4": 局内部合同定案; "80":无合同续签补充协议；"5":局内部补充协议; "50":局内部无合同续签补充协议)
     */
    @ApiModelProperty(value = "文件类型 ( '1': 投标总结; '8': 补充协议 ; '3': 补充协议定案; '4': 局内部合同定案; '80':无合同续签补充协议；'5':局内部补充协议; " +
            "'50':局内部无合同续签补充协议)")
    private Set<String> scopeType;


}
