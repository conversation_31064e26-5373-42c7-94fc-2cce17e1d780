
package com.cscec3b.iti.projectmanagement.api.dto.response.task;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "TaskResp", description = "任务响应数据")
public class TaskResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "关联Id")
    private Long relationId;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务类型")
    private String taskType;

    @ApiModelProperty(value = "状态(0:待办;1:在办;2:已办)")
    private Integer status;

    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Long initTime;

    @ApiModelProperty(value = "完成时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Long finishTime;
}

