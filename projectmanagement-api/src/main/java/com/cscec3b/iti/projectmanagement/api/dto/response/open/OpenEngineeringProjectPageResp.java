package com.cscec3b.iti.projectmanagement.api.dto.response.open;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "工程项目分页列表返回值")
public class OpenEngineeringProjectPageResp implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 工程项目标识
     */
    @ApiModelProperty(value = "工程项目标识")
    private String engineeringKey;
    /**
     * 工程项目名称
     */
    @ApiModelProperty(value = "工程项目名称")
    private String engineeringName;
    /**
     * 工程项目编码
     */
    @ApiModelProperty(value = "工程项目编码")
    private String engineeringCode;
    /**
     * 执行单位 ID
     */
    @ApiModelProperty(value = "执行单位组织id")
    private String executeUnitId;
    /**
     * 项目部门 ID
     */
    @ApiModelProperty(value = "项目部组织id")
    private String projectDeptId;

    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位名称")
    private String executeUnitName;

    /**
     * 项目部门名称
     */
    @ApiModelProperty(value = "项目部门名称")
    private String projectDeptName;

}
