package com.cscec3b.iti.projectmanagement.api.dto.response.investment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 投资文件信息
 */
@Data
@Accessors(chain = true)
public class InvestmentFileResp implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 文件编码
     */
    @ApiModelProperty(value = "文件编码")
    private String fileCode;

    /**
     * 源文件id
     */
    @ApiModelProperty(value = "源文件id")
    private Long belongId;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 执行单位组织id
     */
    @ApiModelProperty(value = "执行单位组织id")
    private String yunshuExecuteUnitId;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financialBusinessSegment;

    /**
     * 财商业务板块编码
     */
    @ApiModelProperty(value = "财商业务板块编码")
    private String financialBusinessSegmentIdPath;

    /**
     * 投标总结工程编码
     */
    @ApiModelProperty(value = "投标总结工程编码")
    private String bidSummaryProjectCode;

    /**
     * 合同定案合同编码
     */
    @ApiModelProperty(value = "合同定案合同编码")
    private String bidSummaryContractCode;

    /**
     * 独立合同ID
     */
    @ApiModelProperty(value = "独立合同ID")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；")
    private Integer independentContractType;

    /**
     * 独立性判断,Y:是独立,N:否
     */
    @ApiModelProperty(value = "独立性判断,Y:是独立,N:否")
    private String isIndependent;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 是否删除：0：未删除； 时间戳：删除时间
     */
    @ApiModelProperty(value = "是否删除：0：未删除； 时间戳：删除时间")
    private Long deleted;


    /**
     * 关联文件belongId
     */
    @ApiModelProperty(value = "关联文件belongId")
    private String relationFileBelongId;

    /**
     * 关联文件类型
     */
    @ApiModelProperty(value = "关联文件类型")
    private Integer relationFileType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 业务类型code
     */
    @ApiModelProperty(value = "业务类型code")
    private String businessTypeCode;

    /**
     * 计划总投资(亿元)
     */
    @ApiModelProperty(value = "计划总投资(亿元)")
    private BigDecimal planTotalInvestment;

    /**
     * 计划出资(亿元)
     */
    @ApiModelProperty(value = "计划出资(亿元)")
    private BigDecimal planContributionCapital;

    /**
     * 其中:自营建安(亿元)
     */
    @ApiModelProperty(value = "其中:自营建安(亿元)")
    private BigDecimal selfConstructionInstallationInvestment;

    /**
     * 股权比例(亿元)
     */
    @ApiModelProperty(value = "股权比例(亿元)")
    private String ownershipRatio;

    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4")
    private String projectType4;

    /**
     * 工程类型code(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)")
    private String projectTypeCode;

    /**
     * 工程类型code(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)2")
    private String projectType2Code;

    /**
     * 工程类型code(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)3")
    private String projectType3Code;

    /**
     * 工程类型code(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)4")
    private String projectType4Code;

    /**
     * 局标准分类1
     */
    @ApiModelProperty(value = "局标准分类1")
    private String standardType1;

    /**
     * 局标准分类2
     */
    @ApiModelProperty(value = "局标准分类2")
    private String standardType2;

    /**
     * 局标准分类3
     */
    @ApiModelProperty(value = "局标准分类3")
    private String standardType3;

    /**
     * 局标准分类4
     */
    @ApiModelProperty(value = "局标准分类4")
    private String standardType4;

    /**
     * 局标准分类1
     */
    @ApiModelProperty(value = "局标准分类1")
    private String standardType1Code;

    /**
     * 局标准分类2
     */
    @ApiModelProperty(value = "局标准分类2")
    private String standardType2Code;

    /**
     * 局标准分类3
     */
    @ApiModelProperty(value = "局标准分类3")
    private String standardType3Code;

    /**
     * 局标准分类4
     */
    @ApiModelProperty(value = "局标准分类4")
    private String standardType4Code;
}