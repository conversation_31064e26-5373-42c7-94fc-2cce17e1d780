package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotNull;

/**
 * @Description 待办消息推送三局通通知对象PortalMessageDto
 * <AUTHOR>
 * @Date 2022/11/21 16:10
 */
@Data
@Accessors(chain = true)
public class PmToPortalSaveMessageReq {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息接收人唯一用户标识（唯一工号 ）")
    @NotNull(message = "消息接收人唯一用户标识（唯一工号 ）不能为空")
    private String userId;

    @ApiModelProperty(value = "业务系统编号（由门户提供）")
    @NotNull(message = "业务系统编号（由门户提供）不能为空")
    private String systemId;

    @ApiModelProperty(value = "该系统调用此接口的口令（由门户提供）")
    @NotNull(message = "该系统调用此接口的口令（由门户提供）不能为空")
    private String passWord;

    @ApiModelProperty(value = "消息ID，用于跟踪和记录消息生命期，由消息发送方生成，建议格式：格式：Msg+systemID+工号+时间戳+6位随机数 ")
    @NotNull(message = "消息ID不能为空")
    private String msgId;

    @ApiModelProperty(value = "对于重复发送的消息，前一条重复性消息的msgId")
    private String preMsgId;

    @ApiModelProperty(value = "消息标题 ")
    @NotNull(message = "消息标题不能为空")
    private String title;

    @ApiModelProperty(value = "消息内容 ")
    @NotNull(message = "消息内容不能为空")
    private String content;

    @ApiModelProperty(value = "发送人姓名 ")
    @NotNull(message = "发送人姓名不能为空")
    private String senderName;

    @ApiModelProperty(value = "消息/公告类型")
    private String type;

    @ApiModelProperty(value = "消息发生日期时间（格式：\"yyyy-MM-dd hh:mm:ss\" 24小时制）  ")
    @NotNull(message = "消息发生日期时间不能为空")
    private String time;

    @ApiModelProperty(value = "通知目标列表：PC门户（1），移动门户（2），邮件（3），" +
            "微信（4），短信（5），企业微信（6）多个目标以逗号分隔，空缺缺省为门户 ")
    private String target;

    @ApiModelProperty(value = "移动端内容链接地址")
    private String appUrl;

    @ApiModelProperty(value = "PC内容链接地址 ")
    private String pcUrl;

    @ApiModelProperty(value = "预留字段")
    private String desc;
}
