package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.CreateProjectEventReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.CreateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersListReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.UpdateProjectEventReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.UpdateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventItem;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowNodeListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberPageResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目事件订阅配置管理
 * <AUTHOR>
 * @description IProjectEventApi
 * @date 2023/04/18 14:05
 */
public interface IProjectEventApi {
    /**
     * 路径
     */
    String PATH = "/event";

    /**
     * 分页查询系统订阅配置
     * @param subscribersReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/subscriber/page")
    @ApiOperation(value = "分页查询系统订阅配置", notes = "分页查询系统订阅配置")
    GenericityResponse<Page<SubscriberPageResp>> getSubscriberPages(@Validated @ApiParam("系统订阅配置查询对象") @RequestBody QuerySubscribersReq subscribersReq);

    /**
     * 新增系统订阅配置
     * @param subscriberReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/subscriber/create")
    @ApiOperation(value = "新增系统订阅配置", notes = "新增系统订阅配置")
    GenericityResponse<Boolean> createSubscriber(@Validated @ApiParam("创建系统订阅配置信息") @RequestBody CreateSubscriberReq subscriberReq);

    /**
     * 更新系统订阅配置
     * @param subscriberReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/subscriber/update")
    @ApiOperation(value = "更新系统订阅配置", notes = "更新系统订阅配置信息")
    GenericityResponse<Boolean> updateSubscriber(@Validated @ApiParam("更新系统订阅配置信息") @RequestBody UpdateSubscriberReq subscriberReq);

    /**
     * 变更系统订阅配置的状态
     * @param subscriberReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/subscriber/status")
    @ApiOperation(value = "变更系统订阅配置的状态", notes = "变更系统订阅配置的状态")
    GenericityResponse<Boolean> changeSubscriberStatus(@Validated @ApiParam("变更系统订阅配置的状态") @RequestBody UpdateSubscriberReq subscriberReq);

    /**
     * 获取所有事件配置的信息列表
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取所有事件配置的信息列表", notes = "获取所有事件配置的信息列表")
    GenericityResponse<List<ProjectEventItem>> getProjectEvents();

    /**
     * 新增系统事件配置
     * @param eventReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增系统事件配置", notes = "新增系统事件配置")
    GenericityResponse<Boolean>
        createEvent(@Validated @Valid @ApiParam("创建系统事件配置信息") @RequestBody CreateProjectEventReq eventReq);

    /**
     * 更新系统事件配置
     * @param eventReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/update")
    @ApiOperation(value = "更新系统事件配置", notes = "更新系统事件配置")
    GenericityResponse<Boolean>
        updateEvent(@Validated @ApiParam("更新系统事件配置信息") @RequestBody UpdateProjectEventReq eventReq);

    /**
     * 更新系统事件配置
     * @param eventId
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/status")
    @ApiOperation(value = "更新系统事件配置", notes = "更新系统事件配置")
    GenericityResponse<Boolean> updateEventStatus(
        @ApiParam(value = "订阅系统id", required = true) @RequestParam(value = "eventId") Integer eventId);

    /**
     * 配置可触发事件节点
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping("/nodeEnum")
    @ApiOperation(value = "配置可触发事件节点", notes = "配置可触发事件节点")
    GenericityResponse<List<ProjectFlowNodeListResp>> getFlowNodeEnumInfo();

    /**
     * 查询系统配置
     * @param subscribersReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     *
     *
     *
     */
    @PostMapping("/subscriber/list")
    @ApiOperation(value = "查询系统配置", notes = "查询系统配置")
    GenericityResponse<List<SubscriberListResp>> getSubscriberList(@Validated @ApiParam("系统订阅配置查询对象") @RequestBody QuerySubscribersListReq subscribersReq);
}
