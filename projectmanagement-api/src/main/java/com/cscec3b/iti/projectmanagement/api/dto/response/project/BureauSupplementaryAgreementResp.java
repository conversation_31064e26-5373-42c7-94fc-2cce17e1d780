package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 局补充协议职责 局补充协议职责 局补充协议职责 局补充协议职责 局内补充协议响应对象
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/31 15:26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BureauSupplementaryAgreementResp", description = "局内补充协议响应对象详情")
public class BureauSupplementaryAgreementResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人单位", position = 1)
    private String submitPerson;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "补充协议编号", position = 2)
    private String agreementCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 3)
    private String projectName;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 4)
    private String projectBelong;

    /**
     * 是否局重点项目
     */
    @ApiModelProperty(value = "是否局重点项目", position = 5)
    private String bureauProject;

    /**
     * 是否授权外
     */
    @ApiModelProperty(value = "是否授权外", position = 6)
    private String mandateForeign;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 7)
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 8)
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 9)
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 10)
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 11)
    private String contactPersonMobile;

    /**
     * 合同经办人
     */
    @ApiModelProperty(value = "合同经办人", position = 12)
    private String contractResponsiblePerson;

    /**
     * 是否纳入公司考核指标
     */
    @ApiModelProperty(value = "是否纳入公司考核指标", position = 13)
    private String companyAssessmentIndicators;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 14)
    private String businessType;

    /**
     * 补充协议金额
     */
    @ApiModelProperty(value = "补充协议金额 ", position = 15)
    private BigDecimal supplementAmount;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 16)
    private String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 17)
    private String contractForm;

    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 18)
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 19)
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 20)
    private String advancesFlag;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式", position = 21)
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @ApiModelProperty(value = "月进度付款比例", position = 22)
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 23)
    private String completedRate;

    /**
     * 竣工验收周期（月）
     */
    @ApiModelProperty(value = "竣工验收周期（月）", position = 24)
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 25)
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 26)
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 27)
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 28)
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 29)
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 30)
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @ApiModelProperty(value = "现金支付方式", position = 31)
    private String specificPayWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 32)
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 33)
    private String guaranteeWay;

    /**
     * 补充协议文本
     */
    @ApiModelProperty(value = "补充协议文本", position = 34)
    private String agreementUrl;

    /**
     * 合同主要条款对比表
     */
    @ApiModelProperty(value = "合同主要条款对比表", position = 35)
    private String contractTermUrl;

    /**
     * 法律意见书
     */
    @ApiModelProperty(value = "法律意见书", position = 36)
    private String lawUrl;

    /**
     * 局内补充协议编号
     */
    @ApiModelProperty(value = "局内补充协议编号", position = 37)
    private String bureauSupplementaryAgreementCode;

    /**
     * 独立合同ID
     */
    @ApiModelProperty(value = "独立合同ID", position = 38)
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2补充协议；3局内分包合同
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同", position = 39)
    private Integer independentContractType;

    /**
     * 接口幂等性校验字段
     */
    @ApiModelProperty(value = "所属源文件ID，接口幂等性校验字段", position = 40)
    private Long belongId;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 41)
    private String breakBottom;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体Code")
    private String signedSubjectCode;
}