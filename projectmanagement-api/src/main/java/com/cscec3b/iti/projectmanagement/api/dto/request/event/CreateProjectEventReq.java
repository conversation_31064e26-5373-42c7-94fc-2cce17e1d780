package com.cscec3b.iti.projectmanagement.api.dto.request.event;

import java.io.Serializable;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.cscec3b.iti.projectmanagement.api.dto.dto.EventBusinessColumn;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 项目信息事件表
 */
@ApiModel(description = "项目信息事件新增")
@Data
public class CreateProjectEventReq implements Serializable {

    /**
     * 事件code
     */
    @ApiModelProperty(value = "事件code")
    private String eventCode;

    /**
     * 事件名称
     */
    @ApiModelProperty(value = "事件名称")
    @NotNull(message = "事件名称不能为空")
    private String eventName;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String eventDesc;

    /**
     * 业务字段
     */
    @ApiModelProperty(value = "业务字段")
    @Valid
    @NotEmpty(message = "业务字段信息不能为空")
    private List<EventBusinessColumn> businessColumn;

    /**
     * 事件查询API
     */
    @ApiModelProperty(value = "事件查询API")
    @NotNull(message = "事件回调url不能为空")
    private String eventApi;

    /**
     * 创建用户
     */
    @ApiModelProperty(value = "创建用户")
    private String createBy;

}
