package com.cscec3b.iti.projectmanagement.api.dto.request.warn;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectMonitorQueryParams", description = "项目监控查询参数")
public class ProjectMonitorQueryParams extends BasePage {

    @ApiModelProperty(value = "执行单位id,前端传递组织ID")
    private String executeUnitId;

    @ApiModelProperty("云枢执行单位的treeId,云枢IdPath的末级组织")
    private String treeId;

    @ApiModelProperty(value = "工程名称")
    private String projectName;

    @ApiModelProperty(value = "项目编号")
    private String projectFinanceCode;

    @ApiModelProperty(value = "项目名称")
    private String projectFinanceName;

    @ApiModelProperty(value = "项目简称")
    private String projectFinanceAbbreviation;

    @ApiModelProperty(value = "A8项目编码")
    private String a8ProjectCode;

    @ApiModelProperty(value = "财商立项状态")
    private Integer financeApproveStatus;

    @ApiModelProperty(value = "智慧工地立项状态")
    private Integer smartApproveStatus;

    @ApiModelProperty(value = "UC创建项目部状态")
    private Integer ucDepartStatus;

    @ApiModelProperty(value = "投标总结编号")
    private String bidCode;

    @ApiModelProperty(value = "局内合同编号")
    private String contractCode;

    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    @ApiModelProperty(value = "投资文件编号")
    private String investmentCode;

    @ApiModelProperty(value = "监控类型，全部:0、重大基础设施类项目立项:1、非重大基础设施类项目立项:2、特殊立项:3")
    private Integer monitorProjectType;

    @ApiModelProperty(value = "异常数据展示，不展示：0、展示：1")
    private Integer exceptionDataDisplay;

    @ApiModelProperty("项目标识")
    private String cpmProjectKey;

    @ApiModelProperty("云枢项目ID")
    private String yunshuOrgId;

}
