package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 项目立项步骤-独立性判断请求参数
 *
 * <AUTHOR>
 * @date 2024/01/04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("项目立项步骤-类型确认请求参数")
public class StepOfRecheckReq extends ApprovalStepReq implements Serializable {

    /**
     * 流程模板id
     */
    @ApiModelProperty(value = "流程模板id")
    // @NotBlank(message = "流程模板id不能为空")
    private String procDefId;

    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    @NotBlank(message = "流程实例id不能为空")
    private String procInstId;

}
