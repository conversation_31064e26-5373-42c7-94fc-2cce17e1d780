package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> on 2016/9/28.
 */
@Data
public class TreeNodeBO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;

    private String parentId;

	private String code;

    private String name;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "展开状态：1是 0否")
    private Integer isOpen;

    @ApiModelProperty(value = "是否叶子节点(1是/0否)")
    private Integer isLeaf;

    @ApiModelProperty(value = "0=云筑网，1=本系统")
    private Integer dataSource;

    private String key;

    private String title;

    private String value;

    private List<TreeNodeBO> children;

    /**
     * id的全路径,保存从根节点到当前节点的id值 下划线分割
     */
    private String idFullPath;

    /**
     * code的全路径,保存从根节点到当前节点的code值 下划线分割
     */
    private String codeFullPath;
    
    @ApiModelProperty(value = "部门Id")
    private String departmentId;

}
