package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 项目信息总表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel(value = "ProjectHookInfo对象", description = "项目挂接信息表")
public class ProjectHookInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id,立项id
     */
    @ApiModelProperty(value = "自增id")
    private Integer id;

    /**
     * 与立项表关联
     */
    @ApiModelProperty(value = "与立项表关联")
    private Integer applyId;

    /**
     * 挂接的项目id,与项目信息表关联
     */
    @ApiModelProperty(value = "挂接的项目id")
    private Integer hookProjectId;

    /**
     * 挂接的项目名称
     */
    @ApiModelProperty(value = "挂接的项目名称")
    private String hookProjectName;

    /**
     * 挂接的项目上级组织id
     */
    @ApiModelProperty(value = "挂接的项目上级组织id")
    private Integer hookDepartmentId;

    /**
     * 挂接的项目上级组织名称
     */
    @ApiModelProperty(value = "挂接的项目上级组织名称")
    private String hookDepartmentName;

    /**
     * 状态 0待审批 1已同意 2已拒绝
     */
    @ApiModelProperty(value = "状态 0待审批 1已同意 2已拒绝")
    private Integer status;

    /**
     * 当前修改人id
     */
    @ApiModelProperty(value = "当前修改人id")
    private String modifierId;

    /**
     * 当前修改人姓名
     */
    @ApiModelProperty(value = "当前修改人姓名")
    private String modifierName;

    /**
     * 当前审批人id
     */
    @ApiModelProperty(value = "当前审批人id")
    private String approverId;

    /**
     * 当前审批人姓名
     */
    @ApiModelProperty(value = "当前审批人姓名")
    private String approverName;

    /**
     * 审批备注
     */
    @ApiModelProperty(value = "审批备注")
    private String approveComment;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createName;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String createId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}
