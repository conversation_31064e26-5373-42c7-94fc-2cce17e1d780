package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("查询用户请求")
public class QueryUsersReq extends BasePage {

    /**
     * 部门 ID
     */
    @ApiModelProperty(value = "部门id")
    @NotBlank(message = "部门id不能为空")
    private String departmentId;

    /**
     * 关键字
     */
    @ApiModelProperty(value = "用户姓名或帐号", notes = "默认传\"\"")
    private String keywords = "";
}
