package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.dto.FinancialBusinessSegmentDto;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

public interface IMainDataApi {

    String PATH = "/main-data";

    /**
     * 查询财商业务版块信息
     *
     * @return {@link GenericityResponse }<{@link List }<{@link FinancialBusinessSegmentDto }>>
     */
    @GetMapping("/business-segment/tree")
    @ApiOperation(value = "查询财商业务树列表信息")
    GenericityResponse<List<FinancialBusinessSegmentDto>> getBusinessSegmentListTree();

    /**
     * 查询财商业务版块信息
     *
     * @return {@link GenericityResponse }<{@link List }<{@link FinancialBusinessSegmentDto }>>
     */
    @GetMapping("/business-segment/List")
    @ApiOperation(value = "查询财商业务版块列表信息")
    GenericityResponse<List<FinancialBusinessSegmentDto>> getBusinessSegmentList();
}
