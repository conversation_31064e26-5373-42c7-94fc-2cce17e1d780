package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel("立项步骤注意事项更新请求参数")
public class ApprovalStepNoteUpdateReq extends ApprovalStepNoteReq implements Serializable {


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

}
