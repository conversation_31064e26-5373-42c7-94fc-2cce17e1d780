package com.cscec3b.iti.projectmanagement.api.dto.response.event;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/27 10:34
 */
@Data
public class ProjectFlowItemsResp {

    @ApiModelProperty(value = "项目流转节点名称")
    private String flowNodeName;
    @ApiModelProperty(value = "触发节点")
    private String flowNodeCode;
    @ApiModelProperty(value = "节点位置")
    private String flowHandlerCode;
}
