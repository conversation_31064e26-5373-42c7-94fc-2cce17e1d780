package com.cscec3b.iti.projectmanagement.api.dto.response.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "项目流转事件及推送记录")
public class ProjectFlowEventLogResp implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id， 消息id")
    private String id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目流转节点
     */
    @ApiModelProperty(value = "项目流转节点")
    private String flowNodeCode;

    /**
     * 项目流转节点监听切点：pre: 节点后;  post:节点后;
     */
    @ApiModelProperty(value = "项目流转节点监听切点：pre: 节点后;  post:节点后;")
    private String flowHandlerCode;

    /**
     * 监听事件触发时的项目信息
     */
    @ApiModelProperty(value = "监听事件触发时的项目信息")
    private String projectArchive;

    /**
     * 项目信息类型：insert:新增; update:更新;
     */
    @ApiModelProperty(value = "项目信息类型：insert:新增; update:更新;")
    private String flowDataTypeCode;


    /**
     * 是否手动触发
     */
    @ApiModelProperty(value = "是否手动触发", notes = "0: 否; 1: 是")
    private int manual;

    /**
     * 触发时间
     */
    @ApiModelProperty(value = "触发时间")
    private Long createAt;

    /**
     * 触发时间
     */
    @ApiModelProperty(value = "推送历史")
    private List<PushRecordResp> pushRecords;

}
