package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.validation.annotation.MultiFieldCheck;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@MultiFieldCheck.Rules(value = {
        @MultiFieldCheck(checkRule = "(#belongId != null && #belongId !='') || (#fileCode != null && #fileCode != '')",
                message = "belongId 和 fileCode 不能同时为空")
})
public class ContractFileDetailReq implements Serializable {
    /**
     * 文件所属id
     */
    @ApiModelProperty(value = "所属文件类型")
    @NotNull(message = "所属文件类型不能为空")
    private Integer belongFileType;

    /**
     * 文件所属id
     */
    @ApiModelProperty(value = "文件所属id")
    private String belongId;

    /**
     * 文件、合同编码
     */
    @ApiModelProperty(value = "文件、合同编码")
    private String fileCode;

}
