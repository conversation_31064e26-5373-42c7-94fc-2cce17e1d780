package com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "项目立项类型与业务系统绑定响应")
public class ApprovalTypeSubscribeMappingResp implements Serializable {


    /**
     * 业务系统id
     */
    @ApiModelProperty(value = "业务系统id")
    private Long subscribeId;

    /**
     * 业务系统名称
     */
    @ApiModelProperty(value = "业务系统名称")
    private String subscribeName;

    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 执行单位id
     */
    @ApiModelProperty(value = "执行单位id")
    private String yunshuExecuteUnitId;

    /**
     * 业务系统要求级别
     */
    @ApiModelProperty(value = "业务系统要求级别")
    private String pushLevel;

    /**
     * 业务系统要求级别名称
     */
    @ApiModelProperty(value = "业务系统要求级别名称")
    private String pushLevelName;

}
