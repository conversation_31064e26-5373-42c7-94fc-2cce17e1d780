package com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectResp", description = "工程项目关施工项目响应对象")
public class StandardProjectResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long id;

    /**
     * CPM 项目密钥
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * CPM 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

    /**
     * 项目财商编码
     */
    @ApiModelProperty(value = "项目财商编码")
    private String projectFinanceCode;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 是否主项目
     */
    @ApiModelProperty(value = "是否主施工项目")
    private boolean mainProject;

    /**
     * 项目在各系统状态
     */
    @ApiModelProperty(value = "项目在各系统状态：顺序：工程，财商，商务，供应链")
    private Integer systemStatus;

    /**
     * 项目在各系统状态
     */
    @ApiModelProperty(value = "项目在工地系统状态")
    private Boolean smartSiteStatus;

}
