package com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "关联施工项目到工程请求参数")
public class MappingStandard2EngineeringProjectReq implements Serializable {

    /**
     * 施工项目id
     */
    @ApiModelProperty(value = "施工项目id", required = true)
    @NotNull(message = "施工项目id不能为空")
    private Long projectId;

    /**
     * 工程项目id
     */
    @ApiModelProperty(value = "工程项目id", required = true)
    @NotNull(message = "工程项目id不能为空")
    private Long engineeringProjectId;
}
