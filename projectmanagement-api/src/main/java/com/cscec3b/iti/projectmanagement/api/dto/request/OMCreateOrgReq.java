package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "创建组织请求")
public class OMCreateOrgReq implements Serializable {


    /**
     * 上级组织ID
     */
    @ApiModelProperty(value = "上级组织Id")
    @NotBlank(message = "上级组织ID不能为空")
    private String parentOrgId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String orgAbbr = orgName;

    /**
     * 组织标签
     */
    @ApiModelProperty(value = "组织标签")
    private List<String> labels;

    @ApiModelProperty(value = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 主项目财商项目编码
     */
    private String financeProjectCode;

}
