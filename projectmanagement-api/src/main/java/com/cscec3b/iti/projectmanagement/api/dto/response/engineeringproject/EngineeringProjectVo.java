package com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "工程项目vo")
public class EngineeringProjectVo implements Serializable {

    /**
     * 工程id
     */
    private Long id;
    /**
     * 工程标识
     */
    private String engineeringKey;
    /**
     * 工程名称
     */
    private String engineeringName;
    /**
     * 工程项目编码
     */
    private String engineeringCode;
    /**
     * 主要施工项目id
     */
    private Long mainProjectId;
    /**
     * 执行单 ID
     */
    private String executeUnitId;

    /**
     * 执行单位名称
     */
    private String executeUnitName;
    /**
     * 执行单位ID 路径
     */
    private String executeUnitIdPath;

    /**
     * 执行单位缩写
     */
    private String executeUnitAbbreviation;

    /**
     * 项目部 ID
     */
    private String projectDeptId;

}
