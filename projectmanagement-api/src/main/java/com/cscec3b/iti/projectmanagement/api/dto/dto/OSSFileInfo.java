package com.cscec3b.iti.projectmanagement.api.dto.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "oss文件信息")
public class OSSFileInfo implements Serializable {

    /**
     * 文件访问地址
     */
    @ApiModelProperty(value = "oss访问地址")
    private String filePath;

    /**
     * 文件大小，单位字节
     */
    @ApiModelProperty(value = "文件大小 byte")
    private Long fileSize;

    /**
     * 文件名称,编码后的名称,对应OSS的objectKey
     */
    @ApiModelProperty(value = "文件名称,编码后的名称,对应OSS的objectKey")
    private String fileId;

    /**
     * 原始文件名
     */
    @ApiModelProperty(value = "原始文件名")
    private String originalName;

    /**
     * 基础存储路径
     */
    @ApiModelProperty(value = "基础存储路径")
    private String basePath;


    /**
     * MIME 类型
     */
    @ApiModelProperty(value = "MIME类型")
    private String contentType;

    /**
     * 文件md5
     */
    @ApiModelProperty(value = "文件md5")
    private String fileMd5;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "文件上传时间")
    private Long createAt;

    @ApiModelProperty(value = "文件上传状态;true 说明文件已经上传完成;")
    private boolean completed = false;

    private static final long serialVersionUID = 1L;

    public OSSFileInfo() {
    }

    public OSSFileInfo(String filePath, Long fileSize, String fileId, String originalName, String contentType, String fileMd5) {
        this.filePath = filePath;
        this.fileSize = fileSize;
        this.fileId = fileId;
        this.originalName = originalName;
        this.contentType = contentType;
        this.fileMd5 = fileMd5;
    }
}
