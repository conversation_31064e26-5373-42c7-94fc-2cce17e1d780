
package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
* @description 智慧工地响应信息
* <AUTHOR>
* @date 2022/11/16
*/
@Data
@Accessors(chain = true)
@ApiModel(value = "SmartSiteResp", description = "智慧工地响应信息")
public class SmartSiteResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "true-成功，false-失败")
    private Boolean success;

    @ApiModelProperty(value = "状态(0-正常)")
    private Integer code;

    @ApiModelProperty(value = "消息")
    private String msg;

    @ApiModelProperty(value = "数据")
    private List<SmartSiteData> data;

}