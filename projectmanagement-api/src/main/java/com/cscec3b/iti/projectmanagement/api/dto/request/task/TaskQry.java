
package com.cscec3b.iti.projectmanagement.api.dto.request.task;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @description 任务查询条件
 * <AUTHOR>
 * @date 2022/10/19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "TaskQry", description = "任务查询条件")
public class TaskQry extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "状态类型不能为空")
    @ApiModelProperty(value = "状态类型(todo:待办;done:已办)", required = true)
    private String statusType;

    @ApiModelProperty(value = "经办人")
    private String handlerPerson;
}
