package com.cscec3b.iti.projectmanagement.api.dto.response;

import java.io.Serializable;

import com.cscec3b.iti.projectmanagement.api.dto.dto.OMInitProjectDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 初始化项目响应信息
 *
 * <AUTHOR>
 * @date 2023/07/25 15:26
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class OMInitProjectResp extends OMInitProjectDto implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long id;
}
