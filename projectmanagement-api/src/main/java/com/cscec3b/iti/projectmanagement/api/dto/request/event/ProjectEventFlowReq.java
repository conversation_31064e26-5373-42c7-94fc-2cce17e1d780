package com.cscec3b.iti.projectmanagement.api.dto.request.event;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "项目事件触发及推送记录")
@Data
public class ProjectEventFlowReq extends BasePage implements Serializable {

    /**
     * 订阅者系统id
     */
    @ApiModelProperty(value = "订阅者系统id")
    private Integer pushSystemId;


    /**
     * 项目事件消息id，对应project_flow_event_record主键id
     */
    @ApiModelProperty(value = "项目事件消息id，对应project_flow_event_record主键id")
    private String projectMsgId;

    /**
     * 项目实体主键id
     */
    @ApiModelProperty(value = "项目实体主键id")
    private Long projectId;


    /**
     * 项目流转节点
     */
    @ApiModelProperty(value = "项目流转节点")
    private String flowNodeCode;

    /**
     * 监听位置
     */
    @ApiModelProperty(value = "监听位置： pre：前置； post: 后置")
    private String flowHandlerCode;


    /**
     * 消息触发开始时间

     */
    @ApiModelProperty(value = "消息触发开始时间")
    private Long crtTimeBegin;

    /**
     * 消息触发结束时间
     */
    @ApiModelProperty(value = "消息触发结束时间")
    private Long crtTimeEnd;

    /**
     * 项目中心标识
     */
    @ApiModelProperty(value = "项目中心标识")
    private String cpmProjectKey;
}