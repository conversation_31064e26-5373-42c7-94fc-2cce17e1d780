package com.cscec3b.iti.projectmanagement.api.dto.response.open;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 项目列表分页请求参数
 *
 * <AUTHOR>
 * @date 2023/11/16
 */
@Getter
@Setter
@ApiModel(value = "项目列表分页请求参数")
public class OpenProjectPageResp implements Serializable {

    /**
     * 项目中心项目标识
     * cpmProjectKey
     */
    @ApiModelProperty(value = "项目唯一标识")
    private String code;


    /**
     * 项目中心项目名称，（财商立项商取工程名称，财商立项后取财商名称）
     * cpmProjectName
     */
    @ApiModelProperty(value = "项目名称", notes = "财商立项商取工程名称，财商立项后取财商名称")
    private String name;

    /**
     * 项目中心项目简称，（财商立项商前取工程简称，财商立项后取财商简称）
     * cpmProjectAbbreviation
     */
    @ApiModelProperty(value = "项目简", notes = "财商立项商前取工程简称，财商立项后取财商简称")
    private String abbreviation;


    /**
     * 智慧工地项目云枢组织TreeID
     * yunshuTreeId
     */
    @ApiModelProperty(value = "项目部组织树TreeID")
    @JsonProperty(value = "PDOrgTreeID")
    private String PDOrgTreeID;


    /**
     * 云枢组织id
     * yunshuOrgId
     */
    @ApiModelProperty(value = "项目部组织id")
    @JsonProperty(value = "PDOrgID")
    private String PDOrgID;

    /**
     * 云枢执行单位名称
     * yunshuExecuteUnit
     */
    @ApiModelProperty(value = "执行单位名称")
    private String operatingBranchName;

    /**
     * 云枢执行单位编码
     * yunshuExecuteUnitCode
     */
    @ApiModelProperty(value = "执行单位组织编码")
    @JsonProperty(value = "OBOrgCode")
    private String OBOrgCode;

    /**
     * 云枢执行单位id
     * yunshuExecuteUnitId
     */
    @ApiModelProperty(value = "执行单位组织id")
    @JsonProperty(value = "OBOrgID")
    private String OBOrgID;

    /**
     * 云枢执行单位idPath
     * yunshuExecuteUnitIdPath
     */
    @ApiModelProperty(value = "执行单位组织idPath")
    @JsonProperty(value = "OBOrgIDPath")
    private String OBOrgIDPath;

    /**
     * 云枢执行单位简称
     * yunshuExecuteUnitAbbreviation
     */
    @ApiModelProperty(value = "执行单位简称")
    private String operatingBranchAbb;

    /**
     * 智慧工地项目云枢组织queryCode
     * yunshuQueryCode
     */
    @ApiModelProperty(value = "项目部组织树queryCode")
    @JsonProperty(value = "PDOrgQueryCode")
    private String PDOrgQueryCode;

    /**
     * 智慧工地项目直接上级云枢组织ID
     * yunshuParentOrgId
     */
    @ApiModelProperty(value = "项目部直接上级组织ID")
    @JsonProperty(value = "PDParentOrgID")
    private String PDParentOrgId;

    /**
     * 智慧工地项目直接上级全称
     * yunshuParentOrgName
     */
    @ApiModelProperty(value = "项目部直接上级组织名称")
    @JsonProperty(value = "PDParentOrgName")
    private String PDParentOrgName;

    /**
     * 智慧工地项目直接上级treeID
     * yunshuParentTreeId
     */
    @ApiModelProperty(value = "项目部直接上级组织treeID")
    @JsonProperty(value = "PDParentOrgTreeId")
    private String PDParentOrgTreeId;

    /**
     * 财商项目编码
     * projectFinanceCode
     */
    @ApiModelProperty(value = "财商项目编码")
    private String financeProjectCode;

    /**
     * 财商项目名称
     * projectFinanceName
     */
    @ApiModelProperty(value = "财商项目名称")
    private String financeProjectName;
    /**
     * 财商简称
     * projectFinanceAbbreviation
     */
    @ApiModelProperty(value = "财商简称")
    private String financeProjectAbb;

    /**
     * 工程名称
     * projectName
     */
    @ApiModelProperty(value = "工程名称")
    private String engineeringName;

    /**
     * 工程简称
     * projectAbbreviation
     */
    @ApiModelProperty(value = "工程简称")
    private String engineeringAbb;


    /**
     * 工程地址
     */
    @ApiModelProperty(value = "工程地址")
    private String engineeringAddress;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额（元）")
    private BigDecimal contractAmount;
    /**
     * 施工项目状态（工程）
     */
    private String statusEngineering;
    /**
     * 施工项目状态（财务）
     */
    @ApiModelProperty(value = "施工项目状态（财务）")
    private String statusFinance;


    /**
     * 施工项目状态（商务）
     */
    @ApiModelProperty(value = "施工项目状态（商务）")
    private String statusBusiness;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customer;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司")
    private String customerParent;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financialBusinessSegment;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financeBusinessSegment;

    @ApiModelProperty(value = "市场业务板块")
    private String marketingBusinessSegment;


    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "市场业务板块codePath")
    private String marketingBusinessSegmentCodePath;


    /**
     * 财商业务板块codepath
     */
    @ApiModelProperty(value = "财商业务板块codepath")
    private String financeBusinessSegmentCodePath;

    /**
     * CPM 业务板块代码路径
     */
    @ApiModelProperty(value = "项目中心业务板块", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegmentCodePath;

    /**
     * CPM 业务部门
     */
    @ApiModelProperty(value = "项目中心业务板块名称", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegment;

}
