package com.cscec3b.iti.projectmanagement.api.dto.response.dict;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 字典数据视图对象 sys_dict_data
 *
 * <AUTHOR>
 * @date 2024/01/04
 */
@Data
public class SysDictDataResp implements Serializable {


    /**
     * 字典编码
     */

    private Long dictCode;

    /**
     * 字典排序
     */
    private Integer dictSort;

    /**
     * 字典标签
     */
    private String dictLabel;

    /**
     * 字典键值
     */
    private String dictValue;

    /**
     * 字典类型
     */
    private String dictType;


    /**
     * 是否默认（Y是 N否）
     */
    private String byDefault;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

}
