package com.cscec3b.iti.projectmanagement.api;

import java.util.List;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.groups.Default;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.help.HelpCenterSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.validatagroup.ValidateGroup;
import com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 帮助中心
 * <AUTHOR>
 * @date 2023/1/3 14:52
 */
public interface IHelpCenterApi {

    /**
     * 路径
     */
    String PATH = "/help";

    /**
     * 首页-帮助中心列表
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping
    @ApiOperation(value = "首页-帮助中心列表", notes = "已发布的前5条")
    GenericityResponse<List<HelpCenterResp>> get();
    
    /**
     * 帮助分页列表信息
     *
     * @param current   当前
     * @param size      大小
     * @param title     标题
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param status    状态
     * @return {@link GenericityResponse }<{@link Page }<{@link HelpCenterResp }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @GetMapping("/list")
    @ApiOperation(value = "帮助分页列表信息", notes = "帮助中心页面分页列表")
    GenericityResponse<Page<HelpCenterResp>> getList(
            @ApiParam(value = "当前页码") @RequestParam(value = "current", required = false, defaultValue = "1") @Min(1) int current,
            @ApiParam(value = "每页数量") @RequestParam(value = "size", required = false, defaultValue = "10") @Min(1) @Max(100) int size,
            @ApiParam(value = "标题") @RequestParam(value = "title", required = false) String title,
            @ApiParam(value = "发布时间-起始时间") @RequestParam(value = "beginTime", required = false) Long beginTime,
            @ApiParam(value = "发布时间-结束时间") @RequestParam(value = "endTime", required = false) Long endTime,
            @ApiParam(value = "发布状态 0:未发布; 1:已发布;") @RequestParam(value = "status", required = false) Integer status);
    
    /**
     * 新增帮助中心信息
     *
     * @param helpCenterSaveReq 帮助中心保存要求事情
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping
    @ApiOperation(value = "新增帮助中心信息", notes = "新增一条帮助信息")
    GenericityResponse<Boolean> save(@ApiParam(value = "帮助信息", required = true) @Validated @RequestBody HelpCenterSaveReq helpCenterSaveReq);
    
    
    /**
     * 编辑更新帮助中心信息
     *
     * @param helpCenterRe 帮助中心再保险
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PutMapping
    @ApiOperation(value = "编辑更新帮助中心信息", notes = "编辑一条帮助信息,只有未发布的信息可以再次编辑，已发布的信息会直接发布")
    GenericityResponse<Boolean> update(@ApiParam(value = "帮助信息", required = true) @Validated({Default.class, ValidateGroup.HelpEditReq.class}) @RequestBody HelpCenterEditReq helpCenterRe);
    
    /**
     * 删除帮助中心信息
     *
     * @param ids id
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除帮助中心信息，可批量", notes = "删除一条帮助信息,逻辑删除")
    GenericityResponse<Boolean> delete(@ApiParam(value = "id", required = true) @PathVariable Long[] ids);
    
    /**
     * 发布帮助中心信息
     *
     * @param helpCenterRe 帮助中心
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PutMapping("/publish")
    @ApiOperation(value = "发布帮助中心信息", notes = "发布一条帮助信息")
    GenericityResponse<Boolean> publish(@ApiParam(value = "帮助信息", required = true) @Validated @RequestBody HelpCenterEditReq helpCenterRe);
}
