package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;

/**
 * 项目信息事件表
 */
@ApiModel(description = "MQ消息体")
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ProjectEventMsgDto implements Serializable {

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id", notes = "消息id")
    private String msgId;

    /**
     * 事件Url
     */
    @ApiModelProperty(value = "事件url", notes = "事件配置的回调url")
    private String eventUrl;

    /**
     * 事件Id
     */
    @ApiModelProperty(value = "事件Id", notes = "事件id")
    private Integer eventId;

    /**
     * 事件code
     */
    @ApiModelProperty(value = "事件code", notes = "事件code")
    private String eventCode;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", notes = "项目id")
    private Long projectId;

    /**
     * consumerId
     */
    @ApiModelProperty(value = "消息者id")
    private Long consumerId;

    /**
     * 消息推送时间
     */
    @ApiModelProperty(value = "消息推送时间")
    private long sendTime = Instant.now().getEpochSecond();

}
