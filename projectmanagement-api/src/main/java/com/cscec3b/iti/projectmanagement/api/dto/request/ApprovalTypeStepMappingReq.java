package com.cscec3b.iti.projectmanagement.api.dto.request;

import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.MsgNoticeTargetUserInfo;
import com.cscec3b.iti.projectmanagement.api.validation.annotation.MultiFieldCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel(value = "项目立项类型与立项步骤绑定响应")
@MultiFieldCheck.Rules({
        @MultiFieldCheck(message = "需要发送通知消息时，必须填写消息配置id", conditional = "#sendNoticeMsg", checkRule =
                "#msgConfigId != null"),
        @MultiFieldCheck(message = "需要发送待办或任务时，必须填写发送对象", conditional = "#sendTodoTask || #sendNoticeMsg", checkRule =
                "#sendUserCodes != null && #sendUserCodes.size > 0")
})
public class ApprovalTypeStepMappingReq implements Serializable {


    /**
     * id
     */
    @ApiModelProperty(value = "立项类型与步骤关联id")
    private Long stepMappingId;

    /**
     * 项目立项类型id
     */
    @ApiModelProperty(value = "项目立项类型id")
    private Long typeId;

    /**
     * 项目立项步骤编码
     */
    @ApiModelProperty(value = "项目立项步骤编码")
    private String stepCode;

    /**
     * 项目立项步骤顺序
     */
    @ApiModelProperty(value = "项目立项步骤顺序")
    @NotNull(message = "项目立项步骤顺序不能为空")
    private Integer stepSeq;

    /**
     * 项目立项步骤No
     */
    @ApiModelProperty(value = "项目立项步骤No")
    @NotNull(message = "项目立项步骤No不能为空")
    private Integer stepNo;

    /**
     * 默认步骤版本标识
     */
    @ApiModelProperty(value = "默认步骤版本标识")
    @NotBlank(message = "默认步骤版本标识不能为空")
    private String version;

    /**
     * 是否发送待办任务
     */
    @ApiModelProperty(value = "发送待办任务: 1:是; 0:否")
    private boolean sendTodoTask;

    /**
     * 是否发送通知消息
     */
    @ApiModelProperty(value = "是否发送通知消息: 1:是; 0:否")
    private boolean sendNoticeMsg;

    /**
     * 消息配置 ID， 在UC平台上配置的消息配置 ID
     */
    @ApiModelProperty(value = "消息配置 ID， 在UC平台上配置的消息配置 ID")
    private String msgConfigId;

    /**
     * 选择用户类型  1：组织角色； 2：组织
     */
    @ApiModelProperty(value = "选择用户类型  1：组织角色； 2：组织")
    private Integer userChoseType;


    /**
     * 发送用户列表
     */
    @ApiModelProperty(value = "发送用户列表")
    // private Set<String> sendUserCodes = new HashSet<>();
    private List<MsgNoticeTargetUserInfo> sendUserCodes = new ArrayList<>();


}
