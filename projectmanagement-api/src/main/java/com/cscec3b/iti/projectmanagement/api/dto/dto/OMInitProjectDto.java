package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 运维接口-插入项目信息
 *
 * <AUTHOR>
 * @date 2023/07/25 14:50
 **/

@Data
@ApiModel(value = "初始化项目信息")
public class OMInitProjectDto implements Serializable {

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位")
    private String executeUnit;

    /**
     * 执行单位id
     */
    @ApiModelProperty(value = "执行单位id")
    private String executeUnitId;

    /**
     * 执行单位Code
     */
    @ApiModelProperty(value = "执行单位Code")
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath")
    private String executeUnitIdPath;

    /**
     * 执行单位简称
     */
    @ApiModelProperty(value = "执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    /**
     * 云枢执行单位id
     */
    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位
     */
    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位idPath
     */
    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号", position = 22)
    private String projectFinanceCode;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称", position = 23)
    private String projectFinanceName;

    /**
     * 财商立项项目简称（中文）
     */
    @ApiModelProperty(value = "财商立项项目简称（中文）", position = 24)
    private String projectFinanceAbbreviation;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 30)
    private String projectAddress;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）", position = 27)
    private String projectType;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financeBusinessSegment;

    /**
     * 财商业务板块code
     */
    @ApiModelProperty(value = "财商业务板块codePath")
    private String financeBusinessSegmentCodePath;

    /**
     *局标准分类
     */
    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部")
    private String isCreateHead;
}
