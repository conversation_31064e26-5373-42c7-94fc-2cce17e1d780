package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 中标未立项分页列表响应参数
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
@Accessors(chain = true)
@ApiModel("中标未立项分页列表响应参数")
public class BidApprovalPageResp implements Serializable {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * 所属文件id
     */
    @ApiModelProperty("所属文件id")
    private Long belongId;

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String scopeType;

    /**
     * 工程名称
     */
    @ApiModelProperty("工程名称")
    private String projectName;

    /**
     * 执行单位名称
     */
    @ApiModelProperty("执行单位名称")
    private String executeUnit;

    /**
     * 执行单位queryCode
     */
    @ApiModelProperty("执行单位queryCode")
    private String executeUnitIdPath;

    /**
     * 执行单位组织id
     */
    @ApiModelProperty("执行单位组织id")
    private String executeUnitId;

    /**
     * 业主名称
     */
    @ApiModelProperty("业主名称")
    private String customerName;


    /**
     * 发起人
     */
    @ApiModelProperty("发起人")
    private String submitPerson;
    /**
     * 发起人姓名
     */
    @ApiModelProperty("发起人姓名")
    private String submitPersonName;


    /**
     * 接收人
     */
    @ApiModelProperty("接收人")
    private String approvalPerson;
    /**
     * 接收人姓名
     */
    @ApiModelProperty("接收人姓名")
    private String approvalPersonName;


    /**
     * 数据来源名称
     */
    @ApiModelProperty("数据来源名称")
    private String scopeTypeName;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty("是否创建指挥部")
    private String isCreateHead;

    /**
     * 独立性
     */
    @ApiModelProperty("独立性")
    private String isIndependent;

    /**
     * 独立性名称
     */
    @ApiModelProperty("独立性名称")
    private String isIndependentName;

    /**
     * 独立性编码
     */
    @ApiModelProperty("独立性编码")
    private Integer isIndependentId;

    /**
     * 地址
     */
    @ApiModelProperty("地址")
    private String address;


    /**
     * 文件编码
     */
    @ApiModelProperty("文件编码")
    private String contractFileCode;


    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Long updateAt;

    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty("区")
    private String region;

    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private Integer status;

    /**
     * 当前进行中步骤
     */
    @ApiModelProperty("当前进行中步骤")
    private Integer currentStepNo;

    /**
     * 立项项目类型
     */
    @ApiModelProperty("立项项目类型")
    private String approvalTypeName;

    /**
     * 立项项目id
     */
    @ApiModelProperty("立项项目id")
    private Long approvalTypeId;


    /**
     * 立项项目类型编码
     */
    @ApiModelProperty("立项项目类型编码")
    private String approvalTypeCode;

    /**
     * 立项流程定义
     */
    @ApiModelProperty("立项流程定义")
    private String stepList;

    /**
     * 流程模板定义
     */
    @ApiModelProperty("流程模板定义")
    private String procDefId;

    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String procInstId;

    /**
     * 项目中心项目标识
     */
    @ApiModelProperty("中心项目标识")
    private String cpmProjectKey;

    /**
     * 财商立项完成时间
     */
    @ApiModelProperty(value = "财商立项完成时间")
    private Long financeApprovalTime;

    /**
     * 智慧工地立项完成时间
     */
    @ApiModelProperty(value = "智慧工地立项完成时间")
    private Long smartSiteApprovalTime;


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

    /**
     * 步骤版本
     */
    @ApiModelProperty(value = "步骤版本")
    private Integer stepVersion;
}
