package com.cscec3b.iti.projectmanagement.api;

import org.springframework.web.bind.annotation.GetMapping;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommonConfigDto;

import io.swagger.annotations.ApiOperation;

/**
 * 基础配置信息
 * <AUTHOR>
 * @date 2022-12-2022/12/30 11:24
 */
public interface ICommonConfigApi {

    /**
     * 路径
     */
    String PATH = "/common-data";
    
    /**
     * 公共配置信息
     *
     * @return {@link GenericityResponse }<{@link CommonConfigDto }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @GetMapping("/config")
    @ApiOperation("公共配置信息")
    GenericityResponse<CommonConfigDto> commonConfig();
}
