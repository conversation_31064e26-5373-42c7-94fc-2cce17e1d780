package com.cscec3b.iti.projectmanagement.api;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgCodeSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgCodeUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 待办任务配置消息配置
 *
 * <AUTHOR>
 * @date 2024/11/29
 */
public interface PendingTaskMsgConfigApi {

    /**
     * 路径
     */
    String PATH = "/task-msg-config";

    @PostMapping("page")
    @ApiOperation("待办任务配置消息配置列表")
    GenericityResponse<Page<PendingTaskMsgConfigResp>>
        page(@Validated @ApiParam("分页查询") @RequestBody PendingTaskMsgReq req);

    @PostMapping
    @ApiOperation("待办任务配置消息添加")
    GenericityResponse<Boolean> create(@Validated @ApiParam("新增参数") @RequestBody PendingTaskMsgCodeSaveReq saveReq);

    @PutMapping
    @ApiOperation("待办任务配置消息更新")
    GenericityResponse<Boolean> update(@ApiParam("更新参数") @RequestBody PendingTaskMsgCodeUpdateReq updateReq);

    @DeleteMapping("{id}")
    @ApiOperation("待办任务配置消息删除")
    GenericityResponse<Boolean> delete(@ApiParam(value = "id", required = true) @PathVariable Long id);
}
