package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "OrgNodeResp", description = "项目列表标准组织返回对象")
public class OrgNodeResp {
    @ApiModelProperty(value = "标准组织ID")
    private Long id;
    @ApiModelProperty(value = "标准组织路径(从根节点到当前节点)")
    private String idPath;
    @ApiModelProperty(value = "标准组织编码")
    private String code;
    @ApiModelProperty(value = "标准组织全名")
    private String name;
    @ApiModelProperty(value = "标准组织简称")
    private String abbreviation;
    @ApiModelProperty(value = "标准组织父节点全名")
    private String parentName;
    @ApiModelProperty(value = "标准组织排序编号")
    private Integer order;
    @ApiModelProperty(value = "是否叶子节点")
    private Boolean leaf = false;
    @ApiModelProperty(value = "组织类型: 0:常设机构; 1:项目部; 2:指挥部; 3:部门; 4:总部; 5:单位直管项目组;")
    private Integer orgType;
    @ApiModelProperty(value = "能否直接下设项目部或指挥部: 0:不可添加; 1:可以添加;")
    private Integer isSetupSub;
}