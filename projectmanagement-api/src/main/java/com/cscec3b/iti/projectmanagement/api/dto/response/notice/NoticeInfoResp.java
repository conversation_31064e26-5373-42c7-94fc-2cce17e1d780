package com.cscec3b.iti.projectmanagement.api.dto.response.notice;

import java.io.Serializable;
import java.util.List;

import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description 通知公告详情响应返回对象
 * <AUTHOR>
 * @Date 2023/1/3 10:55
 */
@Data
@Accessors(chain = true)
@ApiModel(value="NoticeInfoResp", description="通知公告详情响应返回对象")
public class NoticeInfoResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 通知公告id
     */
    @ApiModelProperty(value = "通知公告id")
    private Long id;

    /**
     * 公告标题
     */
    @ApiModelProperty(value = "公告标题")
    private String noticeTitle;

    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    private String noticeContent;

    /**
     * 发布单位
     */
    @ApiModelProperty(value = "发布单位")
    private String publishOrganization;

    /**
     * 公告类型
     */
    @ApiModelProperty(value = "公告类型（1：系统公告，2：业务公告）")
    private Integer noticeType;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态（0：未发布，1：已发布）")
    private Integer publishStatus;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Long publishTime;

    /**
     * 阅读量
     */
    @ApiModelProperty(value = "阅读量")
    private String readingQuantity;

    /**
     * 公告附件信息
     */
    @ApiModelProperty(value = "公告附件信息")
    private List<AttachmentDto> attachments;

}
