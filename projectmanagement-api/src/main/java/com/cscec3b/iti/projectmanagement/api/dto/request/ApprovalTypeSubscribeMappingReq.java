package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@ApiModel(value = "项目立项类型与业务系统绑定响应")
public class ApprovalTypeSubscribeMappingReq implements Serializable {


    /**
     * 业务系统id
     */
    @ApiModelProperty(value = "业务系统id")
    private Long subscribeId;


    /**
     * 业务系统要求级别
     */
    @ApiModelProperty(value = "业务系统要求级别")
    private String pushLevel;


}
