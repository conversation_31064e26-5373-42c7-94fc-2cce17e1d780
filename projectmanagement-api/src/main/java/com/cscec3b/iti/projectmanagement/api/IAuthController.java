package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.SwitchOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 六统一认证管理
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Deprecated
public interface IAuthController {
    /**
     * 路径
     */
    String PATH = "/v2/auth";

    @GetMapping("/userinfo")
    @ApiOperation(value = "获取用户信息", notes = "获取用户信息")
    GenericityResponse<UserInfo> getUserInfo();

    /**
     * 切换组织
     *
     * @param orgId 组织ID
     * @return {@link GenericityResponse}<{@link SwitchOrgResp}>
     */
    @GetMapping("/switch-org")
    @ApiOperation(value = "切换组织", notes = "切换组织")
    GenericityResponse<SwitchOrgResp> switchOrg(@ApiParam(value = "组织ID", required = true)  @RequestParam(value = "orgId") String orgId);
}
