package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/01/02
 */
@Getter
@Setter
@ApiModel("项目立项类型更新请求")
public class ProjectApprovalTypeUpdateReq extends ProjectApprovalTypeSaveReq implements Serializable {


    /**
     * 项目类型id
     */
    @ApiModelProperty(value = "项目类型id")
    @NotNull(message = "项目类型id不能为空")
    private Long typeId;

}
