package com.cscec3b.iti.projectmanagement.api;

import java.io.IOException;

import javax.validation.constraints.NotBlank;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cscec3b.iti.common.base.api.GenericityResponse;

import io.swagger.annotations.ApiOperation;

/**
 * 本地公共数据标准清单
 *
 * <AUTHOR>
 * @date 2022/4/1 13:03
 **/

public interface ILocalCommonDataApi {
    /**
     * 路径
     */
    String PATH = "/local-common-data";

    /**
     * 查询公共标准数据清单（本地缓存）
     * @param url
     * @return resource中预置的json数据
     */
    @GetMapping("/common-data")
    @ApiOperation(value = "查询公共标准数据清单（本地缓存）")
    GenericityResponse<Object> getCommonData(@RequestParam(value = "url") @NotBlank String url);

    /**
     * 查询财商业务版块
     * @param
     * @return resource中预置的json数据
     */
    @GetMapping("/business-segment")
    @ApiOperation(value = "查询财商业务版块")
    GenericityResponse<Object> getBusinessSegment() throws IOException;
}
