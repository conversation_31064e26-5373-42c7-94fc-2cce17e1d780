package com.cscec3b.iti.projectmanagement.api.dto.response.warn;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectNoAgencyResp", description = "立项未签约响应对象")
public class ProjectNoAgencyResp implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "预警主键ID")
    private long projectId;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;

    @ApiModelProperty(value = "工程名称")
    private String engineeringName;

    @ApiModelProperty(value = "执行单位")
    private String executeUnit;

    @ApiModelProperty(value = "立项完成时间")
    private Long projectCompletionTime;

    @ApiModelProperty(value = "执行单位简称")
    private String executeUnitAbbreviation;

    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位编码")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 云枢执行单位简称
     */
    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

}
