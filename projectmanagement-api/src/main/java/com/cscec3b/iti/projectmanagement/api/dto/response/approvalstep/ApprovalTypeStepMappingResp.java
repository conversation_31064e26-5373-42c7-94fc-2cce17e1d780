package com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep;

import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.MsgNoticeTargetUserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "项目立项类型与立项步骤绑定响应")
public class ApprovalTypeStepMappingResp implements Serializable {


    /**
     * 立项步骤id
     */
    @ApiModelProperty(value = "立项类型与步骤关联id")
    private Long stepMappingId;

    /**
     * 项目立项类型id
     */
    @ApiModelProperty(value = "项目立项类型id")
    private Long typeId;

    /**
     * 项目立项步骤名称
     */
    @ApiModelProperty(value = "项目立项步骤名称")
    private String stepName;

    /**
     * 项目立项步骤编码
     */
    @ApiModelProperty(value = "项目立项步骤编码")
    private String stepCode;

    /**
     * 项目立项步骤no
     */
    @ApiModelProperty(value = "项目立项步骤序列")
    private Integer stepNo;

    @JsonIgnore
    private String stepEnum;


    /**
     * 项目立项步骤顺序
     */
    @ApiModelProperty(value = "项目立项步骤顺序")
    private Integer stepSeq;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    /**
     * 是否发送待办任务
     */
    @ApiModelProperty(value = "发送待办任务: 1:是; 0:否")
    private boolean sendTodoTask;

    /**
     * 是否发送通知消息
     */
    @ApiModelProperty(value = "是否发送通知消息: 1:是; 0:否")
    private boolean sendNoticeMsg;

    /**
     * 消息配置 ID， 在UC平台上配置的消息配置 ID
     */
    @ApiModelProperty(value = "消息配置 ID， 在UC平台上配置的消息配置 ID")
    private String msgConfigId;


    /**
     * 发送用户列表
     */
    @ApiModelProperty(value = "发送用户列表")
    private List<MsgNoticeTargetUserInfo> sendUserCodes = new ArrayList<>();

    /**
     * 组织 ID
     */
    @ApiModelProperty(value = "组织 ID")
    private String orgId;

    /**
     * remind 表达式
     */
    @ApiModelProperty(value = "remind 表达式")
    private String remindExpression;

    /**
     * 用户选择类型 1: 组织角色； 2：组织
     */
    @ApiModelProperty(value = "用户选择类型 1: 组织角色； 2：组织")
    private Integer userChoseType;
}
