package com.cscec3b.iti.projectmanagement.api.dto.request.task;

import com.cscec3b.iti.projectmanagement.api.dto.request.OMCreateOrgReq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "待办任务之创建组织请求参数")
public class TodoTaskCreateOrgReq extends OMCreateOrgReq {

    /**
     * 任务billId
     */
    @ApiModelProperty(value = "任务billId", required = true)
    private String billId;
}
