package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 执行单位部门信息
 */
@Data
@Accessors(chain = true)
public class ExecuteUnitDeptDto {
    @ApiModelProperty(value = "treeId")
    private String treeId;
    @ApiModelProperty(value = "部门id")
    private String deptId;
    @ApiModelProperty(value = "部门全称")
    private String orgFullName;
    @ApiModelProperty(value = "部门简称")
    private String orgShortName;
    @ApiModelProperty(value = "执行单位父节点treeId")
    private String parentId;
}

