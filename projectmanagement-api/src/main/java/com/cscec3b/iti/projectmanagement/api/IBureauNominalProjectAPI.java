package com.cscec3b.iti.projectmanagement.api;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.BureauNominalPageParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauNominalProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectDetailResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 局名义项目关系(局总包/分包)控制器
 *
 * <AUTHOR>
 * @description 项目关系(局总包 / 分包)控制器
 * @date 2023/07/28 17:17
 */
public interface IBureauNominalProjectAPI {

    /**
     * 路径
     */
    String PATH = "/project/bureau-nominal";
    
    /**
     * 局名义总包与分包项目关系
     *
     * @param type      类型
     * @param projectId 项目id
     * @return {@link GenericityResponse }<{@link BureauNamedProjectRelationship }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @GetMapping("/{type}/{projectId}")
    @ApiOperation(value = "局名义总包与分包项目关系")
    GenericityResponse<BureauNamedProjectRelationship> getProjectRelationship(
            @ApiParam(value = "项目局名义类型:1：局名义总包项目类型；2：局名义分包项目类型") @PathVariable int type,
            @ApiParam(value = "当前项目id") @PathVariable Long projectId);
    
    /**
     * 局名义总包与非局名义项目关系转换
     *
     * @param projectId 项目id
     * @return {@link GenericityResponse }<{@link BureauNamedProjectRelationship }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PutMapping("/change/{projectId}")
    @ApiOperation(value = "局名义总包与非局名义项目关系转换", notes = "非局名义总包项目可以直接转换为局名义总包项目，局名义总包项目下没有关系分包项目时才能转换为非局名义项目")
    GenericityResponse<BureauNamedProjectRelationship> changeProjectRelation(@ApiParam(name = "项目id") @PathVariable Long projectId);
    
    /**
     * 通过财商编码查询所有财商已立项的非局名义的项目信息
     *
     * @param financeCode 金融编码
     * @return {@link GenericityResponse }<{@link ProjectDetailResp }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @GetMapping("/{financeCode}")
    @ApiOperation(value = "通过财商编码查询所有财商已立项的非局名义的项目信息")
    GenericityResponse<ProjectDetailResp> getByFinanceCode(@ApiParam(name = "财商编码") @PathVariable String financeCode);
    
    /**
     * 添加非局名义项目到总包项目下
     *
     * @param generalContractorId 总承包商id
     * @param projectId           项目id
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping("/add/{generalContractorId}/{projectId}")
    @ApiOperation(value = "添加非局名义项目到总包项目下", notes = "已是分包状态的项目不能再次添加；添加完成后,添加的项目状态转换为分包状态,")
    GenericityResponse<Boolean> addProjectToGeneralContractorProject(
            @ApiParam(value = "总包项目id") @PathVariable Long generalContractorId,
            @ApiParam(value = "要添加的分包项目id") @PathVariable Long projectId);
    
    /**
     * 移除分包项目
     *
     * @param subcontractingId 分包id
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PutMapping("/remove/{subcontractingId}")
    @ApiOperation(value = "移除分包项目", notes = "移除分包项目后，被移除的项目转变为非局名义项目")
    GenericityResponse<Boolean>
    removeSubcontractingProjectId(@ApiParam(value = "分包项目id") @PathVariable Long subcontractingId);
    
    /**
     * 局名义项目管理列表
     *
     * @param queryParams 查询参数
     * @return {@link GenericityResponse }<{@link Page }<{@link BureauNominalProjectPageResp }>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @PostMapping("/page")
    @ApiOperation(value = "局名义项目管理列表", notes = "局名义项目管理列表通用查询接口")
    GenericityResponse<Page<BureauNominalProjectPageResp>> bureauNominalProjectPage(@Validated @RequestBody BureauNominalPageParams queryParams);

    /**
     * 局名义项目管理列表-切换云枢组织
     *
     * @param queryParams 查询参数
     * @return {@link GenericityResponse }<{@link Page }<{@link BureauNominalProjectPageResp }>>
     * <AUTHOR>
     * @date 2023/08/24
     */
    @PostMapping("/cloud-pivot/page")
    @ApiOperation(value = "局名义项目管理列表", notes = "局名义项目管理列表通用查询接口")
    GenericityResponse<Page<BureauNominalProjectPageResp>> bureauNominalPageCloudPivot(@Validated @RequestBody BureauNominalPageParams queryParams);
}
