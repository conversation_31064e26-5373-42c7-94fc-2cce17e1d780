<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.taskmesage.mapper.TodoTaskMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.taskmesage.model.TodoTask">
        <!--@mbg.generated-->
        <!--@Table u_todo_task-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_code" jdbcType="VARCHAR" property="taskCode"/>
        <result column="bill_id" jdbcType="VARCHAR" property="billId"/>
        <result column="bill_type" jdbcType="VARCHAR" property="billType"/>
        <result column="start_user_name" jdbcType="VARCHAR" property="startUserName"/>
        <result column="start_user_id" jdbcType="VARCHAR" property="startUserId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="state" jdbcType="TINYINT" property="state"/>
        <result column="bpm_instance_id" jdbcType="VARCHAR" property="bpmInstanceId"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="expected_deadline" jdbcType="TIMESTAMP" property="expectedDeadline"/>
        <result column="app_link" jdbcType="VARCHAR" property="appLink"/>
        <result column="web_link" jdbcType="VARCHAR" property="webLink"/>
        <result column="target_user" jdbcType="VARCHAR" property="targetUser"/>
        <result column="config_code" jdbcType="VARCHAR" property="configCode"/>
        <result column="payload" jdbcType="VARCHAR" property="payload"/>
        <result column="repeat_count" jdbcType="INTEGER" property="repeatCount"/>
        <result column="current_count" jdbcType="INTEGER" property="currentCount"/>
        <result column="remind_expression" jdbcType="VARCHAR" property="remindExpression"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
        <result column="approval_rate" jdbcType="TINYINT" property="approvalRate"/>
        <result column="next_remind_time" jdbcType="BIGINT" property="nextRemindTime"/>
        <result column="process_user" jdbcType="BIGINT" property="processUser"/>
        <result column="business_key" jdbcType="BIGINT" property="businessKey"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_code, bill_id, bill_type, start_user_name, start_user_id, title, `state`,
        bpm_instance_id, start_time, expected_deadline, app_link, web_link, target_user,
        config_code, payload, repeat_count, current_count, remind_expression, create_at,
        update_at,deleted, approval_rate, next_remind_time,process_user,business_key
    </sql>

    <select id="getByTaskCode" resultType="com.cscec3b.iti.taskmesage.model.TodoTask">
        select
        <include refid="Base_Column_List"/>
        from u_todo_task
        where deleted = 0
        and task_code = #{taskCode}
    </select>
</mapper>