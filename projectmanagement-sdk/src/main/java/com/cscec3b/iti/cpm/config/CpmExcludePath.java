package com.cscec3b.iti.cpm.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.AntPathMatcher;

/**
 * sdk 白名单路径配置
 *
 * <AUTHOR>
 * @date 2023/09/26 10:38
 **/


public class CpmExcludePath {
	
	
	/**
	 * sdk 白名单
	 */
	public static final List<String> excludeURIList = new ArrayList<>();
	
	private static final AntPathMatcher antPathMatcher = new AntPathMatcher();
	
	private CpmExcludePath() {}
	
	static {
		excludeURIList.add("/**/cpm/project/flow/event/notice");
	}
	
	/**
	 * @param requestUri 当前请求的uri
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/09/26
	 */
	public static boolean matchUri(String requestUri) {
		return excludeURIList.stream().anyMatch(uri -> antPathMatcher.match(uri, requestUri));
	}
	
}
