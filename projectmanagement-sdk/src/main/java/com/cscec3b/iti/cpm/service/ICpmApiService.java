package com.cscec3b.iti.cpm.service;

import com.cscec3b.iti.cpm.api.GenericityResponse;
import com.cscec3b.iti.cpm.config.CpmCallBackProperties;
import com.cscec3b.iti.cpm.exception.FrameworkException;
import com.cscec3b.iti.cpm.utils.ShenYuUtil;
import com.cscec3b.iti.model.req.OpenProjectSyncReq;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.ShenYuResponse;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import io.swagger.annotations.ApiParam;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

import static com.cscec3b.iti.cpm.utils.ShenYuUtil.genHttpHeaders;

/**
 * 项目中心开放接口
 *
 * <AUTHOR>
 * @date 2024/06/11
 */
public interface ICpmApiService {

    /**
     * 业务系统向项目中心同步项目信息
     *
     * @param syncReq {@link OpenProjectSyncReq}
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    Boolean syncProjectInfo(OpenProjectSyncReq syncReq);

    /**
     * 获取项目信息 <br>
     *
     * @param openReq {@link ProjectOpenReq}
     * @return {@link List}<{@link ProjectOpenResp}>
     */
    List<ProjectOpenResp> openProjectInfo(ProjectOpenReq openReq);

    /**
     * 云筑网项目关系映射信息 <br>
     * 通过云枢id和项目标识查询对应的云筑网系统id信息
     *
     * @param mappingReqs {@link ProjectYzwMappingReq}
     * @return {@link List }<{@link ProjectOpenMappingResp }>
     */
    List<ProjectOpenMappingResp> getMappingInfo(
            @ApiParam("项目") @RequestBody @Size(min = 1, max = 100, message = "仅支持1-100条信息,请检查查询数量")
            List<ProjectYzwMappingReq> mappingReqs);

    /**
     * 回调项目中心通用方法
     *
     * @param req                请求参数
     * @param callBackProperties 回调配置
     * @param callBackPath       回调路径
     * @param restTemplate       restTemplate
     * @return {@link ResponseEntity}<{@link ShenYuResponse}>
     */
    default <T> ResponseEntity<ShenYuResponse<T>> callBackToCpm(Object req,
            CpmCallBackProperties callBackProperties,
            String callBackPath, RestTemplate restTemplate) {
        final HttpHeaders httpHeaders = genHttpHeaders(callBackProperties, callBackPath);
        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(req, httpHeaders);
            final ParameterizedTypeReference<ShenYuResponse<T>> typeReference =
                    new ParameterizedTypeReference<ShenYuResponse<T>>() {
                    };
            return restTemplate.exchange(callBackProperties.getCpmHost() + callBackPath, HttpMethod.POST, httpEntity,
                    typeReference);
        } catch (RestClientException e) {
            throw new FrameworkException(-1, "回调项目中心api异常" + e.getMessage());
        }
    }

    /**
     * 回调项目中心通用方法, 多层级泛型时使用
     *
     * @param req                请求参数
     * @param callBackProperties 回调配置
     * @param callBackPath       回调路径
     * @param restTemplate       restTemplate
     * @param typeReference      泛型引用
     * @return {@link ResponseEntity}<{@link ShenYuResponse}<{@link T}>>
     */
    default <T> ResponseEntity<ShenYuResponse<T>> callBackToCpm(Object req, CpmCallBackProperties callBackProperties,
            String callBackPath, RestTemplate restTemplate,
            ParameterizedTypeReference<ShenYuResponse<T>> typeReference) {
        HttpHeaders httpHeaders = ShenYuUtil.genHttpHeaders(callBackProperties, callBackPath);

        try {
            HttpEntity<Object> httpEntity = new HttpEntity<>(req, httpHeaders);
            return restTemplate.exchange(callBackProperties.getCpmHost() + callBackPath, HttpMethod.POST, httpEntity,
                    typeReference);
        } catch (RestClientException var8) {
            throw new FrameworkException(-1, "回调项目中心api异常" + var8.getMessage());
        }
    }

    /**
     * 判断响应是否成功
     *
     * @param responseEntity 响应
     * @return 响应数据
     */
    default <T> T checkResponse(ResponseEntity<ShenYuResponse<T>> responseEntity) {
        // 判断htt状态码
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            final ShenYuResponse<T> body = responseEntity.getBody();
            // 优先判断神禹网关的状态
            if (Objects.nonNull(body)) {
                final Integer code = body.getCode();
                if (Objects.nonNull(code)) {
                    throw new FrameworkException(code, "神禹网关鉴权异常：" + body.getMsg());
                }
                // 再判断业务状态
                final Integer status = body.getStatus();
                if (status != 0) {
                    throw new FrameworkException(status, "业务处理异常：" + body.getMsg());
                }
                return body.getData();
            } else {
                throw new FrameworkException(500, "响应异常:(未获取到业务返回信息)");
            }

        } else {
            throw new FrameworkException(responseEntity.getStatusCode(), -1, "http请求异常");
        }
    }

}
