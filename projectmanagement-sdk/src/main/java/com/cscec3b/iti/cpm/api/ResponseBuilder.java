package com.cscec3b.iti.cpm.api;



/**
 * <AUTHOR>
 * @date 2021/10/28 通用的API返回格式
 */

public class ResponseBuilder {
    private ResponseBuilder() {}

    public static BaseResponse fromEmpty() {
        return new BaseResponse(0);
    }

    public static <T> GenericityResponse<T> fromData(T data) {
        return new GenericityResponse<>(data);
    }

    public static <T> GenericityResponse<T> fromData(int status, T data) {
        return new GenericityResponse<>(status, data);
    }

}
