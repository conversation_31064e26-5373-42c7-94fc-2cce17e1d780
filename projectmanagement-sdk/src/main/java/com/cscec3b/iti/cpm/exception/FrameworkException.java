package com.cscec3b.iti.cpm.exception;

import org.springframework.http.HttpStatus;

import lombok.Getter;

/**
 * 通用的业务异常定义
 *
 * <AUTHOR>
 * @date 2023/09/13
 */
@Getter
public class FrameworkException extends RuntimeException {
    /**
     * http状态码
     */
    private final HttpStatus httpStatus;
    /**
     * 业务状态码
     */
    private final int status;
    /**
     * 报错提示信息
     */
    private final String msg;

    /**
     * 国际化报错信息中变量
     */
    private final String[] errArgs;

    public FrameworkException(HttpStatus httpStatus, int status, String msg) {
        this(httpStatus, status, msg, null);
    }

    private FrameworkException(HttpStatus httpStatus, int status, String msg, String[] errArgs) {
        super(msg);
        this.httpStatus = httpStatus;
        this.status = status;
        this.msg = msg;
        this.errArgs = errArgs;
    }

    public FrameworkException(HttpStatus httpStatus, int status, String[] errArgs) {
        this(httpStatus, status, null, errArgs);
    }

    public FrameworkException(HttpStatus httpStatus, int status) {
        this(httpStatus, status, null, null);
    }

    public FrameworkException(int status, String msg) {
        this(HttpStatus.OK, status, msg, null);
    }

    public FrameworkException(int status, String[] errArgs) {
        this(HttpStatus.OK, status, null, errArgs);
    }

    public FrameworkException(int status) {
        this(HttpStatus.OK, status, null, null);
    }

}
