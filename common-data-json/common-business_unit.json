{"status": 0, "msg": null, "errService": null, "data": [{"parentId": "root", "id": "03", "name": "房地产开发与投资业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "03", "value": "房地产开发与投资业务", "parentId": null, "status": 1}, "children": [{"parentId": "03", "id": "0302", "name": "保障性住房开发", "check": 0, "leaf": true, "order": 0, "data": {"code": "0302", "value": "保障性住房开发", "parentId": "03", "status": 1}, "children": [], "selectable": true}, {"parentId": "03", "id": "0303", "name": "商业地产（写字楼商铺等）销售", "check": 0, "leaf": true, "order": 0, "data": {"code": "0303", "value": "商业地产（写字楼商铺等）销售", "parentId": "03", "status": 1}, "children": [], "selectable": true}, {"parentId": "03", "id": "0304", "name": "投资性房地产", "check": 0, "leaf": false, "order": 0, "data": {"code": "0304", "value": "投资性房地产", "parentId": "03", "status": 1}, "children": [{"parentId": "0304", "id": "030403", "name": "长租公寓", "check": 0, "leaf": true, "order": 0, "data": {"code": "030403", "value": "长租公寓", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030406", "name": "高尔夫", "check": 0, "leaf": true, "order": 0, "data": {"code": "030406", "value": "高尔夫", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030404", "name": "联合办公", "check": 0, "leaf": true, "order": 0, "data": {"code": "030404", "value": "联合办公", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030401", "name": "写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "030401", "value": "写字楼", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030405", "name": "酒店", "check": 0, "leaf": true, "order": 0, "data": {"code": "030405", "value": "酒店", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030407", "name": "物流地产", "check": 0, "leaf": true, "order": 0, "data": {"code": "030407", "value": "物流地产", "parentId": "0304", "status": 1}, "children": [], "selectable": true}, {"parentId": "0304", "id": "030402", "name": "集中商业", "check": 0, "leaf": true, "order": 0, "data": {"code": "030402", "value": "集中商业", "parentId": "0304", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "03", "id": "0399", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "0399", "value": "其他", "parentId": "03", "status": 1}, "children": [], "selectable": true}, {"parentId": "03", "id": "0305", "name": "物业管理", "check": 0, "leaf": false, "order": 0, "data": {"code": "0305", "value": "物业管理", "parentId": "03", "status": 1}, "children": [{"parentId": "0305", "id": "030501", "name": "物业服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "030501", "value": "物业服务", "parentId": "0305", "status": 1}, "children": [], "selectable": true}, {"parentId": "0305", "id": "030503", "name": "工程服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "030503", "value": "工程服务", "parentId": "0305", "status": 1}, "children": [], "selectable": true}, {"parentId": "0305", "id": "030502", "name": "增值服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "030502", "value": "增值服务", "parentId": "0305", "status": 1}, "children": [], "selectable": true}, {"parentId": "0305", "id": "030504", "name": "资产租赁", "check": 0, "leaf": true, "order": 0, "data": {"code": "030504", "value": "资产租赁", "parentId": "0305", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "03", "id": "0306", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "0306", "value": "城市综合体", "parentId": "03", "status": 1}, "children": [], "selectable": true}, {"parentId": "03", "id": "0301", "name": "商业住宅开发", "check": 0, "leaf": true, "order": 0, "data": {"code": "0301", "value": "商业住宅开发", "parentId": "03", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "04", "name": "基础设施建设业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "04", "value": "基础设施建设业务", "parentId": null, "status": 1}, "children": [{"parentId": "04", "id": "0406", "name": "邮电通讯工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0406", "value": "邮电通讯工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0406", "id": "040603", "name": "通信线路", "check": 0, "leaf": true, "order": 0, "data": {"code": "040603", "value": "通信线路", "parentId": "0406", "status": 1}, "children": [], "selectable": true}, {"parentId": "0406", "id": "040602", "name": "发射塔", "check": 0, "leaf": true, "order": 0, "data": {"code": "040602", "value": "发射塔", "parentId": "0406", "status": 1}, "children": [], "selectable": true}, {"parentId": "0406", "id": "040601", "name": "基站", "check": 0, "leaf": true, "order": 0, "data": {"code": "040601", "value": "基站", "parentId": "0406", "status": 1}, "children": [], "selectable": true}, {"parentId": "0406", "id": "040699", "name": "其他邮电通讯工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040699", "value": "其他邮电通讯工程", "parentId": "0406", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0407", "name": "防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0407", "value": "防卫防灾工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0407", "id": "040702", "name": "山洪防御工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040702", "value": "山洪防御工程", "parentId": "0407", "status": 1}, "children": [], "selectable": true}, {"parentId": "0407", "id": "040703", "name": "防空设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "040703", "value": "防空设施", "parentId": "0407", "status": 1}, "children": [], "selectable": true}, {"parentId": "0407", "id": "040704", "name": "消防设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "040704", "value": "消防设施", "parentId": "0407", "status": 1}, "children": [], "selectable": true}, {"parentId": "0407", "id": "040701", "name": "堤坝工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040701", "value": "堤坝工程", "parentId": "0407", "status": 1}, "children": [], "selectable": true}, {"parentId": "0407", "id": "040705", "name": "排雨工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040705", "value": "排雨工程", "parentId": "0407", "status": 1}, "children": [], "selectable": true}, {"parentId": "0407", "id": "040799", "name": "其他防卫防灾工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040799", "value": "其他防卫防灾工程", "parentId": "0407", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0402", "name": "能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0402", "value": "能源工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0402", "id": "040203", "name": "水电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040203", "value": "水电工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040209", "name": "太阳能工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040209", "value": "太阳能工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040204", "name": "火电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040204", "value": "火电工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040206", "name": "煤炭化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040206", "value": "煤炭化工工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040207", "name": "热力工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040207", "value": "热力工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040299", "name": "其他能源工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040299", "value": "其他能源工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040201", "name": "变电站及输电线路工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040201", "value": "变电站及输电线路工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040208", "name": "燃气供应工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040208", "value": "燃气供应工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040210", "name": "矿山工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040210", "value": "矿山工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040202", "name": "核电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040202", "value": "核电工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}, {"parentId": "0402", "id": "040205", "name": "风电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040205", "value": "风电工程", "parentId": "0402", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0404", "name": "供水及处理工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0404", "value": "供水及处理工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0404", "id": "040401", "name": "输、供水工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "040401", "value": "输、供水工程（含管道及附属设备）", "parentId": "0404", "status": 1}, "children": [], "selectable": true}, {"parentId": "0404", "id": "040402", "name": "水处理工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "040402", "value": "水处理工程（含管道及附属设备）", "parentId": "0404", "status": 1}, "children": [], "selectable": true}, {"parentId": "0404", "id": "040403", "name": "排污、排洪管道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040403", "value": "排污、排洪管道工程", "parentId": "0404", "status": 1}, "children": [], "selectable": true}, {"parentId": "0404", "id": "040499", "name": "其他供水及处理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040499", "value": "其他供水及处理工程", "parentId": "0404", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0401", "name": "交通运输工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0401", "value": "交通运输工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0401", "id": "040101", "name": "公路", "check": 0, "leaf": true, "order": 0, "data": {"code": "040101", "value": "公路", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040105", "name": "机场", "check": 0, "leaf": true, "order": 0, "data": {"code": "040105", "value": "机场", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040107", "name": "停车场", "check": 0, "leaf": true, "order": 0, "data": {"code": "040107", "value": "停车场", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040109", "name": "桥梁", "check": 0, "leaf": true, "order": 0, "data": {"code": "040109", "value": "桥梁", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040104", "name": "城市轨道交通", "check": 0, "leaf": true, "order": 0, "data": {"code": "040104", "value": "城市轨道交通", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040108", "name": "隧道", "check": 0, "leaf": true, "order": 0, "data": {"code": "040108", "value": "隧道", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040102", "name": "市政道路", "check": 0, "leaf": true, "order": 0, "data": {"code": "040102", "value": "市政道路", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040103", "name": "铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "040103", "value": "铁路", "parentId": "0401", "status": 1}, "children": [], "selectable": true}, {"parentId": "0401", "id": "040199", "name": "其他交通运输工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040199", "value": "其他交通运输工程", "parentId": "0401", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0403", "name": "石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0403", "value": "石油化工工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0403", "id": "040399", "name": "其他石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040399", "value": "其他石油化工工程", "parentId": "0403", "status": 1}, "children": [], "selectable": true}, {"parentId": "0403", "id": "040301", "name": "炼油及石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040301", "value": "炼油及石油化工工程", "parentId": "0403", "status": 1}, "children": [], "selectable": true}, {"parentId": "0403", "id": "040302", "name": "输油管线及泵房", "check": 0, "leaf": true, "order": 0, "data": {"code": "040302", "value": "输油管线及泵房", "parentId": "0403", "status": 1}, "children": [], "selectable": true}, {"parentId": "0403", "id": "040303", "name": "长输燃气管道", "check": 0, "leaf": true, "order": 0, "data": {"code": "040303", "value": "长输燃气管道", "parentId": "0403", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0408", "name": "水利、水运工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0408", "value": "水利、水运工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0408", "id": "040801", "name": "引水工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040801", "value": "引水工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040812", "name": "填海造地、人工岛", "check": 0, "leaf": true, "order": 0, "data": {"code": "040812", "value": "填海造地、人工岛", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040802", "name": "水库", "check": 0, "leaf": true, "order": 0, "data": {"code": "040802", "value": "水库", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040804", "name": "灌溉排水", "check": 0, "leaf": true, "order": 0, "data": {"code": "040804", "value": "灌溉排水", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040805", "name": "船闸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040805", "value": "船闸工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040899", "name": "其他水利、水运工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040899", "value": "其他水利、水运工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040803", "name": "水利枢纽", "check": 0, "leaf": true, "order": 0, "data": {"code": "040803", "value": "水利枢纽", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040806", "name": "码头与岸壁工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040806", "value": "码头与岸壁工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040811", "name": "道路与堆场工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040811", "value": "道路与堆场工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040808", "name": "疏浚与吹填工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040808", "value": "疏浚与吹填工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040809", "name": "船坞与船台滑道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040809", "value": "船坞与船台滑道工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040807", "name": "防波堤与护岸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040807", "value": "防波堤与护岸工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}, {"parentId": "0408", "id": "040810", "name": "航道与航标工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040810", "value": "航道与航标工程", "parentId": "0408", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0405", "name": "环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0405", "value": "环保工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0405", "id": "040504", "name": "污水处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "040504", "value": "污水处理", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040505", "name": "景观、绿地与环境再造", "check": 0, "leaf": true, "order": 0, "data": {"code": "040505", "value": "景观、绿地与环境再造", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040502", "name": "工业废物处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "040502", "value": "工业废物处理", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040503", "name": "建筑业垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "040503", "value": "建筑业垃圾处理", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040599", "name": "其他环保工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "040599", "value": "其他环保工程", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040501", "name": "民用垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "040501", "value": "民用垃圾处理", "parentId": "0405", "status": 1}, "children": [], "selectable": true}, {"parentId": "0405", "id": "040506", "name": "防磁、防光、防辐射、防噪音", "check": 0, "leaf": true, "order": 0, "data": {"code": "040506", "value": "防磁、防光、防辐射、防噪音", "parentId": "0405", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "04", "id": "0499", "name": "其他工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0499", "value": "其他工程", "parentId": "04", "status": 1}, "children": [{"parentId": "0499", "id": "049904", "name": "地下综合管廊、管网", "check": 0, "leaf": true, "order": 0, "data": {"code": "049904", "value": "地下综合管廊、管网", "parentId": "0499", "status": 1}, "children": [], "selectable": true}, {"parentId": "0499", "id": "049905", "name": "海绵城市", "check": 0, "leaf": true, "order": 0, "data": {"code": "049905", "value": "海绵城市", "parentId": "0499", "status": 1}, "children": [], "selectable": true}, {"parentId": "0499", "id": "049901", "name": "室外体育场", "check": 0, "leaf": true, "order": 0, "data": {"code": "049901", "value": "室外体育场", "parentId": "0499", "status": 1}, "children": [], "selectable": true}, {"parentId": "0499", "id": "049999", "name": "其他工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "049999", "value": "其他工程", "parentId": "0499", "status": 1}, "children": [], "selectable": true}, {"parentId": "0499", "id": "049902", "name": "室外娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "049902", "value": "室外娱乐设施", "parentId": "0499", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "05", "name": "制造业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "05", "value": "制造业务", "parentId": null, "status": 1}, "children": [{"parentId": "05", "id": "0503", "name": "幕墙制造", "check": 0, "leaf": false, "order": 0, "data": {"code": "0503", "value": "幕墙制造", "parentId": "05", "status": 1}, "children": [{"parentId": "0503", "id": "050301", "name": "房屋建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050301", "value": "房屋建设项目", "parentId": "0503", "status": 1}, "children": [], "selectable": true}, {"parentId": "0503", "id": "050302", "name": "基础设施建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050302", "value": "基础设施建设项目", "parentId": "0503", "status": 1}, "children": [], "selectable": true}, {"parentId": "0503", "id": "050399", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050399", "value": "其他项目", "parentId": "0503", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "05", "id": "0505", "name": "门窗及金属制品", "check": 0, "leaf": true, "order": 0, "data": {"code": "0505", "value": "门窗及金属制品", "parentId": "05", "status": 1}, "children": [], "selectable": true}, {"parentId": "05", "id": "0599", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "0599", "value": "其他", "parentId": "05", "status": 1}, "children": [], "selectable": true}, {"parentId": "05", "id": "0504", "name": "预制构件制造", "check": 0, "leaf": true, "order": 0, "data": {"code": "0504", "value": "预制构件制造", "parentId": "05", "status": 1}, "children": [], "selectable": true}, {"parentId": "05", "id": "0502", "name": "钢构制造", "check": 0, "leaf": false, "order": 0, "data": {"code": "0502", "value": "钢构制造", "parentId": "05", "status": 1}, "children": [{"parentId": "0502", "id": "050202", "name": "基础设施建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050202", "value": "基础设施建设项目", "parentId": "0502", "status": 1}, "children": [], "selectable": true}, {"parentId": "0502", "id": "050299", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050299", "value": "其他项目", "parentId": "0502", "status": 1}, "children": [], "selectable": true}, {"parentId": "0502", "id": "050201", "name": "房屋建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050201", "value": "房屋建设项目", "parentId": "0502", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "05", "id": "0501", "name": "商品混凝土", "check": 0, "leaf": false, "order": 0, "data": {"code": "0501", "value": "商品混凝土", "parentId": "05", "status": 1}, "children": [{"parentId": "0501", "id": "050199", "name": "其他项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050199", "value": "其他项目", "parentId": "0501", "status": 1}, "children": [], "selectable": true}, {"parentId": "0501", "id": "050102", "name": "基础设施建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050102", "value": "基础设施建设项目", "parentId": "0501", "status": 1}, "children": [], "selectable": true}, {"parentId": "0501", "id": "050101", "name": "房屋建设项目", "check": 0, "leaf": true, "order": 0, "data": {"code": "050101", "value": "房屋建设项目", "parentId": "0501", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "06", "name": "基础设施投资业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "06", "value": "基础设施投资业务", "parentId": null, "status": 1}, "children": [{"parentId": "06", "id": "0601", "name": "交通运输工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0601", "value": "交通运输工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0601", "id": "060101", "name": "公路", "check": 0, "leaf": true, "order": 0, "data": {"code": "060101", "value": "公路", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060104", "name": "城市轨道交通", "check": 0, "leaf": true, "order": 0, "data": {"code": "060104", "value": "城市轨道交通", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060108", "name": "隧道", "check": 0, "leaf": true, "order": 0, "data": {"code": "060108", "value": "隧道", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060199", "name": "其他交通运输工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060199", "value": "其他交通运输工程", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060102", "name": "市政道路", "check": 0, "leaf": true, "order": 0, "data": {"code": "060102", "value": "市政道路", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060103", "name": "铁路", "check": 0, "leaf": true, "order": 0, "data": {"code": "060103", "value": "铁路", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060107", "name": "停车场", "check": 0, "leaf": true, "order": 0, "data": {"code": "060107", "value": "停车场", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060109", "name": "桥梁", "check": 0, "leaf": true, "order": 0, "data": {"code": "060109", "value": "桥梁", "parentId": "0601", "status": 1}, "children": [], "selectable": true}, {"parentId": "0601", "id": "060105", "name": "机场", "check": 0, "leaf": true, "order": 0, "data": {"code": "060105", "value": "机场", "parentId": "0601", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0602", "name": "能源工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0602", "value": "能源工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0602", "id": "060299", "name": "其他能源工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060299", "value": "其他能源工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060201", "name": "变电站及输电线路工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060201", "value": "变电站及输电线路工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060208", "name": "燃气供应工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060208", "value": "燃气供应工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060210", "name": "矿山工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060210", "value": "矿山工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060205", "name": "风电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060205", "value": "风电工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060206", "name": "煤炭化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060206", "value": "煤炭化工工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060209", "name": "太阳能工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060209", "value": "太阳能工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060203", "name": "水电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060203", "value": "水电工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060207", "name": "热力工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060207", "value": "热力工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060202", "name": "核电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060202", "value": "核电工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}, {"parentId": "0602", "id": "060204", "name": "火电工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060204", "value": "火电工程", "parentId": "0602", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0605", "name": "环保工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0605", "value": "环保工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0605", "id": "060506", "name": "防磁、防光、防辐射、防噪音", "check": 0, "leaf": true, "order": 0, "data": {"code": "060506", "value": "防磁、防光、防辐射、防噪音", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060599", "name": "其他环保工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060599", "value": "其他环保工程", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060505", "name": "景观、绿地与环境再造", "check": 0, "leaf": true, "order": 0, "data": {"code": "060505", "value": "景观、绿地与环境再造", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060501", "name": "民用垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "060501", "value": "民用垃圾处理", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060502", "name": "工业废物处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "060502", "value": "工业废物处理", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060503", "name": "建筑业垃圾处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "060503", "value": "建筑业垃圾处理", "parentId": "0605", "status": 1}, "children": [], "selectable": true}, {"parentId": "0605", "id": "060504", "name": "污水处理", "check": 0, "leaf": true, "order": 0, "data": {"code": "060504", "value": "污水处理", "parentId": "0605", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0607", "name": "防卫防灾工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0607", "value": "防卫防灾工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0607", "id": "060703", "name": "防空设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "060703", "value": "防空设施", "parentId": "0607", "status": 1}, "children": [], "selectable": true}, {"parentId": "0607", "id": "060705", "name": "排雨工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060705", "value": "排雨工程", "parentId": "0607", "status": 1}, "children": [], "selectable": true}, {"parentId": "0607", "id": "060701", "name": "堤坝工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060701", "value": "堤坝工程", "parentId": "0607", "status": 1}, "children": [], "selectable": true}, {"parentId": "0607", "id": "060704", "name": "消防设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "060704", "value": "消防设施", "parentId": "0607", "status": 1}, "children": [], "selectable": true}, {"parentId": "0607", "id": "060702", "name": "山洪防御工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060702", "value": "山洪防御工程", "parentId": "0607", "status": 1}, "children": [], "selectable": true}, {"parentId": "0607", "id": "060799", "name": "其他防卫防灾工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060799", "value": "其他防卫防灾工程", "parentId": "0607", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0608", "name": "水利、水运工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0608", "value": "水利、水运工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0608", "id": "060803", "name": "水利枢纽", "check": 0, "leaf": true, "order": 0, "data": {"code": "060803", "value": "水利枢纽", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060802", "name": "水库", "check": 0, "leaf": true, "order": 0, "data": {"code": "060802", "value": "水库", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060805", "name": "船闸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060805", "value": "船闸工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060806", "name": "码头与岸壁工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060806", "value": "码头与岸壁工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060899", "name": "其他水利、水运工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060899", "value": "其他水利、水运工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060801", "name": "引水工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060801", "value": "引水工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060808", "name": "疏浚与吹填工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060808", "value": "疏浚与吹填工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060809", "name": "船坞与船台滑道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060809", "value": "船坞与船台滑道工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060811", "name": "道路与堆场工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060811", "value": "道路与堆场工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060807", "name": "防波堤与护岸工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060807", "value": "防波堤与护岸工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060810", "name": "航道与航标工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060810", "value": "航道与航标工程", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060812", "name": "填海造地、人工岛", "check": 0, "leaf": true, "order": 0, "data": {"code": "060812", "value": "填海造地、人工岛", "parentId": "0608", "status": 1}, "children": [], "selectable": true}, {"parentId": "0608", "id": "060804", "name": "灌溉排水", "check": 0, "leaf": true, "order": 0, "data": {"code": "060804", "value": "灌溉排水", "parentId": "0608", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0604", "name": "供水及处理工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0604", "value": "供水及处理工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0604", "id": "060403", "name": "排污、排洪管道工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060403", "value": "排污、排洪管道工程", "parentId": "0604", "status": 1}, "children": [], "selectable": true}, {"parentId": "0604", "id": "060401", "name": "输、供水工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "060401", "value": "输、供水工程（含管道及附属设备）", "parentId": "0604", "status": 1}, "children": [], "selectable": true}, {"parentId": "0604", "id": "060402", "name": "水处理工程（含管道及附属设备）", "check": 0, "leaf": true, "order": 0, "data": {"code": "060402", "value": "水处理工程（含管道及附属设备）", "parentId": "0604", "status": 1}, "children": [], "selectable": true}, {"parentId": "0604", "id": "060499", "name": "其他供水及处理工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060499", "value": "其他供水及处理工程", "parentId": "0604", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0606", "name": "邮电通讯工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0606", "value": "邮电通讯工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0606", "id": "060603", "name": "通信线路", "check": 0, "leaf": true, "order": 0, "data": {"code": "060603", "value": "通信线路", "parentId": "0606", "status": 1}, "children": [], "selectable": true}, {"parentId": "0606", "id": "060602", "name": "发射塔", "check": 0, "leaf": true, "order": 0, "data": {"code": "060602", "value": "发射塔", "parentId": "0606", "status": 1}, "children": [], "selectable": true}, {"parentId": "0606", "id": "060601", "name": "基站", "check": 0, "leaf": true, "order": 0, "data": {"code": "060601", "value": "基站", "parentId": "0606", "status": 1}, "children": [], "selectable": true}, {"parentId": "0606", "id": "060699", "name": "其他邮电通讯工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060699", "value": "其他邮电通讯工程", "parentId": "0606", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0699", "name": "其他工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0699", "value": "其他工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0699", "id": "069999", "name": "其他工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "069999", "value": "其他工程", "parentId": "0699", "status": 1}, "children": [], "selectable": true}, {"parentId": "0699", "id": "069901", "name": "室外体育场", "check": 0, "leaf": true, "order": 0, "data": {"code": "069901", "value": "室外体育场", "parentId": "0699", "status": 1}, "children": [], "selectable": true}, {"parentId": "0699", "id": "069902", "name": "室外娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "069902", "value": "室外娱乐设施", "parentId": "0699", "status": 1}, "children": [], "selectable": true}, {"parentId": "0699", "id": "069905", "name": "海绵城市", "check": 0, "leaf": true, "order": 0, "data": {"code": "069905", "value": "海绵城市", "parentId": "0699", "status": 1}, "children": [], "selectable": true}, {"parentId": "0699", "id": "069904", "name": "地下综合管廊、管网", "check": 0, "leaf": true, "order": 0, "data": {"code": "069904", "value": "地下综合管廊、管网", "parentId": "0699", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "06", "id": "0603", "name": "石油化工工程", "check": 0, "leaf": false, "order": 0, "data": {"code": "0603", "value": "石油化工工程", "parentId": "06", "status": 1}, "children": [{"parentId": "0603", "id": "060301", "name": "炼油及石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060301", "value": "炼油及石油化工工程", "parentId": "0603", "status": 1}, "children": [], "selectable": true}, {"parentId": "0603", "id": "060303", "name": "长输燃气管道", "check": 0, "leaf": true, "order": 0, "data": {"code": "060303", "value": "长输燃气管道", "parentId": "0603", "status": 1}, "children": [], "selectable": true}, {"parentId": "0603", "id": "060302", "name": "输油管线及泵房", "check": 0, "leaf": true, "order": 0, "data": {"code": "060302", "value": "输油管线及泵房", "parentId": "0603", "status": 1}, "children": [], "selectable": true}, {"parentId": "0603", "id": "060399", "name": "其他石油化工工程", "check": 0, "leaf": true, "order": 0, "data": {"code": "060399", "value": "其他石油化工工程", "parentId": "0603", "status": 1}, "children": [], "selectable": true}], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "01", "name": "设计勘察业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "01", "value": "设计勘察业务", "parentId": null, "status": 1}, "children": [{"parentId": "01", "id": "0110", "name": "测量", "check": 0, "leaf": true, "order": 0, "data": {"code": "0110", "value": "测量", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0111", "name": "工程咨询", "check": 0, "leaf": true, "order": 0, "data": {"code": "0111", "value": "工程咨询", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0101", "name": "民用建筑设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0101", "value": "民用建筑设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0106", "name": "桥梁设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0106", "value": "桥梁设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0107", "name": "装饰、园林设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0107", "value": "装饰、园林设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0108", "name": "勘察", "check": 0, "leaf": true, "order": 0, "data": {"code": "0108", "value": "勘察", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0199", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "0199", "value": "其他", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0104", "name": "公路设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0104", "value": "公路设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0102", "name": "工业建筑设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0102", "value": "工业建筑设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0105", "name": "铁路设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0105", "value": "铁路设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}, {"parentId": "01", "id": "0103", "name": "市政建筑设计", "check": 0, "leaf": true, "order": 0, "data": {"code": "0103", "value": "市政建筑设计", "parentId": "01", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "07", "name": "房屋建筑工程投资业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "07", "value": "房屋建筑工程投资业务", "parentId": null, "status": 1}, "children": [{"parentId": "07", "id": "0701", "name": "住宅（含别墅、公寓）", "check": 0, "leaf": true, "order": 0, "data": {"code": "0701", "value": "住宅（含别墅、公寓）", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0703", "name": "商用写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "0703", "value": "商用写字楼", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0799", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "0799", "value": "其他", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0707", "name": "教育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0707", "value": "教育设施", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0711", "name": "医疗建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0711", "value": "医疗建筑", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0714", "name": "工业加工厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0714", "value": "工业加工厂房", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0709", "name": "娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0709", "value": "娱乐设施", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0713", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "0713", "value": "城市综合体", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0719", "name": "市政配套建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0719", "value": "市政配套建筑", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0708", "name": "体育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0708", "value": "体育设施", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0715", "name": "工业制造厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0715", "value": "工业制造厂房", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0717", "name": "仓储物流", "check": 0, "leaf": true, "order": 0, "data": {"code": "0717", "value": "仓储物流", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0702", "name": "保障性住房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0702", "value": "保障性住房", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0716", "name": "会议会展中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "0716", "value": "会议会展中心", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0704", "name": "商厦", "check": 0, "leaf": true, "order": 0, "data": {"code": "0704", "value": "商厦", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0705", "name": "政府办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "0705", "value": "政府办公楼", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0706", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0706", "value": "文化设施", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0710", "name": "福利设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0710", "value": "福利设施", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0712", "name": "酒店度假建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0712", "value": "酒店度假建筑", "parentId": "07", "status": 1}, "children": [], "selectable": true}, {"parentId": "07", "id": "0718", "name": "宗教建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0718", "value": "宗教建筑", "parentId": "07", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "99", "name": "其他业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "99", "value": "其他业务", "parentId": null, "status": 1}, "children": [{"parentId": "99", "id": "9905", "name": "住宿和餐饮业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9905", "value": "住宿和餐饮业", "parentId": "99", "status": 1}, "children": [{"parentId": "9905", "id": "990501", "name": "住宿业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990501", "value": "住宿业", "parentId": "9905", "status": 1}, "children": [], "selectable": true}, {"parentId": "9905", "id": "990502", "name": "餐饮业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990502", "value": "餐饮业", "parentId": "9905", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9912", "name": "专业技术服务业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9912", "value": "专业技术服务业", "parentId": "99", "status": 1}, "children": [{"parentId": "9912", "id": "991205", "name": "工程技术与设计服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991205", "value": "工程技术与设计服务", "parentId": "9912", "status": 1}, "children": [], "selectable": true}, {"parentId": "9912", "id": "991203", "name": "环境与生态监测检测服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991203", "value": "环境与生态监测检测服务", "parentId": "9912", "status": 1}, "children": [], "selectable": true}, {"parentId": "9912", "id": "991201", "name": "测绘地理信息服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991201", "value": "测绘地理信息服务", "parentId": "9912", "status": 1}, "children": [], "selectable": true}, {"parentId": "9912", "id": "991202", "name": "质检技术服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991202", "value": "质检技术服务", "parentId": "9912", "status": 1}, "children": [], "selectable": true}, {"parentId": "9912", "id": "991204", "name": "地质勘查", "check": 0, "leaf": true, "order": 0, "data": {"code": "991204", "value": "地质勘查", "parentId": "9912", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9914", "name": "农林牧渔业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9914", "value": "农林牧渔业", "parentId": "99", "status": 1}, "children": [{"parentId": "9914", "id": "991401", "name": "农业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991401", "value": "农业", "parentId": "9914", "status": 1}, "children": [], "selectable": true}, {"parentId": "9914", "id": "991404", "name": "渔业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991404", "value": "渔业", "parentId": "9914", "status": 1}, "children": [], "selectable": true}, {"parentId": "9914", "id": "991403", "name": "牧业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991403", "value": "牧业", "parentId": "9914", "status": 1}, "children": [], "selectable": true}, {"parentId": "9914", "id": "991402", "name": "林业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991402", "value": "林业", "parentId": "9914", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9911", "name": "商务服务业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9911", "value": "商务服务业", "parentId": "99", "status": 1}, "children": [{"parentId": "9911", "id": "991104", "name": "咨询与调查", "check": 0, "leaf": true, "order": 0, "data": {"code": "991104", "value": "咨询与调查", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991105", "name": "广告业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991105", "value": "广告业", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991103", "name": "法律服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991103", "value": "法律服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991108", "name": "会议、展览及相关服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991108", "value": "会议、展览及相关服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991199", "name": "其他商务服务业", "check": 0, "leaf": true, "order": 0, "data": {"code": "991199", "value": "其他商务服务业", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991102", "name": "综合管理服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991102", "value": "综合管理服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991106", "name": "人力资源服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991106", "value": "人力资源服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991107", "name": "安全保护服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991107", "value": "安全保护服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}, {"parentId": "9911", "id": "991101", "name": "组织管理服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "991101", "value": "组织管理服务", "parentId": "9911", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9999", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "9999", "value": "其他", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9901", "name": "金融", "check": 0, "leaf": false, "order": 0, "data": {"code": "9901", "value": "金融", "parentId": "99", "status": 1}, "children": [{"parentId": "9901", "id": "990101", "name": "保险业务", "check": 0, "leaf": true, "order": 0, "data": {"code": "990101", "value": "保险业务", "parentId": "9901", "status": 1}, "children": [], "selectable": true}, {"parentId": "9901", "id": "990102", "name": "货币金融服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "990102", "value": "货币金融服务", "parentId": "9901", "status": 1}, "children": [], "selectable": true}, {"parentId": "9901", "id": "990109", "name": "其他金融业务", "check": 0, "leaf": true, "order": 0, "data": {"code": "990109", "value": "其他金融业务", "parentId": "9901", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9903", "name": "电力、热力生产和供应业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "9903", "value": "电力、热力生产和供应业务", "parentId": "99", "status": 1}, "children": [{"parentId": "9903", "id": "990301", "name": "热力生产和供应（热电厂）", "check": 0, "leaf": true, "order": 0, "data": {"code": "990301", "value": "热力生产和供应（热电厂）", "parentId": "9903", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9907", "name": "文化、体育和娱乐业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9907", "value": "文化、体育和娱乐业", "parentId": "99", "status": 1}, "children": [{"parentId": "9907", "id": "990701", "name": "新闻和出版业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990701", "value": "新闻和出版业", "parentId": "9907", "status": 1}, "children": [], "selectable": true}, {"parentId": "9907", "id": "990704", "name": "体育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990704", "value": "体育", "parentId": "9907", "status": 1}, "children": [], "selectable": true}, {"parentId": "9907", "id": "990702", "name": "广播、电视、电影和录音制作业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990702", "value": "广播、电视、电影和录音制作业", "parentId": "9907", "status": 1}, "children": [], "selectable": true}, {"parentId": "9907", "id": "990703", "name": "文化艺术业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990703", "value": "文化艺术业", "parentId": "9907", "status": 1}, "children": [], "selectable": true}, {"parentId": "9907", "id": "990705", "name": "娱乐业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990705", "value": "娱乐业", "parentId": "9907", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9910", "name": "租赁业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9910", "value": "租赁业", "parentId": "99", "status": 1}, "children": [{"parentId": "9910", "id": "991001", "name": "房屋、厢式板房租赁", "check": 0, "leaf": true, "order": 0, "data": {"code": "991001", "value": "房屋、厢式板房租赁", "parentId": "9910", "status": 1}, "children": [], "selectable": true}, {"parentId": "9910", "id": "991003", "name": "材料物资经营租赁", "check": 0, "leaf": true, "order": 0, "data": {"code": "991003", "value": "材料物资经营租赁", "parentId": "9910", "status": 1}, "children": [], "selectable": true}, {"parentId": "9910", "id": "991002", "name": "机械设备经营租赁", "check": 0, "leaf": true, "order": 0, "data": {"code": "991002", "value": "机械设备经营租赁", "parentId": "9910", "status": 1}, "children": [], "selectable": true}, {"parentId": "9910", "id": "991099", "name": "其他经营租赁", "check": 0, "leaf": true, "order": 0, "data": {"code": "991099", "value": "其他经营租赁", "parentId": "9910", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9915", "name": "城镇综合建设", "check": 0, "leaf": false, "order": 0, "data": {"code": "9915", "value": "城镇综合建设", "parentId": "99", "status": 1}, "children": [{"parentId": "9915", "id": "991502", "name": "棚户区改造", "check": 0, "leaf": true, "order": 0, "data": {"code": "991502", "value": "棚户区改造", "parentId": "9915", "status": 1}, "children": [], "selectable": true}, {"parentId": "9915", "id": "991599", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "991599", "value": "其他", "parentId": "9915", "status": 1}, "children": [], "selectable": true}, {"parentId": "9915", "id": "991501", "name": "一级开发", "check": 0, "leaf": true, "order": 0, "data": {"code": "991501", "value": "一级开发", "parentId": "9915", "status": 1}, "children": [], "selectable": true}, {"parentId": "9915", "id": "991503", "name": "特色小镇", "check": 0, "leaf": true, "order": 0, "data": {"code": "991503", "value": "特色小镇", "parentId": "9915", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9913", "name": "商品零售业", "check": 0, "leaf": true, "order": 0, "data": {"code": "9913", "value": "商品零售业", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9916", "name": "工程监理", "check": 0, "leaf": true, "order": 0, "data": {"code": "9916", "value": "工程监理", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9902", "name": "交通运输、仓储和邮政业", "check": 0, "leaf": false, "order": 0, "data": {"code": "9902", "value": "交通运输、仓储和邮政业", "parentId": "99", "status": 1}, "children": [{"parentId": "9902", "id": "990205", "name": "管道运输业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990205", "value": "管道运输业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}, {"parentId": "9902", "id": "990204", "name": "航空运输业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990204", "value": "航空运输业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}, {"parentId": "9902", "id": "990206", "name": "多式联运和运输代理业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990206", "value": "多式联运和运输代理业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}, {"parentId": "9902", "id": "990201", "name": "铁路运输业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990201", "value": "铁路运输业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}, {"parentId": "9902", "id": "990202", "name": "道路运输业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990202", "value": "道路运输业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}, {"parentId": "9902", "id": "990203", "name": "水上运输业", "check": 0, "leaf": true, "order": 0, "data": {"code": "990203", "value": "水上运输业", "parentId": "9902", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9906", "name": "卫生和社会工作", "check": 0, "leaf": false, "order": 0, "data": {"code": "9906", "value": "卫生和社会工作", "parentId": "99", "status": 1}, "children": [{"parentId": "9906", "id": "990603", "name": "专业公共卫生服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "990603", "value": "专业公共卫生服务", "parentId": "9906", "status": 1}, "children": [], "selectable": true}, {"parentId": "9906", "id": "990601", "name": "医院", "check": 0, "leaf": true, "order": 0, "data": {"code": "990601", "value": "医院", "parentId": "9906", "status": 1}, "children": [], "selectable": true}, {"parentId": "9906", "id": "990604", "name": "其他卫生活动", "check": 0, "leaf": true, "order": 0, "data": {"code": "990604", "value": "其他卫生活动", "parentId": "9906", "status": 1}, "children": [], "selectable": true}, {"parentId": "9906", "id": "990602", "name": "基层医疗卫生服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "990602", "value": "基层医疗卫生服务", "parentId": "9906", "status": 1}, "children": [], "selectable": true}, {"parentId": "9906", "id": "990605", "name": "社会工作", "check": 0, "leaf": true, "order": 0, "data": {"code": "990605", "value": "社会工作", "parentId": "9906", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9909", "name": "电子商务", "check": 0, "leaf": true, "order": 0, "data": {"code": "9909", "value": "电子商务", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9918", "name": "工程咨询", "check": 0, "leaf": true, "order": 0, "data": {"code": "9918", "value": "工程咨询", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9919", "name": "检测服务", "check": 0, "leaf": true, "order": 0, "data": {"code": "9919", "value": "检测服务", "parentId": "99", "status": 1}, "children": [], "selectable": true}, {"parentId": "99", "id": "9904", "name": "教育业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "9904", "value": "教育业务", "parentId": "99", "status": 1}, "children": [{"parentId": "9904", "id": "990404", "name": "高等教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990404", "value": "高等教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}, {"parentId": "9904", "id": "990406", "name": "技能培训、教育辅助及其他教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990406", "value": "技能培训、教育辅助及其他教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}, {"parentId": "9904", "id": "990401", "name": "学前教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990401", "value": "学前教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}, {"parentId": "9904", "id": "990403", "name": "中等教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990403", "value": "中等教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}, {"parentId": "9904", "id": "990405", "name": "特殊教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990405", "value": "特殊教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}, {"parentId": "9904", "id": "990402", "name": "初等教育", "check": 0, "leaf": true, "order": 0, "data": {"code": "990402", "value": "初等教育", "parentId": "9904", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9908", "name": "贸易经纪与代理业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "9908", "value": "贸易经纪与代理业务", "parentId": "99", "status": 1}, "children": [{"parentId": "9908", "id": "990801", "name": "进出口贸易代理", "check": 0, "leaf": true, "order": 0, "data": {"code": "990801", "value": "进出口贸易代理", "parentId": "9908", "status": 1}, "children": [], "selectable": true}, {"parentId": "9908", "id": "990899", "name": "其他贸易经纪与代理", "check": 0, "leaf": true, "order": 0, "data": {"code": "990899", "value": "其他贸易经纪与代理", "parentId": "9908", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "99", "id": "9917", "name": "工程测量", "check": 0, "leaf": true, "order": 0, "data": {"code": "9917", "value": "工程测量", "parentId": "99", "status": 1}, "children": [], "selectable": true}], "selectable": true}, {"parentId": "root", "id": "02", "name": "房屋建筑工程建设业务", "check": 0, "leaf": false, "order": 0, "data": {"code": "02", "value": "房屋建筑工程建设业务", "parentId": null, "status": 1}, "children": [{"parentId": "02", "id": "0207", "name": "教育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0207", "value": "教育设施", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0212", "name": "酒店度假建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0212", "value": "酒店度假建筑", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0203", "name": "商用写字楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "0203", "value": "商用写字楼", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0208", "name": "体育设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0208", "value": "体育设施", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0213", "name": "城市综合体", "check": 0, "leaf": true, "order": 0, "data": {"code": "0213", "value": "城市综合体", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0218", "name": "宗教建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0218", "value": "宗教建筑", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0201", "name": "住宅（含别墅、公寓）", "check": 0, "leaf": true, "order": 0, "data": {"code": "0201", "value": "住宅（含别墅、公寓）", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0202", "name": "保障性住房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0202", "value": "保障性住房", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0204", "name": "商厦", "check": 0, "leaf": true, "order": 0, "data": {"code": "0204", "value": "商厦", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0206", "name": "文化设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0206", "value": "文化设施", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0209", "name": "娱乐设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0209", "value": "娱乐设施", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0217", "name": "仓储物流", "check": 0, "leaf": true, "order": 0, "data": {"code": "0217", "value": "仓储物流", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0205", "name": "政府办公楼", "check": 0, "leaf": true, "order": 0, "data": {"code": "0205", "value": "政府办公楼", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0214", "name": "工业加工厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0214", "value": "工业加工厂房", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0219", "name": "市政配套建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0219", "value": "市政配套建筑", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0299", "name": "其他", "check": 0, "leaf": true, "order": 0, "data": {"code": "0299", "value": "其他", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0210", "name": "福利设施", "check": 0, "leaf": true, "order": 0, "data": {"code": "0210", "value": "福利设施", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0211", "name": "医疗建筑", "check": 0, "leaf": true, "order": 0, "data": {"code": "0211", "value": "医疗建筑", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0215", "name": "工业制造厂房", "check": 0, "leaf": true, "order": 0, "data": {"code": "0215", "value": "工业制造厂房", "parentId": "02", "status": 1}, "children": [], "selectable": true}, {"parentId": "02", "id": "0216", "name": "会议会展中心", "check": 0, "leaf": true, "order": 0, "data": {"code": "0216", "value": "会议会展中心", "parentId": "02", "status": 1}, "children": [], "selectable": true}], "selectable": true}]}